"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosterController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../users/jwt/jwt-auth.guard");
const create_poster_dto_1 = require("./dto/create-poster.dto");
const poster_service_1 = require("./poster.service");
let PosterController = class PosterController {
    constructor(service) {
        this.service = service;
    }
    getEvents(request, query) {
        const { userId } = request.user;
        return this.service.getPosterConfig(userId, query);
    }
    async updateImage(req, body) {
        const { userId } = req.user;
        return this.service.updateImage(userId, body);
    }
    createPoster(body, request) {
        const { userId } = request.user;
        return this.service.create(userId, body);
    }
    getPosterEventTypes() {
        return this.service.getPosterEventTypes();
    }
    getPosterEventsByType(typeName) {
        return this.service.getPosterEventsByType(typeName);
    }
    async createTemplate1(body, request) {
        const { userId } = request.user;
        const result = await this.service.createTemplate1(userId, body);
        return { template1: result };
    }
    async createTemplate2(body, request) {
        const { userId } = request.user;
        const result = await this.service.createTemplate2(userId, body);
        return { template2: result };
    }
    async createTemplate3(body, request) {
        const { userId } = request.user;
        const result = await this.service.createTemplate3(userId, body);
        return { template3: result };
    }
    getPosters(query) {
        return this.service.getPosters(query);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/poster-config'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], PosterController.prototype, "getEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('/logo'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PosterController.prototype, "updateImage", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/config'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_poster_dto_1.CreatePosterDto, Object]),
    __metadata("design:returntype", void 0)
], PosterController.prototype, "createPoster", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/poster-event-types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PosterController.prototype, "getPosterEventTypes", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/poster-events'),
    __param(0, (0, common_1.Query)('typeName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PosterController.prototype, "getPosterEventsByType", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/poster-preview/template1'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PosterController.prototype, "createTemplate1", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/poster-preview/template2'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PosterController.prototype, "createTemplate2", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/poster-preview/template3'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PosterController.prototype, "createTemplate3", null);
__decorate([
    (0, common_1.Get)('/posters'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PosterController.prototype, "getPosters", null);
PosterController = __decorate([
    (0, common_1.Controller)('poster'),
    __metadata("design:paramtypes", [poster_service_1.PosterService])
], PosterController);
exports.PosterController = PosterController;
//# sourceMappingURL=poster.controller.js.map