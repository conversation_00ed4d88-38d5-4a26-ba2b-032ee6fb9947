{"version": 3, "file": "tan-utils.js", "sourceRoot": "", "sources": ["../../../../src/modules/tan-automation/tan-utils.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,iBAAiB;IAC7B,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC/C,OAAO,mBAAmB,CAAC,YAAY,CAAC,CAAC;AAC3C,CAAC;AAHH,8CAGG;AAEF,SAAgB,mBAAmB,CAAC,KAAa;IAC9C,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,GAAG,CAAC;IACzC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,GAAG,CAAC;IACzC,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,CAAC;IAC3C,OAAO,GAAG,CAAC;AACb,CAAC;AALF,kDAKE;AAEY,QAAA,YAAY,GAAG,CAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,CAAC,CAAA;AAE1E,SAAgB,mBAAmB,CAAC,KAAY;IAC9C,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAC1C,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAC1C,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,IAAI,CAAC;IAC5C,OAAO,IAAI,CAAC;AACd,CAAC;AALD,kDAKC;AAED,SAAgB,gBAAgB,CAAC,OAAc;IAC7C,IAAG,OAAO,KAAK,IAAI,EAAC;QAClB,OAAO,IAAI,CAAA;KACZ;SAAK,IAAG,OAAO,KAAK,IAAI,EAAC;QACxB,OAAO,IAAI,CAAA;KACZ;SAAK,IAAG,OAAO,KAAK,IAAI,EAAC;QACxB,OAAO,IAAI,CAAA;KACZ;SAAK,IAAG,OAAO,KAAK,IAAI,EAAC;QACxB,OAAO,IAAI,CAAA;KACZ;AACH,CAAC;AAVD,4CAUC;AAIH,SAAgB,kBAAkB,CAAC,SAAiB,EAAE,iBAAyB,EAAE,OAAe,EAAE,eAAuB;IACvH,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,GAAG,SAAS,CAAC;IACrB,IAAI,YAAY,GAAG,iBAAiB,CAAC;IAErC,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,YAAY,IAAI,eAAe,CAAC,EAAE;QAC5E,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,YAAY,EAAE,EAAE,CAAC,CAAC;QACrD,YAAY,EAAE,CAAC;QACf,IAAI,YAAY,GAAG,CAAC,EAAE;YACpB,YAAY,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;SACV;KACJ;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAdD,gDAcC;AAED,SAAgB,mBAAmB,CAAC,IAAU;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAClC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,CAAC,CAAC;IACvC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAAE,OAAO,CAAC,CAAC;IACvC,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,CAAC,CAAC;IACzC,OAAO,CAAC,CAAC;AACX,CAAC;AAND,kDAMC;AAGD,SAAgB,oBAAoB,CAAC,SAAiB,EAAE,aAAqB;IAC3E,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,GAAE,CAAC,CAAC,CAAC,CAAC;IACtF,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;IAEpD,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IACzB,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACxC,MAAM,cAAc,GAAG,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAE,CAAC,CAAC,CAAC,CAAC;IAE1E,MAAM,kBAAkB,GAAG,GAAG,aAAa,QAAQ,CAAC;IACpD,MAAM,gBAAgB,GAAG,GAAG,aAAa,GAAG,CAAC,QAAQ,CAAC;IAEtD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,EAAE;QAEtD,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAC5C;SAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE;QAE3D,OAAO,aAAa,CAAC;KACtB;SAAM,IACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC;QACnD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,EACjD;QAEA,IAAI,aAAa,KAAK,WAAW,EAAE;YAEjC,KAAK,IAAI,OAAO,GAAG,YAAY,EAAE,OAAO,GAAG,cAAc,EAAE,OAAO,EAAE,EAAE;gBACpE,aAAa,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;aACnC;SACF;aAAM;YAEL,KAAK,IAAI,OAAO,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;gBACxD,aAAa,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;aACnC;SACF;KACF;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;AArCD,oDAqCC;AAyED,SAAgB,+BAA+B,CAC7C,eAAqB,EACrB,aAAqB,EACrB,WAAmB,EACnB,mBAA2B;IAG3B,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,MAAM,qBAAqB,GAAG,QAAQ,CAAC,mBAAmB,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5F,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC;IAC9D,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,QAAQ,CAAC,CAAC;IAChE,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,GAAG,WAAW,QAAQ,CAAC,CAAC;IAE1D,IAAI,eAAe,GAAG,gBAAgB,EAAE;QAEtC,OAAO,EAAE,CAAC;KACX;SAAM,IAAI,eAAe,GAAG,kBAAkB,EAAE;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;SAC1D;KACF;SAAM,IAAI,eAAe,IAAI,kBAAkB,IAAI,eAAe,IAAI,gBAAgB,EAAE;QAEvF,MAAM,YAAY,GAAG,qBAAqB,CAAC;QAC3C,MAAM,UAAU,GAAG,aAAa,KAAK,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3E,KAAK,IAAI,CAAC,GAAG,YAAY,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE;YAC/C,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;SAC1D;KACF;SAAM,IACL,qBAAqB,GAAG,mBAAmB;QAC3C,eAAe,IAAI,IAAI,IAAI,CAAC,GAAG,WAAW,QAAQ,CAAC;QACnD,eAAe,GAAG,gBAAgB,EAClC;QAEA,KAAK,IAAI,CAAC,GAAG,qBAAqB,EAAE,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE,EAAE;YAChE,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;SAC1D;KACF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAxCD,0EAwCC"}