import Client from 'src/modules/clients/entity/client.entity';
import { BaseEntity } from 'typeorm';
declare class GstrUpdateTracker extends BaseEntity {
    id: number;
    noticeAndOrder: object;
    additionalNoticeAndOrder: object;
    demands: object;
    ledgers: object;
    isChange: boolean;
    organizationId: number;
    clientId: number;
    client: Client;
    gstrCredentialsId: number;
    createdAt: string;
    updatedAt: string;
}
export default GstrUpdateTracker;
