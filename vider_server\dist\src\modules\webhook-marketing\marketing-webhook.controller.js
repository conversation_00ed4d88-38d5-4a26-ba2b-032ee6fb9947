"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketingWebhookController = void 0;
const common_1 = require("@nestjs/common");
const marketing_webhook_service_1 = require("./marketing-webhook.service");
const vider_marketing_webhook_entity_1 = require("./entity/vider-marketing-webhook.entity");
let MarketingWebhookController = class MarketingWebhookController {
    constructor(webhookService) {
        this.webhookService = webhookService;
    }
    async handleWebhook(req, query) {
        if (query['hub.mode'] == 'subscribe' && query['hub.verify_token'] == ('marketing')) {
            return query['hub.challenge'];
        }
        else {
            return 400;
        }
    }
    async sendMessages(payload) {
        const webhookLogData = new vider_marketing_webhook_entity_1.default();
        webhookLogData.createdTimestamp = new Date();
        webhookLogData.source = 'meta';
        webhookLogData.payload = JSON.stringify(payload);
        webhookLogData.status = 'RECEIVED';
        webhookLogData.destinationApplication = 'Marketing';
        await webhookLogData.save();
        return { success: true };
    }
};
__decorate([
    (0, common_1.Get)('/'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MarketingWebhookController.prototype, "handleWebhook", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MarketingWebhookController.prototype, "sendMessages", null);
MarketingWebhookController = __decorate([
    (0, common_1.Controller)('webhook'),
    __metadata("design:paramtypes", [marketing_webhook_service_1.MarketingWebhookService])
], MarketingWebhookController);
exports.MarketingWebhookController = MarketingWebhookController;
//# sourceMappingURL=marketing-webhook.controller.js.map