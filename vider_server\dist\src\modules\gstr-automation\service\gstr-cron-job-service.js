"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrCronJobService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const cron_activity_entity_1 = require("../../cron-activity/cron-activity.entity");
const moment = require("moment");
const organization_entity_1 = require("../../organization/entities/organization.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
const noticeOrders_entity_1 = require("../entity/noticeOrders.entity");
const newemails_1 = require("../../../emails/newemails");
const ejs = require("ejs");
const puppeteer = require("puppeteer");
const AWS = require("aws-sdk");
const whatsapp_service_1 = require("../../whatsapp/whatsapp.service");
const viderWhatsappSessions_1 = require("../../whatsapp/entity/viderWhatsappSessions");
const permission_1 = require("../../events/permission");
const s3 = new AWS.S3();
const BUCKET_NAME = process.env.AWS_BUCKET_NAME;
let GstrCronJobService = class GstrCronJobService {
    async getAdditionalNoticeOrdersRecords() {
        if (process.env.Cron_Running === 'true') {
            console.log("-------- GST NOTICE CRON EXECUTION STARTED ---------");
            const cronData = new cron_activity_entity_1.default();
            cronData.cronType = 'GSTR NOTICES';
            cronData.cronDate = moment().toDate().toString();
            cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const cornActivityID = await cronData.save();
            const errorList = [];
            try {
                const statusMap = {
                    REPLIED: "Replied",
                    NOT_REPLIED: "Not Replied",
                    NA: "NA"
                };
                const totalOrganization = await organization_entity_1.Organization.createQueryBuilder('organization')
                    .leftJoinAndSelect('organization.users', 'user')
                    .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate", { expirydate: moment().format('YYYY-MM-DD') })
                    .andWhere('user.status = :status', { status: 'active' })
                    .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION })
                    .getMany();
                for (let organization of totalOrganization) {
                    const users = organization === null || organization === void 0 ? void 0 : organization.users;
                    if ((users === null || users === void 0 ? void 0 : users.length) > 0) {
                        for (let user of users) {
                            if (user.status === user_entity_1.UserStatus.DELETED)
                                return;
                            const userData = await user_entity_1.User.findOne({
                                where: { id: user.id },
                                relations: ['organization', 'role'],
                            });
                            const { role } = userData;
                            const ViewAll = role.permissions.some((p) => p.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
                            const ViewAssigned = role.permissions.some((p) => p.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
                            let additionalNoticeOrdersArray = [];
                            let noticeAndOrdersArray = [];
                            if (ViewAll || ViewAssigned) {
                                const today = moment().format('YYYY-MM-DD');
                                const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
                                const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
                                try {
                                    const additionalNoticeOrdersQuery = await gstrAdditionalOrdersAndNotices_entity_1.default.createQueryBuilder('additionalNotice')
                                        .leftJoinAndSelect('additionalNotice.client', 'client')
                                        .where('additionalNotice.organizationId = :organizationId', {
                                        organizationId: organization.id,
                                    })
                                        .andWhere('additionalNotice.caseFolderTypeName != :noticeType', { noticeType: 'REPLIES' })
                                        .andWhere(new typeorm_1.Brackets((qb) => {
                                        qb.where('STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")', { previousSeventhDay, today }).orWhere('STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")', { today, nextSeventhDay });
                                    }))
                                        .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED });
                                    if (!ViewAll) {
                                        additionalNoticeOrdersQuery.andWhere((qb) => {
                                            const subQuery = qb
                                                .subQuery()
                                                .select('1')
                                                .from('client_client_managers_user', 'cm')
                                                .where('cm.client_id = client.id')
                                                .andWhere('cm.user_id = :userId')
                                                .getQuery();
                                            return `EXISTS (${subQuery})`;
                                        }, { userId: userData.id });
                                    }
                                    const additionalNoticeOrders = await additionalNoticeOrdersQuery.getMany();
                                    if ((additionalNoticeOrders === null || additionalNoticeOrders === void 0 ? void 0 : additionalNoticeOrders.length) > 0) {
                                        additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => {
                                            var _a;
                                            return ({
                                                orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                                clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                                issuanceDate: moment(order === null || order === void 0 ? void 0 : order.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                                dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                                    ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                                    : order === null || order === void 0 ? void 0 : order.dueDate,
                                                financialYear: (order === null || order === void 0 ? void 0 : order.fy) || '-',
                                                folder: order === null || order === void 0 ? void 0 : order.caseFolderTypeName,
                                                referenceNumber: order === null || order === void 0 ? void 0 : order.refNum,
                                                type: order === null || order === void 0 ? void 0 : order.caseTypeName,
                                                refStatus: (order === null || order === void 0 ? void 0 : order.refStatus) ? statusMap[order === null || order === void 0 ? void 0 : order.refStatus] : "-"
                                            });
                                        });
                                    }
                                    const noticeOrdersQuery = await noticeOrders_entity_1.default.createQueryBuilder('noticeOrder')
                                        .leftJoinAndSelect('noticeOrder.client', 'client')
                                        .where('noticeOrder.organizationId = :organizationId', {
                                        organizationId: organization.id,
                                    })
                                        .andWhere(new typeorm_1.Brackets((qb) => {
                                        qb.where('STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")', { previousSeventhDay, today }).orWhere('STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")', { today, nextSeventhDay });
                                    }))
                                        .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED });
                                    if (!ViewAll) {
                                        noticeOrdersQuery.andWhere((qb) => {
                                            const subQuery = qb
                                                .subQuery()
                                                .select('1')
                                                .from('client_client_managers_user', 'cm')
                                                .where('cm.client_id = client.id')
                                                .andWhere('cm.user_id = :userId')
                                                .getQuery();
                                            return `EXISTS (${subQuery})`;
                                        }, { userId: userData.id });
                                    }
                                    const noticeOrders = await noticeOrdersQuery.getMany();
                                    if ((noticeOrders === null || noticeOrders === void 0 ? void 0 : noticeOrders.length) > 0) {
                                        noticeAndOrdersArray = noticeOrders.map((order) => {
                                            var _a;
                                            return ({
                                                orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                                clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                                orderNumber: order === null || order === void 0 ? void 0 : order.orderNumber,
                                                issuanceDate: moment(order === null || order === void 0 ? void 0 : order.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                                dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                                    ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                                    : order === null || order === void 0 ? void 0 : order.dueDate,
                                                type: order === null || order === void 0 ? void 0 : order.type,
                                                amount: order === null || order === void 0 ? void 0 : order.amountOfDemand,
                                            });
                                        });
                                    }
                                    const orgId = organization === null || organization === void 0 ? void 0 : organization.id;
                                    const organizations = await organization_entity_1.Organization.findOne({ id: orgId });
                                    const addressParts = [
                                        organizations.buildingNo || '',
                                        organizations.floorNumber || '',
                                        organizations.buildingName || '',
                                        organizations.street || '',
                                        organizations.location || '',
                                        organizations.city || '',
                                        organizations.district || '',
                                        organizations.state || ''
                                    ].filter(part => part && part.trim() !== '');
                                    const pincode = organizations.pincode && organizations.pincode.trim() !== '' ? ` - ${organizations.pincode}` : '';
                                    const address = addressParts.join(', ') + pincode;
                                    if ((additionalNoticeOrdersArray === null || additionalNoticeOrdersArray === void 0 ? void 0 : additionalNoticeOrdersArray.length) > 0 || (noticeAndOrdersArray === null || noticeAndOrdersArray === void 0 ? void 0 : noticeAndOrdersArray.length) > 0) {
                                        const users = organization === null || organization === void 0 ? void 0 : organization.users;
                                        const mailOptions = {
                                            data: {
                                                additionalNoticeOrdersArray,
                                                noticeAndOrdersArray,
                                                userName: user === null || user === void 0 ? void 0 : user.fullName,
                                                userId: user === null || user === void 0 ? void 0 : user.id,
                                                websiteUrl: process.env.WEBSITE_URL,
                                                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                                                mail: organization === null || organization === void 0 ? void 0 : organization.email,
                                                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                                                adress: address,
                                            },
                                            email: user === null || user === void 0 ? void 0 : user.email,
                                            filePath: 'gstr-notice',
                                            subject: 'GST Notices',
                                            key: 'GSTR_NOTICE_MAIL',
                                            id: user === null || user === void 0 ? void 0 : user.id,
                                        };
                                        await (0, newemails_1.sendnewMail)(mailOptions);
                                    }
                                }
                                catch (error) {
                                    console.log(`Error in getting Gstr Notice records in cron:`, error);
                                }
                            }
                        }
                    }
                }
            }
            catch (error) {
                errorList.push(error.message);
                const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                    .where('id = :id', { id: cornActivityID.id })
                    .getOne();
                getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
                getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                await getcornActivityID.save();
                return console.log(error.message);
            }
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
            console.log('GST NOTICE CRON EXECUTION COMPLETED!!!!');
            return 'GST NOTICE CRON EXECUTION COMPLETED!!!!';
        }
    }
    async getAdditionalNoticeOrdersRecordsWhatsapp() {
        var _a;
        if (process.env.Cron_Running === 'true') {
            const cronData = new cron_activity_entity_1.default();
            cronData.cronType = 'GSTR NOTICES WHATSAPP';
            cronData.cronDate = moment().toDate().toString();
            cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const cornActivityID = await cronData.save();
            const errorList = [];
            try {
                const totalOrganization = await organization_entity_1.Organization.createQueryBuilder('organization')
                    .leftJoinAndSelect('organization.users', 'user')
                    .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate", { expirydate: moment().format('YYYY-MM-DD') })
                    .andWhere('user.status = :status', { status: 'active' })
                    .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION, id: 827 })
                    .getMany();
                for (let organization of totalOrganization) {
                    let additionalNoticeOrdersArray = [];
                    let noticeAndOrdersArray = [];
                    const today = moment().format('YYYY-MM-DD');
                    const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
                    const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
                    try {
                        const additionalNoticeOrders = await gstrAdditionalOrdersAndNotices_entity_1.default.createQueryBuilder('additionalNotice')
                            .where('additionalNotice.organizationId = :organizationId', {
                            organizationId: organization.id,
                        })
                            .andWhere(new typeorm_1.Brackets((qb) => {
                            qb.where('STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")', { previousSeventhDay, today }).orWhere('STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")', { today, nextSeventhDay });
                        }))
                            .leftJoinAndSelect('additionalNotice.client', 'client')
                            .getMany();
                        if ((additionalNoticeOrders === null || additionalNoticeOrders === void 0 ? void 0 : additionalNoticeOrders.length) > 0) {
                            additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => {
                                var _a;
                                return ({
                                    orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                    clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                    issuanceDate: moment(order === null || order === void 0 ? void 0 : order.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                    dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                        ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                        : order === null || order === void 0 ? void 0 : order.dueDate,
                                    financialYear: (order === null || order === void 0 ? void 0 : order.fy) || '-',
                                    folder: order === null || order === void 0 ? void 0 : order.caseFolderTypeName,
                                    referenceNumber: order === null || order === void 0 ? void 0 : order.refNum,
                                    type: order === null || order === void 0 ? void 0 : order.caseTypeName,
                                });
                            });
                        }
                        const noticeOrders = await noticeOrders_entity_1.default.createQueryBuilder('noticeOrder')
                            .where('noticeOrder.organizationId = :organizationId', {
                            organizationId: organization.id,
                        })
                            .andWhere(new typeorm_1.Brackets((qb) => {
                            qb.where('STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")', { previousSeventhDay, today }).orWhere('STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")', { today, nextSeventhDay });
                        }))
                            .leftJoinAndSelect('noticeOrder.client', 'client')
                            .getMany();
                        if ((noticeOrders === null || noticeOrders === void 0 ? void 0 : noticeOrders.length) > 0) {
                            noticeAndOrdersArray = noticeOrders.map((order) => {
                                var _a;
                                return ({
                                    orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                    clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                    orderNumber: order === null || order === void 0 ? void 0 : order.orderNumber,
                                    issuanceDate: moment(order === null || order === void 0 ? void 0 : order.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                    dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                        ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                        : order === null || order === void 0 ? void 0 : order.dueDate,
                                    type: order === null || order === void 0 ? void 0 : order.type,
                                    amount: order === null || order === void 0 ? void 0 : order.amountOfDemand,
                                });
                            });
                        }
                        if ((additionalNoticeOrdersArray === null || additionalNoticeOrdersArray === void 0 ? void 0 : additionalNoticeOrdersArray.length) > 0 || (noticeAndOrdersArray === null || noticeAndOrdersArray === void 0 ? void 0 : noticeAndOrdersArray.length) > 0) {
                            const users = organization === null || organization === void 0 ? void 0 : organization.users;
                            if ((users === null || users === void 0 ? void 0 : users.length) > 0) {
                                for (let user of users) {
                                    const ejsData = {
                                        additionalNoticeOrdersArray,
                                        noticeAndOrdersArray,
                                        userName: user === null || user === void 0 ? void 0 : user.fullName,
                                        userId: user === null || user === void 0 ? void 0 : user.id,
                                        websiteUrl: process.env.WEBSITE_URL,
                                    };
                                    const htmlContent = await ejs.renderFile('src/emails/templates/gstr-noticewhatsapp.ejs', ejsData);
                                    const browser = await puppeteer.launch({
                                        headless: true,
                                        executablePath: '/usr/bin/google-chrome',
                                        args: ['--no-sandbox', '--disable-setuid-sandbox'],
                                    });
                                    const page = await browser.newPage();
                                    await page.setContent(htmlContent);
                                    const pdfBuffer = await page.pdf({
                                        format: 'A4',
                                        printBackground: true,
                                        margin: {
                                            top: '1cm',
                                            bottom: '1cm',
                                            left: '1cm',
                                            right: '1cm',
                                        },
                                    });
                                    await browser.close();
                                    const s3Params = {
                                        Bucket: process.env.AWS_BUCKET_NAME,
                                        Key: `gstr-notice-report-${organization.id}-${moment().format('YYYY-MM-DD HH:mm:ss')}.pdf`,
                                        Body: pdfBuffer,
                                        ContentType: 'application/pdf',
                                    };
                                    const uploadResult = await s3.upload(s3Params).promise();
                                    const pdfLink = uploadResult.Location;
                                    const title = 'GST notice';
                                    const users = organization === null || organization === void 0 ? void 0 : organization.users;
                                    const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                                        where: { userId: user === null || user === void 0 ? void 0 : user.id, status: 'ACTIVE' },
                                    });
                                    if (sessionValidation) {
                                        const key = 'GSTR_NOTICE_WHATSAPP';
                                        try {
                                            const caption = "GSTR Notices Report";
                                            const filename = "GSTR_Notice_Report.pdf";
                                            await (0, whatsapp_service_1.sendDocumentTextMessage)(user === null || user === void 0 ? void 0 : user.mobileNumber, pdfLink, caption, filename, (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id, user === null || user === void 0 ? void 0 : user.id, title, key);
                                        }
                                        catch (error) {
                                            console.error('Error sending document message for user:', user.mobileNumber, error);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (error) {
                        console.log(`Error in getting Gstr Notice records in cron:`, error);
                    }
                }
            }
            catch (error) {
                errorList.push(error.message);
                const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                    .where('id = :id', { id: cornActivityID.id })
                    .getOne();
                getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
                getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                await getcornActivityID.save();
                return console.log(error.message);
            }
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
        }
    }
};
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_2AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GstrCronJobService.prototype, "getAdditionalNoticeOrdersRecordsWhatsapp", null);
GstrCronJobService = __decorate([
    (0, common_1.Injectable)()
], GstrCronJobService);
exports.GstrCronJobService = GstrCronJobService;
//# sourceMappingURL=gstr-cron-job-service.js.map