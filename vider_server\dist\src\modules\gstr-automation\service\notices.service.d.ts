import GstrNoticeOrders from '../entity/noticeOrders.entity';
import GstrProfile from '../entity/gstrProfile.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import GstrUpdateTracker from '../entity/gstr_update_tracker.entity';
import * as ExcelJS from 'exceljs';
import { GstrOutstandingDemand } from '../entity/gstrDemands.entity';
import { GstrLedgerBalance } from '../entity/gstrLedgersBalance.entity';
export declare class GstrService {
    getOrderNotices(userId: number, id: number, query: any): Promise<{
        count: number;
        result: GstrNoticeOrders[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    gstClientNoticeandordersExport(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getOrderNotice(userId: number, id: number): Promise<GstrNoticeOrders>;
    getGstrAdditionalDeailss(userId: number, id: number): Promise<{
        additionalData: GstrAdditionalNoticeOrders;
        categorizedData: {
            type: string;
            records: unknown;
        }[];
    }>;
    getGstrProfile(userId: number, id: number): Promise<{
        gstrProfile: GstrProfile;
        lastCompletedMachine: AutomationMachines;
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        gstrProfile?: undefined;
        lastCompletedMachine?: undefined;
    }>;
    getGstrClientCompliance(userId: number, id: number): Promise<Client | {
        accessDenied: boolean;
    }>;
    getAddNoticeAndOrders(userId: number, query: any): Promise<{
        count: number;
        result: GstrAdditionalNoticeOrders[];
    }>;
    exportAdditionalGstNoticeAndOrders(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getNoticeAndOrders(userId: number, query: any): Promise<{
        count: number;
        result: GstrNoticeOrders[];
    }>;
    exportGstNoticeAndOrders(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getGstrReport(userId: number, query: any): Promise<{
        data: AutomationMachines[];
        count: number;
    }>;
    exportGstClientReport(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getGstrAdditionalNoticeOrders(userId: number, id: number, query: any): Promise<{
        count: number;
        result: GstrAdditionalNoticeOrders[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportreferenceBasedNotices(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getNoticeAndOrderDueDateEvents(userId: number, query: Date): Promise<GstrNoticeOrders[]>;
    getNoticeAndOrderIssueDateEvents(userId: number, query: Date): Promise<GstrNoticeOrders[]>;
    getAdditionalNoticeOrderIssueDateEvents(userId: number, query: Date): Promise<GstrAdditionalNoticeOrders[]>;
    getAdditionalNoticeOrderDueDateEvents(userId: number, query: Date): Promise<GstrAdditionalNoticeOrders[]>;
    getGstrUpdates(userId: number, query: any): Promise<GstrUpdateTracker[]>;
    findGstrUpdateItem(userId: any, id: number): Promise<GstrUpdateTracker>;
    organizationGstrScheduling(userId: any): Promise<void>;
    getAllGstrDemands(userId: number, query: any): Promise<{
        count: number;
        result: GstrOutstandingDemand[];
    }>;
    getClientDemands(userId: number, id: number): Promise<{
        result: GstrOutstandingDemand[];
        count: number;
    }>;
    getAllGstrLedgers(userId: number, query: any): Promise<{
        count: number;
        result: GstrLedgerBalance[];
    }>;
    getClientLedgers(userId: number, query: any, id: number): Promise<{
        result: GstrLedgerBalance[];
        count: number;
    }>;
    exportDemands(userId: number, query: any): Promise<ExcelJS.Buffer>;
    exportLedgers(userId: number, query: any): Promise<ExcelJS.Buffer>;
    exportClientDemand(userId: number, query: any): Promise<ExcelJS.Buffer>;
    exportClientLedger(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getCaseTypeNames(userId: any): Promise<any[]>;
    getCaseFolderTypeNames(userId: any): Promise<any[]>;
}
