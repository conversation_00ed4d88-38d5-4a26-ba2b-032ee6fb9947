export declare function getCurrentQuarter(): string;
export declare function getQuarterFromMonth(month: number): string;
export declare const tanFormTypes: string[];
export declare function getAllotmentQuarter(month: number): string;
export declare function getBehindQuarter(quarter: string): "Q4" | "Q1" | "Q2" | "Q3";
export declare function getQuartersBetween(startYear: number, startQuarterIndex: number, endYear: number, endQuarterIndex: number): any[];
export declare function getFinancialQuarter(date: Date): number;
export declare function getFormValidQuarters(startDate: string, financialYear: number): any[];
export declare function getQuartersBetweenFinancialYear(dateOfAllotment: Date, financialYear: number, currentYear: number, currentQuarterIndex: number): any[];
