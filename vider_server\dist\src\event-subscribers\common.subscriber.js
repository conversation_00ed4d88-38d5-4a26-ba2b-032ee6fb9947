"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonSubscriber = void 0;
const afterInsert_1 = require("../utils/afterInsert");
const afterUpdate_1 = require("../utils/afterUpdate");
const typeorm_1 = require("typeorm");
let CommonSubscriber = class CommonSubscriber {
    constructor(cls) {
        this.cls = cls;
    }
    listenTo() {
        return this.cls;
    }
    async afterInsert(event) {
        (0, afterInsert_1.taskUpdated)(event);
    }
    async afterUpdate(event) {
        (0, afterUpdate_1.taskafterUpdatedThenStatusUpdate)(event);
        (0, afterUpdate_1.taskafterUpdated)(event);
    }
};
CommonSubscriber = __decorate([
    (0, typeorm_1.EventSubscriber)(),
    __metadata("design:paramtypes", [Function])
], CommonSubscriber);
exports.CommonSubscriber = CommonSubscriber;
//# sourceMappingURL=common.subscriber.js.map