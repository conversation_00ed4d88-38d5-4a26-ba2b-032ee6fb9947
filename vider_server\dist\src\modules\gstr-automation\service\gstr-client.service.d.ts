import GstrCredentials from '../entity/gstrCredentials.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import * as ExcelJS from 'exceljs';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import AutomationMachinesArchive from 'src/modules/automation/entities/automation_machines_archive.entity';
export declare class GstrClientService {
    getGstrClients(userId: number, query: any): Promise<{
        count: number;
        result: GstrCredentials[];
    }>;
    exportGstrClient(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getAtomClients(userId: number, data: any): Promise<any>;
    addGstrCredentials(userId: any, body: any): Promise<void>;
    updateGstrCredentials(id: number, body: any, userId: number): Promise<GstrCredentials>;
    sendSingleGstrCamundaRequest(userId: number, data: any): Promise<any>;
    createGsrRequest(userId: number, id: number, body?: any): Promise<any>;
    sendIncometaxAutomationRequestToCamunda(userId: number, data: any): Promise<void>;
    bulkGstrSync(userId: any, data: any): Promise<void>;
    getActivityLog(id: any, query: any, userId: number): Promise<{
        result: AutomationMachines[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getActivityArchiveLog(id: any, query: any, userId: number): Promise<{
        result: AutomationMachinesArchive[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientSyncStatus(id: number, userId: number): Promise<{
        lastCompletedMachine: AutomationMachines;
        totalInqueueCount: number;
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    getCaseIdBasedClientNotices(id: number, userId: number, query: any): Promise<{
        count: number;
        result: GstrAdditionalNoticeOrders[];
        accessDenied: boolean;
    }>;
    exportCaseBasedNotices(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getCaseIdBasedOrgNotices(userId: number, query: any): Promise<{
        count: number;
        result: GstrAdditionalNoticeOrders[];
    }>;
    exportCasebasedOrgNotices(userId: number, query: any): Promise<ExcelJS.Buffer>;
}
