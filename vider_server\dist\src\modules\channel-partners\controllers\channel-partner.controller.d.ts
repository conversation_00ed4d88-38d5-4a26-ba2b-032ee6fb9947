import { ChannelPartnerService } from '../services/channel-partner.service';
export declare class ChannelPartnerController {
    private readonly channelPartnerService;
    constructor(channelPartnerService: ChannelPartnerService);
    getAllPartners(): Promise<import("../entity/channel-partner.entity").ChannelPartner[]>;
    getActivePartners(): Promise<import("../entity/channel-partner.entity").ChannelPartner[]>;
    createPartner(dto: any): Promise<import("../entity/channel-partner.entity").ChannelPartner[]>;
    updatePartner(id: number, dto: any): Promise<import("../entity/channel-partner.entity").ChannelPartner>;
    updatePartnerToogle(id: number, dto: any): Promise<import("../entity/channel-partner.entity").ChannelPartner>;
    getAllCoupons(): Promise<import("../entity/coupon-code.entity").CouponCode[]>;
    createCoupon(dto: any): Promise<import("../entity/coupon-code.entity").CouponCode[]>;
    updateCoupon(id: number, dto: any): Promise<import("../entity/coupon-code.entity").CouponCode>;
    validateCoupon(dto: any): Promise<{
        message: string;
        couponId: number;
        partnerId: number;
    }>;
    getSignUps(query: any): Promise<{
        count: number;
        result: import("../entity/channel-partner-signup.entity").ChannelPartnerSignup[];
    }>;
    updateSignUpStatus(id: number, data: any): Promise<void>;
    getPartnerAnalytics(id: number): Promise<{
        partner: {
            name: string;
            status: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
        coupons: {
            active: number;
            expired: number;
        };
        users: number;
        leadStatusCounts: any[];
    }>;
}
