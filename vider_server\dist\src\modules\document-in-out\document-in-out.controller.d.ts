/// <reference types="multer" />
import { DocumentInOutService } from './doucment-in-out.service';
export declare class DocumentInOutController {
    private service;
    constructor(service: DocumentInOutService);
    create(body: any, req: any): Promise<number>;
    createDocumentItem(body: any, req: any): Promise<number>;
    deleteDocumentItem(body: any, req: any): Promise<void>;
    get(req: any, query: any): Promise<[import("./entity/document-in-out.entity").default[], number]>;
    findOne(req: any, id: number): Promise<import("./entity/document-in-out.entity").default>;
    update(req: any, id: number, body: any): Promise<void>;
    delete(id: number, req: any, query: any): Promise<{
        success: boolean;
    }>;
    addAttachments(files: Express.Multer.File[], taskId: number, docId: number, req: any): Promise<any>;
}
