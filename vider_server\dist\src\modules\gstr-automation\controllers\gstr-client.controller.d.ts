import { GstrClientService } from '../service/gstr-client.service';
export declare class GstrClientController {
    private service;
    constructor(service: GstrClientService);
    getGstrClients(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrCredentials.entity").default[];
    }>;
    exportGstrClient(req: any, body: any): Promise<import("exceljs").Buffer>;
    getAtomClients(req: any, query: any): Promise<any>;
    addClientAutCredentials(body: any, req: any): Promise<void>;
    update(id: number, body: any, req: any): Promise<import("../entity/gstrCredentials.entity").default>;
    createGsrRequest(req: any, id: number, body: any): Promise<any>;
    bulkAutomationSync(body: any, req: any): Promise<void>;
    getActivityLog(req: any, id: number, query: any): Promise<{
        result: import("../../automation/entities/automation_machines.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getActivityArchiveLog(req: any, id: number, query: any): Promise<{
        result: import("../../automation/entities/automation_machines_archive.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientSyncStatus(id: number, req: any): Promise<{
        lastCompletedMachine: import("../../automation/entities/automation_machines.entity").default;
        totalInqueueCount: number;
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    getCaseIdBasedClientNotices(id: number, req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrAdditionalOrdersAndNotices.entity").default[];
        accessDenied: boolean;
    }>;
    exportCaseBasedNotices(req: any, body: any): Promise<import("exceljs").Buffer>;
    getCaseIdBasedOrgNotices(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrAdditionalOrdersAndNotices.entity").default[];
    }>;
    exportCasebasedOrgNotices(req: any, body: any): Promise<import("exceljs").Buffer>;
}
