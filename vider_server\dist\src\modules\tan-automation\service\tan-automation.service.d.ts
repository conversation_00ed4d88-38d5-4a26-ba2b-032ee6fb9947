import TanClientCredentials from '../entity/tan-client-credentials.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import TanProfile from '../entity/tan-profile.entity';
import TanEChallan from '../entity/tan-e-challan.entity';
import TanIncomeTaxForms from '../entity/tan-income-tax-forms.entity';
import TanMyCas from '../entity/tan-my-cas.entity';
import <PERSON><PERSON>ey<PERSON>erson from '../entity/tan-key-person.entity';
import TanCommunicationInbox from '../entity/tan-communication-inbox.entity';
import * as ExcelJS from 'exceljs';
import TanTempEproFya from '../entity/tan_temp_epro_fya.entity';
import TanTempEproFyi from '../entity/tan_temp_epro_fyi.entity';
import TraceOutstandingDemand from '../entity/trace-outstanding-deman.entity';
export declare class TanAutomationService {
    findAll(userId: number, query: any): Promise<{
        count: number;
        data: TanClientCredentials[];
    }>;
    exportIncomeTaxTanClients(userId: number, query: any): Promise<ExcelJS.Buffer>;
    addClientTanCredentials(userId: number, body: any): Promise<void>;
    updateClientTanCredentials(id: any, body: any, userId: any): Promise<TanClientCredentials>;
    getAllClients(userId: number, data: any): Promise<any>;
    getIncomeTaxProfile(userId: number, id: number): Promise<{
        profileDetails: TanProfile;
        lastCompletedMachine: AutomationMachines;
        checkClientCredentials: boolean;
        keyPersonDetails: TanKeyPerson[];
        clientCredential: TanClientCredentials;
        lastCompletedTracesMachine: AutomationMachines;
    }>;
    clientEChallan(userId: number, query: any, id: number): Promise<{
        count: number;
        result: TanEChallan[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientChallan(userId: number, query: any): Promise<ExcelJS.Buffer>;
    findEchallan(userId: number, id: number): Promise<TanEChallan>;
    findEchallans(userId: number, query: any): Promise<{
        count: number;
        result: TanEChallan[];
    }>;
    exportTanIncomeTaxChallans(userId: number, query: any): Promise<ExcelJS.Buffer>;
    findForm(userId: number, id: number): Promise<{
        myCaDetails: any;
        id: number;
        formCd: string;
        formDesc: string;
        formName: string;
        formShortName: string;
        ackDt: string;
        ackNum: string;
        caMembershipNo: string;
        caName: string;
        filingTypeCd: string;
        fillingMode: string;
        submitBy: string;
        submitUserId: string;
        udinNum: string;
        activities: object;
        storageFiles: object;
        verStatus: string;
        refYear: string;
        refYearType: string;
        formStatus: string;
        financialQuarter: string;
        isUdinApplicable: boolean;
        transactionNo: string;
        tempAckNo: string;
        createdAt: string;
        updatedAt: string;
        organizationId: number;
        clientId: number;
        transactionType: string;
        tanClientCredentials: TanClientCredentials;
        status: string;
        dtOfPrcng: string;
    }>;
    getClientform(id: number, query: any, userId: number): Promise<{
        count: number;
        result: TanIncomeTaxForms[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientForm(userId: number, query: any): Promise<ExcelJS.Buffer>;
    findAllForms(userId: number, query: any): Promise<{
        count: number;
        result: TanIncomeTaxForms[];
    }>;
    exportTanIncomeTaxForms(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getActivityLogData(id: any, query: any, userId: number): Promise<{
        result: AutomationMachines[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientReport(userId: number, query: any): Promise<{
        data: AutomationMachines[];
        count: number;
    }>;
    exportTanSyncStatus(userId: number, query: any): Promise<ExcelJS.Buffer>;
    exportTanIncomeTaxReports(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getclientAutoStatus(id: number, userId: number): Promise<{
        lastCompletedMachine: AutomationMachines;
        accessDenied: boolean;
        totalInqueueCount: number;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    findMycas(userId: number, query: any): Promise<{
        count: number;
        result: TanMyCas[];
    }>;
    exportTanIncomeTaxMycas(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getMycaFormTypes(userId: number): Promise<any[]>;
    clientMycas(userId: number, query: any, id: number): Promise<{
        count: number;
        result: TanMyCas[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientMyCas(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getClientTraceCommunications(id: number, query: any, userId: number): Promise<{
        count: number;
        result: TanCommunicationInbox[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportClientTanTracesInbox(userId: number, query: any): Promise<ExcelJS.Buffer>;
    findAllTraceCommunication(userId: number, query: any): Promise<{
        count: number;
        result: TanCommunicationInbox[];
    }>;
    exportClientTracesInbox(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getActivityLogTracesData(id: any, query: any, userId: number): Promise<{
        result: AutomationMachines[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientAutoTracesStatus(id: number, userId: number): Promise<{
        lastCompletedMachine: AutomationMachines;
        accessDenied: boolean;
        totalInqueueCount: number;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    getTraceReport(userId: number, query: any): Promise<{
        data: AutomationMachines[];
        count: number;
    }>;
    exportTanTraceSyncStatus(userId: number, query: any): Promise<ExcelJS.Buffer>;
    findFyaTempNotices(userId: number, query: any): Promise<{
        count: number;
        result: TanTempEproFya[];
    }>;
    findFyiTempNotices(userId: number, query: any): Promise<{
        count: number;
        result: TanTempEproFyi[];
    }>;
    getExcelFyaSections(userId: number): Promise<any[]>;
    getExcelFyiSections(userId: number): Promise<any[]>;
    getClientExcelProceedingFyi(id: number, query: any, userId: number): Promise<{
        count: number;
        result: TanTempEproFyi[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    getClientExcelProceedingFya(id: number, query: any, userId: number): Promise<{
        count: number;
        result: TanTempEproFya[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    getExcelCombinedNotices(userId: number, query: any): Promise<{
        count: number;
        result: {
            eproType: string;
            id: number;
            clientId: number;
            client: Client;
            organizationId: number;
            tanClientCredentialsId: number;
            proceedingName: string;
            pan: string;
            ay: string;
            proceedingLimitationDate: string;
            proceedingStatus: string;
            proceedingConcludedDate: string;
            noticeDin: string;
            noticeSentDate: string;
            noticeSection: string;
            dateOfCompliance: string;
            dateResponseSubmitted: string;
            createdAt: Date;
            updatedAt: Date;
            uuid: string;
            type: import("../entity/tan_temp_epro_fya.entity").EproceedingTypeEnum;
        }[];
    }>;
    findAllDemands(userId: number, query: any): Promise<{
        count: number;
        result: TraceOutstandingDemand[];
    }>;
    findDemand(userId: number, id: number): Promise<TraceOutstandingDemand>;
    getClientDemand(id: number, query: any, userId: number): Promise<{
        count: number;
        result: TraceOutstandingDemand[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportDemands(userId: number, query: any): Promise<ExcelJS.Buffer>;
    exportClientDemand(userId: number, query: any): Promise<ExcelJS.Buffer>;
}
