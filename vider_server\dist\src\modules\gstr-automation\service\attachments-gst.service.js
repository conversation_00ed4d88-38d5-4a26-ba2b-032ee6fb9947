"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentGstService = void 0;
const common_1 = require("@nestjs/common");
const organization_entity_1 = require("../../organization/entities/organization.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const storage_entity_1 = require("../../storage/storage.entity");
const uuid_1 = require("uuid");
const upload_service_1 = require("../../storage/upload.service");
const storage_service_1 = require("../../storage/storage.service");
const onedrive_storage_service_1 = require("../../ondrive-storage/onedrive-storage.service");
const auth_token_entity_1 = require("../../ondrive-storage/auth-token.entity");
const activity_entity_1 = require("../../activity/activity.entity");
const actions_1 = require("../../../event-listeners/actions");
const bharath_storage_service_1 = require("../../storage/bharath-storage.service");
const googledrive_storage_service_1 = require("../../ondrive-storage/googledrive-storage.service");
const noticeOrders_entity_1 = require("../entity/noticeOrders.entity");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
let AttachmentGstService = class AttachmentGstService {
    constructor(uploadService, bharahServce, storageService, oneDriveStorageService, googleDriveStorageService) {
        this.uploadService = uploadService;
        this.bharahServce = bharahServce;
        this.storageService = storageService;
        this.oneDriveStorageService = oneDriveStorageService;
        this.googleDriveStorageService = googleDriveStorageService;
    }
    async saveAttachment(noticeId, files, userId, type) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const storageSystem = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.storageSystem;
        if (storageSystem === organization_entity_1.StorageSystem.AMAZON) {
            return await this.addAttachment(noticeId, files, userId, type);
        }
        else if (storageSystem === organization_entity_1.StorageSystem.MICROSOFT) {
            return await this.addOneDriveAttachments(noticeId, files, userId, type);
        }
        else if (storageSystem === organization_entity_1.StorageSystem.GOOGLE) {
            return await this.addGoogleAttachments(noticeId, files, userId, type);
        }
    }
    async addAttachment(noticeId, files, userId, type) {
        var _a, _b;
        try {
            let notice;
            if (type === 'NoticeAndOrder') {
                notice = await noticeOrders_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            else if (type === 'AdditionalNotice') {
                notice = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            let existingStorage = await this.existingGstrStorage(notice, user, type);
            let attachments = [];
            let errors = [];
            for (let file of files) {
                const { buffer, mimetype, originalname, size } = file;
                const existingAttachement = await storage_entity_1.default.find({
                    where: { parent: existingStorage, name: originalname },
                });
                if (existingAttachement === null || existingAttachement === void 0 ? void 0 : existingAttachement.length) {
                    errors.push(`File name ${originalname} already exists`);
                    continue;
                }
                const { freeSpace } = await this.storageService.getOrgStorage(userId);
                if (!(freeSpace - file.size >= 0)) {
                    throw new common_1.ConflictException('We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.');
                }
                let key = `storage/automation/${(_a = notice === null || notice === void 0 ? void 0 : notice.client) === null || _a === void 0 ? void 0 : _a.id}/ ${file.originalname}`;
                let upload = await this.uploadService.upload(buffer, key, mimetype);
                let storage = new storage_entity_1.default();
                storage.name = originalname;
                storage.file = upload.Key;
                storage.fileSize = size;
                storage.fileType = mimetype;
                storage.client = notice.client;
                storage.type = storage_entity_1.StorageType.FILE;
                storage.parent = existingStorage;
                storage.user = user;
                storage.storageSystem = organization_entity_1.StorageSystem.AMAZON;
                if (type === 'NoticeAndOrder') {
                    storage.gstrNoticeOrders = notice;
                }
                else if (type === 'AdditionalNotice') {
                    storage.gstrAdditionalNoticeOrders = notice;
                }
                attachments.push(storage);
            }
            await storage_entity_1.default.save(attachments);
            for (let i of attachments) {
                let collectactivity = new activity_entity_1.default();
                collectactivity.action = actions_1.Event_Actions.ATTACHEMENT_ADDED;
                collectactivity.actorId = user.id;
                collectactivity.type = activity_entity_1.ActivityType.CLIENT;
                collectactivity.typeId = (_b = notice === null || notice === void 0 ? void 0 : notice.client) === null || _b === void 0 ? void 0 : _b.id;
                collectactivity.remarks = `Atom Pro Attachement "${i.name}" Added by ${user.fullName}`;
                await collectactivity.save();
            }
            if (errors === null || errors === void 0 ? void 0 : errors.length) {
                return { errors };
            }
            else {
                return {
                    success: true,
                };
            }
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException(error);
        }
    }
    async getFY(year) {
        if (!year)
            return 'Others';
        const nextYear = Number(year) + 1;
        const shortNextYear = nextYear.toString().slice(-2);
        return `AY ${year}-${shortNextYear}`;
    }
    async convertFYString(fyString) {
        if (!fyString || fyString === 'null' || fyString === 'undefined')
            return 'Others';
        const [startYear, endYear] = fyString.split('-');
        const shortEndYear = endYear.slice(-2);
        return `FY ${startYear}-${shortEndYear}`;
    }
    async capitalizeFirstLetterOfEachWord(str) {
        return str
            .split(' ')
            .map((word) => {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        })
            .join(' ');
    }
    async existingGstrStorage(notice, user, type, storageType) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        try {
            if (!(notice === null || notice === void 0 ? void 0 : notice.client))
                return null;
            let atomFolder;
            let clientFolder;
            let displayNameFolder;
            let atomProFolder;
            let gstFolder;
            let categoryFolder;
            let subCategoryFolder;
            let fyFolder;
            const category = type === 'NoticeAndOrder' ? 'Notice And Orders' : 'Additional Notices And Orders';
            const subCategory = type === 'NoticeAndOrder' ? notice === null || notice === void 0 ? void 0 : notice.type : notice === null || notice === void 0 ? void 0 : notice.caseTypeName;
            if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                atomFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: 'Atom',
                        organization: user === null || user === void 0 ? void 0 : user.organization,
                        show: false,
                    },
                });
                if (!atomFolder) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Atom');
                    atomFolder = new storage_entity_1.default();
                    atomFolder.name = 'Atom';
                    atomFolder.organization = user === null || user === void 0 ? void 0 : user.organization;
                    atomFolder.type = storage_entity_1.StorageType.FOLDER;
                    atomFolder.uid = (0, uuid_1.v4)();
                    atomFolder.fileId = folderData.id;
                    atomFolder.show = false;
                    atomFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                    atomFolder.authId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
                    await atomFolder.save();
                }
                clientFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: 'Clients',
                        organization: user === null || user === void 0 ? void 0 : user.organization,
                        show: false,
                    },
                });
                if (!clientFolder) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Clients', atomFolder.fileId);
                    clientFolder = new storage_entity_1.default();
                    clientFolder.name = 'Clients';
                    clientFolder.organization = user === null || user === void 0 ? void 0 : user.organization;
                    clientFolder.type = storage_entity_1.StorageType.FOLDER;
                    clientFolder.uid = (0, uuid_1.v4)();
                    clientFolder.fileId = folderData.id;
                    clientFolder.show = false;
                    clientFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                    clientFolder.authId = user === null || user === void 0 ? void 0 : user.organization.id;
                    await clientFolder.save();
                }
                displayNameFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: (_b = notice === null || notice === void 0 ? void 0 : notice.client) === null || _b === void 0 ? void 0 : _b.displayName,
                        organization: user === null || user === void 0 ? void 0 : user.organization,
                        show: false,
                        type: storage_entity_1.StorageType.FOLDER,
                    },
                });
                if (!displayNameFolder) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, notice.client.displayName, clientFolder === null || clientFolder === void 0 ? void 0 : clientFolder.fileId);
                    displayNameFolder = new storage_entity_1.default();
                    displayNameFolder.name = (_c = notice.client) === null || _c === void 0 ? void 0 : _c.displayName;
                    displayNameFolder.organization = user.organization;
                    displayNameFolder.type = storage_entity_1.StorageType.FOLDER;
                    displayNameFolder.uid = (0, uuid_1.v4)();
                    displayNameFolder.fileId = folderData.id;
                    displayNameFolder.show = false;
                    displayNameFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                    displayNameFolder.authId = user.organization.id;
                    if (notice === null || notice === void 0 ? void 0 : notice.client) {
                        displayNameFolder.client = notice.client;
                    }
                    await displayNameFolder.save();
                }
            }
            else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                atomFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: 'Atom',
                        organization: user.organization.id,
                        show: false,
                        type: storage_entity_1.StorageType.FOLDER,
                    },
                });
                if (!atomFolder) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Atom');
                    atomFolder = new storage_entity_1.default();
                    atomFolder.name = 'Atom';
                    atomFolder.organization = user.organization;
                    atomFolder.type = storage_entity_1.StorageType.FOLDER;
                    atomFolder.uid = (0, uuid_1.v4)();
                    atomFolder.fileId = folderData.id;
                    atomFolder.show = false;
                    atomFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                    atomFolder.authId = user.organization.id;
                    await atomFolder.save();
                }
                clientFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: 'Clients',
                        organization: user.organization.id,
                        show: false,
                        type: storage_entity_1.StorageType.FOLDER,
                    },
                });
                if (!clientFolder) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Clients', atomFolder.fileId);
                    clientFolder = new storage_entity_1.default();
                    clientFolder.name = 'Clients';
                    clientFolder.organization = user.organization;
                    clientFolder.type = storage_entity_1.StorageType.FOLDER;
                    clientFolder.uid = (0, uuid_1.v4)();
                    clientFolder.fileId = folderData.id;
                    clientFolder.show = false;
                    clientFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                    clientFolder.authId = user.organization.id;
                    await clientFolder.save();
                }
                displayNameFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: notice.client.displayName,
                        organization: user.organization.id,
                        show: false,
                        type: storage_entity_1.StorageType.FOLDER,
                        client: notice.client,
                    },
                });
                if (!displayNameFolder) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, notice.client.displayName, clientFolder === null || clientFolder === void 0 ? void 0 : clientFolder.fileId);
                    displayNameFolder = new storage_entity_1.default();
                    displayNameFolder.name = notice.client.displayName;
                    displayNameFolder.organization = user.organization;
                    displayNameFolder.type = storage_entity_1.StorageType.FOLDER;
                    displayNameFolder.uid = (0, uuid_1.v4)();
                    displayNameFolder.fileId = folderData.id;
                    displayNameFolder.show = false;
                    displayNameFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                    displayNameFolder.authId = user.organization.id;
                    if (notice === null || notice === void 0 ? void 0 : notice.client) {
                        displayNameFolder.client = notice.client;
                    }
                    await displayNameFolder.save();
                }
            }
            if (notice.client) {
                atomProFolder = await storage_entity_1.default.findOne({
                    where: {
                        name: 'Atom Pro',
                        client: { id: (_d = notice === null || notice === void 0 ? void 0 : notice.client) === null || _d === void 0 ? void 0 : _d.id },
                        show: true,
                    },
                });
            }
            if (!atomProFolder) {
                atomProFolder = new storage_entity_1.default();
                atomProFolder.name = 'Atom Pro';
                atomProFolder.client = notice.client;
                atomProFolder.type = storage_entity_1.StorageType.FOLDER;
                atomProFolder.uid = (0, uuid_1.v4)();
                atomProFolder.show = true;
                atomProFolder.authId = notice.organizationId;
                if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Atom Pro', displayNameFolder === null || displayNameFolder === void 0 ? void 0 : displayNameFolder.fileId);
                    atomProFolder.fileId = folderData.id;
                    atomProFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                }
                else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'Atom Pro', displayNameFolder === null || displayNameFolder === void 0 ? void 0 : displayNameFolder.fileId);
                    atomProFolder.fileId = folderData.id;
                    atomProFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                }
                await atomProFolder.save();
            }
            gstFolder = await storage_entity_1.default.findOne({
                where: {
                    name: 'GST',
                    client: { id: (_e = notice === null || notice === void 0 ? void 0 : notice.client) === null || _e === void 0 ? void 0 : _e.id },
                    show: true,
                    parent: { id: atomProFolder === null || atomProFolder === void 0 ? void 0 : atomProFolder.id },
                },
            });
            if (!gstFolder) {
                gstFolder = new storage_entity_1.default();
                gstFolder.name = 'GST';
                gstFolder.client = notice.client;
                gstFolder.type = storage_entity_1.StorageType.FOLDER;
                gstFolder.uid = (0, uuid_1.v4)();
                gstFolder.show = true;
                gstFolder.authId = notice.organizationId;
                gstFolder.parent = atomProFolder;
                if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'GST', atomProFolder === null || atomProFolder === void 0 ? void 0 : atomProFolder.fileId);
                    gstFolder.fileId = folderData.id;
                    gstFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                }
                else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, 'GST', atomProFolder === null || atomProFolder === void 0 ? void 0 : atomProFolder.fileId);
                    gstFolder.fileId = folderData.id;
                    gstFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                }
                await gstFolder.save();
            }
            categoryFolder = await storage_entity_1.default.findOne({
                where: {
                    name: await this.capitalizeFirstLetterOfEachWord(category),
                    client: { id: (_f = notice === null || notice === void 0 ? void 0 : notice.client) === null || _f === void 0 ? void 0 : _f.id },
                    show: true,
                    parent: { id: gstFolder === null || gstFolder === void 0 ? void 0 : gstFolder.id },
                },
            });
            if (!categoryFolder) {
                categoryFolder = new storage_entity_1.default();
                categoryFolder.name = await this.capitalizeFirstLetterOfEachWord(category);
                categoryFolder.client = notice.client;
                categoryFolder.type = storage_entity_1.StorageType.FOLDER;
                categoryFolder.uid = (0, uuid_1.v4)();
                categoryFolder.show = true;
                categoryFolder.authId = notice.organizationId;
                categoryFolder.parent = gstFolder;
                if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.capitalizeFirstLetterOfEachWord(category), gstFolder === null || gstFolder === void 0 ? void 0 : gstFolder.fileId);
                    categoryFolder.fileId = folderData.id;
                    categoryFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                }
                else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.capitalizeFirstLetterOfEachWord(category), gstFolder === null || gstFolder === void 0 ? void 0 : gstFolder.fileId);
                    categoryFolder.fileId = folderData.id;
                    categoryFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                }
                await categoryFolder.save();
            }
            subCategoryFolder = await storage_entity_1.default.findOne({
                where: {
                    name: await this.capitalizeFirstLetterOfEachWord(subCategory),
                    client: { id: (_g = notice === null || notice === void 0 ? void 0 : notice.client) === null || _g === void 0 ? void 0 : _g.id },
                    show: true,
                    parent: { id: categoryFolder === null || categoryFolder === void 0 ? void 0 : categoryFolder.id },
                },
            });
            if (!subCategoryFolder) {
                subCategoryFolder = new storage_entity_1.default();
                subCategoryFolder.name = await this.capitalizeFirstLetterOfEachWord(subCategory);
                subCategoryFolder.client = notice.client;
                subCategoryFolder.type = storage_entity_1.StorageType.FOLDER;
                subCategoryFolder.uid = (0, uuid_1.v4)();
                subCategoryFolder.show = true;
                subCategoryFolder.authId = notice.organizationId;
                subCategoryFolder.parent = categoryFolder;
                if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.capitalizeFirstLetterOfEachWord(subCategory), categoryFolder === null || categoryFolder === void 0 ? void 0 : categoryFolder.fileId);
                    subCategoryFolder.fileId = folderData.id;
                    subCategoryFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                }
                else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.capitalizeFirstLetterOfEachWord(subCategory), categoryFolder === null || categoryFolder === void 0 ? void 0 : categoryFolder.fileId);
                    subCategoryFolder.fileId = folderData.id;
                    subCategoryFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                }
                await subCategoryFolder.save();
            }
            if (type === 'NoticeAndOrder') {
                return subCategoryFolder;
            }
            fyFolder = await storage_entity_1.default.findOne({
                where: {
                    name: await this.convertFYString(notice === null || notice === void 0 ? void 0 : notice.fy),
                    client: { id: (_h = notice === null || notice === void 0 ? void 0 : notice.client) === null || _h === void 0 ? void 0 : _h.id },
                    show: true,
                    parent: { id: subCategoryFolder === null || subCategoryFolder === void 0 ? void 0 : subCategoryFolder.id },
                },
            });
            if (!fyFolder) {
                fyFolder = new storage_entity_1.default();
                fyFolder.name = await this.convertFYString(notice === null || notice === void 0 ? void 0 : notice.fy);
                fyFolder.client = notice.client;
                fyFolder.type = storage_entity_1.StorageType.FOLDER;
                fyFolder.uid = (0, uuid_1.v4)();
                fyFolder.show = true;
                fyFolder.authId = notice.organizationId;
                fyFolder.parent = subCategoryFolder;
                if (storageType && storageType === organization_entity_1.StorageSystem.MICROSOFT) {
                    const folderData = await this.oneDriveStorageService.createOneDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.convertFYString(notice === null || notice === void 0 ? void 0 : notice.fy), gstFolder === null || gstFolder === void 0 ? void 0 : gstFolder.fileId);
                    fyFolder.fileId = folderData.id;
                    fyFolder.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                }
                else if (storageType && storageType === organization_entity_1.StorageSystem.GOOGLE) {
                    const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(user === null || user === void 0 ? void 0 : user.id, await this.convertFYString(notice === null || notice === void 0 ? void 0 : notice.fy), gstFolder === null || gstFolder === void 0 ? void 0 : gstFolder.fileId);
                    fyFolder.fileId = folderData.id;
                    fyFolder.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                }
                await fyFolder.save();
            }
            return fyFolder;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException(error);
        }
    }
    async addOneDriveAttachments(noticeId, files, userId, type) {
        var _a, _b;
        try {
            let notice;
            if (type === 'NoticeAndOrder') {
                notice = await noticeOrders_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            else if (type === 'AdditionalNotice') {
                notice = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            let token = await auth_token_entity_1.default.findOne({
                where: {
                    organizationId: user.organization.id,
                    type: auth_token_entity_1.AuthTokenType.MICROSFT,
                },
            });
            let existingStorage = await this.existingGstrStorage(notice, user, type, organization_entity_1.StorageSystem.MICROSOFT);
            let taskAttachments = [];
            let errors = [];
            for (let file of files) {
                const { buffer, mimetype, originalname, size } = file;
                const existingAttachement = await storage_entity_1.default.find({
                    where: { parent: existingStorage, name: originalname },
                });
                if (existingAttachement === null || existingAttachement === void 0 ? void 0 : existingAttachement.length) {
                    errors.push(`File name ${originalname} already exists`);
                    continue;
                }
                let key = `${existingStorage.fileId}:/${file.originalname
                    .replace(/[<>:"\/\\|?*]/g, '')
                    .replace(/\.(?=.*\.)/g, '')}:`;
                let upload;
                try {
                    upload = await this.oneDriveStorageService.upload(buffer, key, mimetype, token, file, userId);
                }
                catch (err) {
                    let error = (_b = (_a = err === null || err === void 0 ? void 0 : err.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.error;
                    if (error.code === 'InvalidAuthenticationToken') {
                        await this.oneDriveStorageService.refreshToken(token);
                        upload = await this.oneDriveStorageService.upload(buffer, key, mimetype, token, file, userId);
                    }
                    else if (error.code === 'quotaLimitReached') {
                        throw new common_1.ConflictException({
                            code: 'NO_STORAGE',
                            message: error.message,
                        });
                    }
                }
                let storage = new storage_entity_1.default();
                storage.name = originalname;
                storage.file = upload.file;
                storage.webUrl = upload.webUrl;
                storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
                storage.fileId = upload.id;
                storage.authId = user.organization.id;
                storage.fileSize = size;
                storage.fileType = mimetype;
                storage.client = notice.client;
                storage.type = storage_entity_1.StorageType.FILE;
                storage.parent = existingStorage;
                storage.storageSystem = organization_entity_1.StorageSystem.MICROSOFT;
                storage.user = user;
                if (type === 'NoticeAndOrder') {
                    storage.gstrNoticeOrders = notice;
                }
                else if (type === 'AdditionalNotice') {
                    storage.gstrAdditionalNoticeOrders = notice;
                }
                taskAttachments.push(storage);
            }
            await storage_entity_1.default.save(taskAttachments);
            if (errors === null || errors === void 0 ? void 0 : errors.length) {
                return { errors };
            }
            else {
                return {
                    success: true,
                };
            }
        }
        catch (err) {
            console.log(err);
            throw new common_1.InternalServerErrorException(err);
        }
    }
    async addGoogleAttachments(noticeId, files, userId, type) {
        var _a, _b, _c, _d;
        try {
            let notice;
            if (type === 'NoticeAndOrder') {
                notice = await noticeOrders_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            else if (type === 'AdditionalNotice') {
                notice = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id: noticeId },
                    relations: ['client'],
                });
            }
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            let token = await auth_token_entity_1.default.findOne({
                where: {
                    organizationId: user.organization.id,
                    type: auth_token_entity_1.AuthTokenType.GOOGLE,
                },
            });
            let existingStorage = await this.existingGstrStorage(notice, user, type, organization_entity_1.StorageSystem.GOOGLE);
            let taskAttachments = [];
            let errors = [];
            for (let file of files) {
                const { buffer, mimetype, originalname, size } = file;
                const existingAttachement = await storage_entity_1.default.find({
                    where: { parent: existingStorage, name: originalname },
                });
                if (existingAttachement === null || existingAttachement === void 0 ? void 0 : existingAttachement.length) {
                    errors.push(`File name ${originalname} already exists`);
                    continue;
                }
                let key = `${existingStorage.fileId}:/${file.originalname
                    .replace(/[<>:"\/\\|?*]/g, '')
                    .replace(/\.(?=.*\.)/g, '')}:`;
                let upload;
                try {
                    upload = await ((_a = this.googleDriveStorageService) === null || _a === void 0 ? void 0 : _a.uploadToGoogleDrive(file.originalname, token, buffer, existingStorage === null || existingStorage === void 0 ? void 0 : existingStorage.fileId, userId));
                }
                catch (err) {
                    let error = (_c = (_b = err === null || err === void 0 ? void 0 : err.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.error;
                    if (error.code === 'InvalidAuthenticationToken') {
                        await ((_d = this.googleDriveStorageService) === null || _d === void 0 ? void 0 : _d.refreshToken(token));
                        upload = await this.googleDriveStorageService.uploadToGoogleDrive(file.originalname, token, buffer, existingStorage === null || existingStorage === void 0 ? void 0 : existingStorage.fileId, userId);
                    }
                    else if (error.code === 'quotaLimitReached') {
                        throw new common_1.ConflictException({
                            code: 'NO_STORAGE',
                            message: error.message,
                        });
                    }
                }
                let storage = new storage_entity_1.default();
                storage.name = originalname;
                storage.file = upload.file;
                storage.webUrl = upload.webUrl;
                storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
                storage.fileId = upload.id;
                storage.authId = user.organization.id;
                storage.fileSize = size;
                storage.fileType = mimetype;
                storage.client = notice === null || notice === void 0 ? void 0 : notice.client;
                storage.type = storage_entity_1.StorageType.FILE;
                storage.parent = existingStorage;
                storage.storageSystem = organization_entity_1.StorageSystem.GOOGLE;
                storage.user = user;
                if (type === 'NoticeAndOrder') {
                    storage.gstrNoticeOrders = notice;
                }
                else if (type === 'AdditionalNotice') {
                    storage.gstrAdditionalNoticeOrders = notice;
                }
                taskAttachments.push(storage);
            }
            await storage_entity_1.default.save(taskAttachments);
            if (errors === null || errors === void 0 ? void 0 : errors.length) {
                return { errors };
            }
            else {
                return {
                    success: true,
                };
            }
        }
        catch (err) {
            console.log(err);
            throw new common_1.InternalServerErrorException(err);
        }
    }
    async deleteStorageFile(storageId, userId) {
        try {
            await this.storageService.removeFile(storageId, userId);
        }
        catch (err) {
            console.log(err);
            throw new common_1.InternalServerErrorException(err);
        }
    }
};
AttachmentGstService = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => storage_service_1.StorageService))),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => onedrive_storage_service_1.OneDriveStorageService))),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => googledrive_storage_service_1.GoogleDriveStorageService))),
    __metadata("design:paramtypes", [upload_service_1.AwsService,
        bharath_storage_service_1.BharathStorageService,
        storage_service_1.StorageService,
        onedrive_storage_service_1.OneDriveStorageService,
        googledrive_storage_service_1.GoogleDriveStorageService])
], AttachmentGstService);
exports.AttachmentGstService = AttachmentGstService;
//# sourceMappingURL=attachments-gst.service.js.map