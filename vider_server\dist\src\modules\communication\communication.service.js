"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunicationService = exports.BroadcastMessageTo = void 0;
const common_1 = require("@nestjs/common");
const nodemailer = require("nodemailer");
const user_entity_1 = require("../users/entities/user.entity");
const client_group_broadcast_entity_1 = require("./entity/client-group-broadcast.entity");
const typeorm_1 = require("typeorm");
const broadcast_email_templates_entity_1 = require("./entity/broadcast-email-templates-entity");
const broadcast_activity_entity_1 = require("./entity/broadcast-activity.entity");
const schedule_1 = require("@nestjs/schedule");
const newemails_1 = require("../../emails/newemails");
const upload_service_1 = require("../storage/upload.service");
const broadcast_activity_details_entity_1 = require("./entity/broadcast-activity-details.entity");
const label_entity_1 = require("../labels/label.entity");
const client_entity_1 = require("../clients/entity/client.entity");
let transporter = nodemailer.createTransport({
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
});
var BroadcastMessageTo;
(function (BroadcastMessageTo) {
    BroadcastMessageTo["CLIENT"] = "CLIENT";
    BroadcastMessageTo["CLIENTGROUP"] = "CLIENTGROUP";
})(BroadcastMessageTo = exports.BroadcastMessageTo || (exports.BroadcastMessageTo = {}));
let CommunicationService = class CommunicationService {
    constructor(awsService) {
        this.awsService = awsService;
    }
    async sendEmailTemplatetoclients(data) {
        try {
            let subject = `Email template`;
            let mailOptions = {
                from: {
                    name: 'Vider',
                    address: '<EMAIL>',
                },
                to: data.emails,
                subject: data === null || data === void 0 ? void 0 : data.subject,
                html: data.content,
            };
            transporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(error);
                    return error;
                }
                else {
                    console.log(info.response);
                    return info.response;
                }
            });
        }
        catch (error) {
            console.log(error);
        }
    }
    async cronEmailTemplatetoclients() {
        if (process.env.Cron_Running === 'true') {
            const broadcastActivities = await broadcast_activity_entity_1.default.createQueryBuilder('broadcastActivity')
                .leftJoinAndSelect('broadcastActivity.details', 'details')
                .leftJoinAndSelect('broadcastActivity.user', 'user')
                .leftJoinAndSelect('broadcastActivity.template', 'template')
                .where('broadcastActivity.status = :status', { status: 'READY' })
                .getMany();
            for (const br of broadcastActivities) {
                const { template, details } = br;
                let hasError = false;
                let errorMessage = '';
                br.Status = broadcast_activity_entity_1.Status.PROCESSING;
                await br.save();
                for (const detail of details) {
                    await new Promise((resolve) => {
                        setTimeout(async () => {
                            var _a;
                            let mailOptions = {
                                from: {
                                    name: 'Vider',
                                    address: process.env.FROM_EMAIL,
                                },
                                to: detail === null || detail === void 0 ? void 0 : detail.email,
                                subject: template === null || template === void 0 ? void 0 : template.subject,
                                html: template === null || template === void 0 ? void 0 : template.content,
                                userId: (_a = br === null || br === void 0 ? void 0 : br.user) === null || _a === void 0 ? void 0 : _a.id,
                                fromTemplate: true,
                            };
                            try {
                                await (0, newemails_1.sendMailViaAny)(mailOptions);
                                console.log(`Email sent to ${detail.email} for broadcast activity ID: ${br.id}`);
                            }
                            catch (error) {
                                console.log(`Failed to send email to ${detail.email} for broadcast activity ID: ${br.id}`, error);
                                hasError = true;
                            }
                            resolve();
                        }, 10000);
                    });
                    if (hasError)
                        break;
                }
                br.Status = hasError ? broadcast_activity_entity_1.Status.ERROR : broadcast_activity_entity_1.Status.SENT;
                br.errorMessage = hasError ? errorMessage : null;
                await br.save();
            }
        }
    }
    async createClientGroup(data, userId) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const existingGroup = await client_group_broadcast_entity_1.default.findOne({
            where: {
                groupName: data.groupName,
                organization: { id: user.organization.id },
            },
        });
        if (existingGroup) {
            throw new common_1.BadRequestException('GroupName already exists in your organization');
        }
        let newClientGroupBroadcast = new client_group_broadcast_entity_1.default();
        newClientGroupBroadcast.groupName = data.groupName;
        if (data === null || data === void 0 ? void 0 : data.tags) {
            newClientGroupBroadcast.label = data.tags;
        }
        if (data === null || data === void 0 ? void 0 : data.description) {
            newClientGroupBroadcast.description = data.description;
        }
        newClientGroupBroadcast.clients = data === null || data === void 0 ? void 0 : data.users;
        newClientGroupBroadcast.organization = user.organization;
        await newClientGroupBroadcast.save();
        return newClientGroupBroadcast;
    }
    async findClientGroup(userId, query) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const clientGroupData = await client_group_broadcast_entity_1.default.createQueryBuilder('clientGroupBroadcast')
            .leftJoin('clientGroupBroadcast.clients', 'client', 'client.status != :deletedStatus', { deletedStatus: 'DELETED' })
            .leftJoin('clientGroupBroadcast.organization', 'organization')
            .addSelect('organization.id')
            .leftJoinAndSelect('clientGroupBroadcast.label', 'label')
            .where('organization.id = :organizationId', { organizationId: user.organization.id })
            .addSelect('COUNT(client.id)', 'clientCount')
            .groupBy('clientGroupBroadcast.id')
            .orderBy('clientGroupBroadcast.createdAt', 'DESC');
        if (query.search) {
            clientGroupData.andWhere('clientGroupBroadcast.groupName LIKE :search', {
                search: `%${query.search}%`,
            });
        }
        const result = await clientGroupData.getRawAndEntities();
        const formattedResult = result.entities.map((group, index) => (Object.assign(Object.assign({}, group), { clientCount: parseInt(result.raw[index].clientCount, 10) })));
        return formattedResult;
    }
    async deleteClientGroup(ids, userId) {
        let clienGroup = await client_group_broadcast_entity_1.default.delete(ids);
        return clienGroup;
    }
    async updateClientGroup(userId, id, body) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        if (!user) {
            throw new Error('User not found');
        }
        let clientGroupData = await client_group_broadcast_entity_1.default.findOne({
            where: { id: id },
            relations: ['organization', 'clients'],
        });
        if (!clientGroupData) {
            throw new Error('ClientGroupBroadcast not found');
        }
        let existingGroup = await client_group_broadcast_entity_1.default.findOne({
            where: {
                groupName: body.groupName,
                organization: { id: user.organization.id },
                id: (0, typeorm_1.Not)(id),
            },
        });
        if (existingGroup) {
            throw new common_1.BadRequestException('Group name already exists in this organization');
        }
        clientGroupData.groupName = body.groupName;
        clientGroupData.label = body.label;
        clientGroupData.description = body === null || body === void 0 ? void 0 : body.description;
        clientGroupData.organization = user.organization;
        await clientGroupData.save();
        return clientGroupData;
    }
    async addClientstoClientGroup(userId, id, body) {
        let clientGroupData = await client_group_broadcast_entity_1.default.findOne({
            where: { id: id },
            relations: ['organization', 'clients'],
        });
        clientGroupData.clients = [...clientGroupData.clients, ...body.clients];
        await clientGroupData.save();
        return clientGroupData;
    }
    async removeClientsFromClientGroup(userId, id, body) {
        const ids = body.clients.map(item => item.id);
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        if (!user) {
            throw new Error('User not found');
        }
        let clientGroupData = await client_group_broadcast_entity_1.default.findOne({
            where: { id: id },
            relations: ['organization', 'clients'],
        });
        const clients = clientGroupData.clients.filter(item => !ids.includes(item.id));
        clientGroupData.clients = clients;
        if (!clientGroupData.clients.length) {
            throw new common_1.BadRequestException('Atleast one client is mandatory');
        }
        await clientGroupData.save();
        return clientGroupData;
    }
    async findOneClientGroup(userId, id, query) {
        var _a, _b, _c, _d;
        const { limit, offset } = query;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let clientGroupData = client_group_broadcast_entity_1.default.createQueryBuilder('clientGroupBroadcast')
            .leftJoinAndSelect('clientGroupBroadcast.clients', 'client')
            .leftJoinAndSelect('clientGroupBroadcast.label', 'label')
            .leftJoinAndSelect('clientGroupBroadcast.organization', 'organization')
            .where('clientGroupBroadcast.id = :id', { id: id });
        const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                clientId: 'client.clientId',
                clientNumber: 'client.clientNumber',
                displayName: 'client.displayName',
                category: 'client.category',
                active: 'client.active',
            };
            const column = columnMap[sort.column] || sort.column;
            clientGroupData.orderBy(column, sort.direction.toUpperCase());
        }
        else {
            clientGroupData.orderBy('clientGroupBroadcast.createdAt', 'DESC');
        }
        ;
        if (query.search) {
            clientGroupData.andWhere('(client.displayName LIKE :search OR client.email LIKE :search)', { search: `%${query.search}%` });
        }
        let clientGroup = await clientGroupData
            .getOne();
        const checkOrganization = await client_group_broadcast_entity_1.default.findOne({ where: { id }, relations: ["organization"] });
        const checkAccess = ((_a = checkOrganization === null || checkOrganization === void 0 ? void 0 : checkOrganization.organization) === null || _a === void 0 ? void 0 : _a.id) === ((_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id);
        let clientCount = 0;
        if (clientGroup && (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.clients)) {
            clientCount = (_c = clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.clients) === null || _c === void 0 ? void 0 : _c.length;
            const start = (_d = offset * limit) !== null && _d !== void 0 ? _d : 0;
            const end = parseInt(limit) ? start + parseInt(limit) : clientCount;
            clientGroup.clients = clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.clients.slice(start, end);
        }
        return { clientGroup, clientCount, checkAccess };
    }
    async createEmailTemplate(data, userId) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const existingGroup = await broadcast_email_templates_entity_1.default.findOne({
            where: {
                title: data.title,
                organization: { id: user.organization.id },
            },
        });
        if (existingGroup) {
            throw new common_1.BadRequestException('Email Title already exists in your organization');
        }
        let broadcastEmailTemplates = new broadcast_email_templates_entity_1.default();
        broadcastEmailTemplates.title = data.title;
        broadcastEmailTemplates.label = data.tags;
        broadcastEmailTemplates.content = data.content;
        broadcastEmailTemplates.subject = data.subject;
        broadcastEmailTemplates.organization = user.organization;
        await broadcastEmailTemplates.save();
        return broadcastEmailTemplates;
    }
    async getEmailTemplates(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const broadcastEmailTemplatesQuery = broadcast_email_templates_entity_1.default.createQueryBuilder('broadcastEmailTemplates')
            .leftJoin('broadcastEmailTemplates.organization', 'organization')
            .leftJoinAndSelect('broadcastEmailTemplates.label', 'label')
            .where(new typeorm_1.Brackets(qb => {
            qb.where('organization.id = :organizationId', { organizationId: user.organization.id })
                .orWhere('broadcastEmailTemplates.default = :default', { default: 1 });
        }))
            .orderBy('broadcastEmailTemplates.createdAt', 'DESC');
        if (query.search) {
            broadcastEmailTemplatesQuery.andWhere('broadcastEmailTemplates.title LIKE :search', {
                search: `%${query.search}%`,
            });
        }
        let emailTemplates = await broadcastEmailTemplatesQuery.getMany();
        const templateLabels = emailTemplates
            .map((template) => template.labels)
            .filter((label) => !!label);
        if (templateLabels.length === 0) {
            return emailTemplates;
        }
        const labelColors = await label_entity_1.default.createQueryBuilder('label')
            .leftJoin('label.organization', 'organization')
            .where('organization.id = :orgId OR label.defaultOne = true', {
            orgId: (_a = user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('label.name IN (:...templateLabels)', { templateLabels })
            .getMany();
        const formattedTemplates = emailTemplates.map((template) => {
            const matchingLabel = labelColors.find((label) => label.name === template.labels);
            return Object.assign(Object.assign({}, template), { labelColor: matchingLabel ? matchingLabel.color : null });
        });
        return formattedTemplates;
    }
    async getOneEmailTemplate(userId, id) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const getOneEmailTemplate = await broadcast_email_templates_entity_1.default.createQueryBuilder('EmailTemplate')
            .leftJoinAndSelect('EmailTemplate.organization', 'organization')
            .leftJoinAndSelect('EmailTemplate.label', 'label')
            .where('EmailTemplate.id = :id', { id })
            .andWhere(new typeorm_1.Brackets(qb => {
            qb.where('organization.id = :orgId', { orgId: user.organization.id })
                .orWhere('EmailTemplate.default = true');
        }))
            .getOne();
        return getOneEmailTemplate;
    }
    async updateEmailTemplate(userId, id, body) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        if (!user) {
            throw new Error('User not found');
        }
        let emailTemplateData = await broadcast_email_templates_entity_1.default.findOne({
            where: { id: id },
            relations: ['organization'],
        });
        if (!emailTemplateData) {
            throw new Error('emailTemplateData not found');
        }
        const existingGroup = await broadcast_email_templates_entity_1.default.findOne({
            where: {
                title: body.title,
                organization: { id: user.organization.id },
                id: (0, typeorm_1.Not)(id),
            },
        });
        if (existingGroup) {
            throw new common_1.BadRequestException('Email Title already exists in your organization');
        }
        emailTemplateData.title = body.title;
        emailTemplateData.labels = body.tags;
        emailTemplateData.content = body.content;
        emailTemplateData.label = body.tags;
        emailTemplateData.subject = body.subject;
        await emailTemplateData.save();
        return emailTemplateData;
    }
    async deleteEmailTemplate(ids, userId) {
        let clienGroup = await broadcast_email_templates_entity_1.default.delete(ids);
        return clienGroup;
    }
    async createBroadcastActivity(data, userId) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let clientGroupData = client_group_broadcast_entity_1.default.createQueryBuilder('clientGroupBroadcast')
            .leftJoinAndSelect('clientGroupBroadcast.clients', 'client')
            .where('clientGroupBroadcast.id = :id', { id: data.clientGroupId });
        let clientGroup = await clientGroupData.getOne();
        let newBroadcastActivity = new broadcast_activity_entity_1.default();
        newBroadcastActivity.template = data.templateId;
        newBroadcastActivity.clientGroup = data.clientGroupId;
        newBroadcastActivity.templateName = data.templateName;
        newBroadcastActivity.groupName = data.clientGroupName;
        newBroadcastActivity.broadcastMessageTo = BroadcastMessageTo.CLIENTGROUP;
        newBroadcastActivity.Status = data.status;
        newBroadcastActivity.organization = user.organization;
        newBroadcastActivity.user = user;
        await newBroadcastActivity.save();
        let broadcastActivityDetailsArray = [];
        if ((_a = clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.clients) === null || _a === void 0 ? void 0 : _a.length) {
            for (const client of clientGroup.clients) {
                let newBroadcastActivityDetails = new broadcast_activity_details_entity_1.default();
                newBroadcastActivityDetails.broadcastActivityId = newBroadcastActivity.id;
                newBroadcastActivityDetails.clientName = client.displayName;
                newBroadcastActivityDetails.email = client.email;
                broadcastActivityDetailsArray.push(newBroadcastActivityDetails);
            }
        }
        if (broadcastActivityDetailsArray.length) {
            await broadcast_activity_details_entity_1.default.save(broadcastActivityDetailsArray);
        }
        return newBroadcastActivity;
    }
    async getBroadcastActivity(userId, query) {
        const { limit, offset } = query;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const broadcastActivity = await broadcast_activity_entity_1.default.createQueryBuilder('broadcastActivity')
            .leftJoinAndSelect('broadcastActivity.template', 'template')
            .leftJoinAndSelect('broadcastActivity.clientGroup', 'clientGroup')
            .leftJoinAndSelect('broadcastActivity.details', 'details')
            .leftJoinAndSelect('broadcastActivity.user', 'user')
            .leftJoinAndSelect('broadcastActivity.organization', 'organization')
            .where('organization.id = :organizationId', { organizationId: user.organization.id })
            .orderBy('broadcastActivity.createdAt', 'DESC');
        const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                template: 'broadcastActivity.templateName',
                clientGroup: 'broadcastActivity.groupName',
                clients: 'broadcastActivity.groupName',
                createdAt: 'broadcastActivity.createdAt',
                sentAt: 'broadcastActivity.updatedAt',
            };
            const column = columnMap[sort.column] || sort.column;
            broadcastActivity.orderBy(column, sort.direction.toUpperCase());
        }
        else {
            broadcastActivity.orderBy('broadcastActivity.createdAt', 'DESC');
        }
        ;
        if (query.search) {
            broadcastActivity.andWhere('broadcastActivity.templateName LIKE :search OR broadcastActivity.groupName LIKE :search', { search: `%${query.search}%` });
        }
        if (offset >= 0) {
            broadcastActivity.skip(offset);
        }
        if (limit) {
            broadcastActivity.take(limit);
        }
        let result = await broadcastActivity.getManyAndCount();
        return result;
    }
    async getBroadcastActivityDetails(userId, id, query) {
        var _a, _b, _c;
        const { limit, offset, search } = query;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const queryBuilder = await broadcast_activity_entity_1.default.createQueryBuilder('broadcastActivity')
            .leftJoinAndSelect('broadcastActivity.details', 'details')
            .leftJoinAndSelect('broadcastActivity.organization', 'organization')
            .leftJoinAndSelect('broadcastActivity.clientGroup', 'clientGroup')
            .where('organization.id = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
            .andWhere('broadcastActivity.id = :id', { id: id });
        if (search) {
            queryBuilder.andWhere('(details.clientName LIKE :search OR details.email LIKE :search)', { search: `%${search}%` });
        }
        const getOneEmailTemplate = await queryBuilder.getOne();
        const checkOrganization = await broadcast_activity_entity_1.default.findOne({ where: { id }, relations: ["organization"] });
        const checkAccess = ((_b = checkOrganization === null || checkOrganization === void 0 ? void 0 : checkOrganization.organization) === null || _b === void 0 ? void 0 : _b.id) === ((_c = user === null || user === void 0 ? void 0 : user.organization) === null || _c === void 0 ? void 0 : _c.id);
        return { getOneEmailTemplate, checkAccess };
    }
    async upload(buffer, key, contentType = '') {
        try {
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const upload = await this.awsService.uploadS3(buffer, bucketS3, key, contentType);
            return upload;
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async filteredClients(userId, id, query) {
        const { limit, offset } = query;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const existingClients = await (0, typeorm_1.createQueryBuilder)(client_group_broadcast_entity_1.default, 'clientgroup')
            .leftJoinAndSelect('clientgroup.clients', 'client')
            .where('clientgroup.id = :id', { id })
            .getOne();
        const existingClientIds = existingClients
            ? existingClients.clients.map((client) => client.id)
            : [];
        const repo = (0, typeorm_1.createQueryBuilder)(client_entity_1.default, 'client')
            .leftJoinAndSelect('client.organization', 'organization')
            .where('organization.id = :organizationId', { organizationId: user.organization.id })
            .andWhere('client.status != :deletedStatus', { deletedStatus: 'DELETED' });
        if (existingClientIds.length > 0) {
            repo.andWhere('client.id NOT IN (:...existingClientIds)', { existingClientIds });
        }
        if (query.search) {
            repo.andWhere('(client.displayName LIKE :search OR client.email LIKE :search)', {
                search: `%${query.search}%`,
            });
        }
        if (offset >= 0) {
            repo.skip(offset);
        }
        if (limit) {
            repo.take(limit);
        }
        const result = await repo.getManyAndCount();
        return {
            count: result[1],
            result: result[0]
        };
    }
};
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_5_MINUTES),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommunicationService.prototype, "cronEmailTemplatetoclients", null);
CommunicationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [upload_service_1.AwsService])
], CommunicationService);
exports.CommunicationService = CommunicationService;
//# sourceMappingURL=communication.service.js.map