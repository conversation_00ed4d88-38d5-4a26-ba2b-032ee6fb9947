"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AwsService = void 0;
const common_1 = require("@nestjs/common");
const aws_sdk_1 = require("aws-sdk");
let AwsService = class AwsService {
    async upload(buffer, key, contentType = '') {
        const bucketS3 = process.env.AWS_BUCKET_NAME;
        return await this.uploadS3(buffer, bucketS3, key, contentType);
    }
    async get(key) {
        try {
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            return await this.getFileFromS3(bucketS3, key);
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async getFileFromS3(bucket, key) {
        const s3 = this.getS3();
        const params = {
            Bucket: bucket,
            Key: key,
        };
        return new Promise((resolve, reject) => {
            s3.getObject(params, (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err.message);
                }
                resolve(data);
            });
        });
    }
    async uploadS3(file, bucket, key, contentType) {
        const s3 = this.getS3();
        const params = {
            Bucket: bucket,
            Key: key,
            Body: file,
            ContentType: contentType,
        };
        return new Promise((resolve, reject) => {
            s3.upload(params, (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err.message);
                }
                resolve(data);
            });
        });
    }
    async deleteFile(key) {
        if (!key)
            return null;
        try {
            const s3 = this.getS3();
            const params = {
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: key,
            };
            return new Promise((resolve, reject) => {
                s3.deleteObject(params, (err, data) => {
                    if (err) {
                        console.error(err);
                        reject(err.message);
                    }
                    resolve(data);
                });
            });
        }
        catch (err) {
            console.error(err);
        }
    }
    getS3() {
        return new aws_sdk_1.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            region: 'ap-south-1',
        });
    }
};
AwsService = __decorate([
    (0, common_1.Injectable)()
], AwsService);
exports.AwsService = AwsService;
//# sourceMappingURL=logoupload.server.js.map