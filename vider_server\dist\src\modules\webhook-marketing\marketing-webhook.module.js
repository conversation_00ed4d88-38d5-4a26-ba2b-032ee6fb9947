"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketingWebhookModule = void 0;
const common_1 = require("@nestjs/common");
const marketing_webhook_service_1 = require("./marketing-webhook.service");
const typeorm_1 = require("@nestjs/typeorm");
const vider_marketing_webhook_entity_1 = require("./entity/vider-marketing-webhook.entity");
const marketing_webhook_controller_1 = require("./marketing-webhook.controller");
const viderMarketingWebhook_subscriber_1 = require("../../event-subscribers/viderMarketingWebhook.subscriber");
const vider_marketing_entity_1 = require("./entity/vider-marketing.entity");
let MarketingWebhookModule = class MarketingWebhookModule {
};
MarketingWebhookModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                vider_marketing_webhook_entity_1.default, vider_marketing_entity_1.default
            ])
        ],
        controllers: [marketing_webhook_controller_1.MarketingWebhookController],
        providers: [marketing_webhook_service_1.MarketingWebhookService, viderMarketingWebhook_subscriber_1.ViderMarketingWebhookSubscriber],
    })
], MarketingWebhookModule);
exports.MarketingWebhookModule = MarketingWebhookModule;
//# sourceMappingURL=marketing-webhook.module.js.map