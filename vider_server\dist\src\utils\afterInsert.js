"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.taskUpdated = void 0;
const globals_1 = require("typeorm/globals");
async function taskUpdated(event) {
}
exports.taskUpdated = taskUpdated;
async function getOrgUserbyOrgID(orgid) {
    let sql = `select u.id, u.full_name from user u 
    Left join role r on r.id = u.role_id 
    where u.organization_id = '${orgid}' and r.name = 'Admin'`;
    let orgUser = await (0, globals_1.getManager)().query(sql);
    return orgUser;
}
async function insertINTOnotification(event, users) {
    let sql = `INSERT INTO notification (user_id, title, body) 
    VALUES ('95', 'User Invited', '{user name} have invited {invitee name}')`;
    const entityManager = (0, globals_1.getManager)();
    let notification = await entityManager.query(sql);
}
//# sourceMappingURL=afterInsert.js.map