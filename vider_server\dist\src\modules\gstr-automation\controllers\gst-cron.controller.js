"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstCronController = void 0;
const common_1 = require("@nestjs/common");
const gstr_cron_job_service_1 = require("../service/gstr-cron-job-service");
const api_key_auth_guard_1 = require("../../../cron-auth/api-key-auth.guard");
let GstCronController = class GstCronController {
    constructor(service) {
        this.service = service;
    }
    async getAdditionalNoticeOrdersRecords() {
        return this.service.getAdditionalNoticeOrdersRecords();
    }
};
__decorate([
    (0, common_1.UseGuards)(api_key_auth_guard_1.CronAuthGuard),
    (0, common_1.Get)('/notices'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GstCronController.prototype, "getAdditionalNoticeOrdersRecords", null);
GstCronController = __decorate([
    (0, common_1.Controller)('gst-cron'),
    __metadata("design:paramtypes", [gstr_cron_job_service_1.GstrCronJobService])
], GstCronController);
exports.GstCronController = GstCronController;
//# sourceMappingURL=gst-cron.controller.js.map