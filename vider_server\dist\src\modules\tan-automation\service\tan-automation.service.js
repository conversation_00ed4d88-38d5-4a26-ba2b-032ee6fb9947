"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanAutomationService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../../users/entities/user.entity");
const tan_client_credentials_entity_1 = require("../entity/tan-client-credentials.entity");
const typeorm_1 = require("typeorm");
const client_group_entity_1 = require("../../client-group/client-group.entity");
const aut_client_credentials_entity_1 = require("../../automation/entities/aut_client_credentials.entity");
const client_entity_1 = require("../../clients/entity/client.entity");
const automation_machines_entity_1 = require("../../automation/entities/automation_machines.entity");
const tan_profile_entity_1 = require("../entity/tan-profile.entity");
const tan_e_challan_entity_1 = require("../entity/tan-e-challan.entity");
const tan_income_tax_forms_entity_1 = require("../entity/tan-income-tax-forms.entity");
const tan_my_cas_entity_1 = require("../entity/tan-my-cas.entity");
const datesFormation_1 = require("../../../utils/datesFormation");
const tan_key_person_entity_1 = require("../entity/tan-key-person.entity");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
const password_entity_1 = require("../../clients/entity/password.entity");
const permission_1 = require("../../tasks/permission");
const atomProReUse_1 = require("../../../utils/atomProReUse");
const tan_communication_inbox_entity_1 = require("../entity/tan-communication-inbox.entity");
const ExcelJS = require("exceljs");
const re_use_1 = require("../../../utils/re-use");
const utils_1 = require("../../../utils");
const moment = require("moment");
const lodash_1 = require("lodash");
const tan_temp_epro_fya_entity_1 = require("../entity/tan_temp_epro_fya.entity");
const tan_temp_epro_fyi_entity_1 = require("../entity/tan_temp_epro_fyi.entity");
const trace_outstanding_deman_entity_1 = require("../entity/trace-outstanding-deman.entity");
const categoryLabels = {
    individual: 'Individual',
    huf: 'Hindu Undivided Family',
    partnership_firm: 'Partnership Firm',
    llp: 'Limited Liability Partnership',
    company: 'Company',
    opc: 'OPC',
    public: 'Public Limited',
    government: 'Government',
    sec_8: 'Section-8',
    foreign: 'Foreign',
    aop: 'Association of Persons',
    boi: 'Body of Individuals',
    trust: 'Trust',
    public_trust: 'Public Trust',
    private_discretionary_trust: 'Private Discretionary Trust',
    state: 'State',
    central: 'Central',
    local_authority: 'Local Authority',
    artificial_judicial_person: 'Artificial Juridical Person',
};
let TanAutomationService = class TanAutomationService {
    async findAll(userId, query) {
        var _a;
        try {
            const { offset, limit, search } = query;
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const tanClients = (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'tanClientCredentials')
                .leftJoinAndSelect('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('tanClientCredentials.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('tanClientCredentials.status IS NULL').orWhere('tanClientCredentials.status = :enabledStatus', { enabledStatus: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE });
            }));
            if (search) {
                tanClients.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('client.displayName LIKE :search', {
                        search: `%${query.search}%`,
                    });
                    qb.orWhere('tanClientCredentials.tanNumber LIKE :search', {
                        search: `%${query.search}%`,
                    });
                }));
            }
            if (ViewAssigned && !ViewAll) {
                tanClients.andWhere('clientManagers.id = :userId', { userId });
            }
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    client: 'client.clientId',
                    Category: 'client.category',
                    displayName: 'client.displayName',
                    tanNumber: 'tanClientCredentials.tanNumber',
                };
                const column = columnMap[sort.column] || sort.column;
                tanClients.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                tanClients.orderBy('tanClientCredentials.createdAt', 'DESC');
            }
            ;
            if (offset >= 0) {
                tanClients.skip(offset);
            }
            if (limit) {
                tanClients.take(limit);
            }
            const result = await tanClients.getManyAndCount();
            return {
                count: result[1],
                data: result[0],
            };
        }
        catch (error) {
            console.log('error occur while getting tan clients', error);
        }
    }
    async exportIncomeTaxTanClients(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        let clients = await this.findAll(userId, newQuery);
        if (!clients.data.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Clients');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client ID', key: 'clientId' },
            { header: 'Category', key: 'category' },
            { header: 'Client Name', key: 'displayName' },
            { header: 'TAN', key: 'tan' },
            { header: 'Password', key: 'password' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        clients.data.forEach((client) => {
            var _a, _b;
            const rowData = {
                serialNo: serialCounter++,
                clientId: (_a = client === null || client === void 0 ? void 0 : client.client) === null || _a === void 0 ? void 0 : _a.clientId,
                category: (0, utils_1.getTitle)((_b = client === null || client === void 0 ? void 0 : client.client) === null || _b === void 0 ? void 0 : _b.category),
                displayName: client.client.displayName,
                tan: client === null || client === void 0 ? void 0 : client.tanNumber,
                password: client === null || client === void 0 ? void 0 : client.password,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'displayName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async addClientTanCredentials(userId, body) {
        var _a, _b, _c, _d, _e, _f, _g;
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const credential = await tan_client_credentials_entity_1.default.findOne({
                where: { organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id, tanNumber: body === null || body === void 0 ? void 0 : body.tanNumber },
            });
            const organizationPreferences = await organization_preferences_entity_1.default.findOne({
                where: { organization: user.organization },
            });
            if (organizationPreferences) {
                const organizationLimit = (_b = organizationPreferences === null || organizationPreferences === void 0 ? void 0 : organizationPreferences.automationConfig) === null || _b === void 0 ? void 0 : _b.tanLimit;
                if (organizationLimit) {
                    const tanClientCredentialCount = await tan_client_credentials_entity_1.default.count({
                        where: { organizationId: user.organization.id, status: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE },
                    });
                    if (organizationLimit >= tanClientCredentialCount) {
                        if (credential) {
                            throw new common_1.BadRequestException('Specified TAN Number Utilize your organization already');
                        }
                        else {
                            const client = await client_entity_1.default.findOne({ where: { id: (_c = body === null || body === void 0 ? void 0 : body.selectedClient) === null || _c === void 0 ? void 0 : _c.id } });
                            const tanPassword = ((_d = body === null || body === void 0 ? void 0 : body.password) !== null && _d !== void 0 ? _d : '').trim();
                            const traceUserId = ((_e = body === null || body === void 0 ? void 0 : body.traceUserId) !== null && _e !== void 0 ? _e : '').trim();
                            const tracePassword = ((_f = body === null || body === void 0 ? void 0 : body.tracePassword) !== null && _f !== void 0 ? _f : '').trim();
                            const details = {
                                website: 'Income Tax | e-Filing (TAN)',
                                websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                                loginId: body === null || body === void 0 ? void 0 : body.tanNumber,
                                password: (body === null || body === void 0 ? void 0 : body.password) === "" ? null : body === null || body === void 0 ? void 0 : body.password,
                                client: client,
                                isaddAtomPro: password_entity_1.IsExistingAtomPro.YES,
                                userId,
                                traceUserId: body === null || body === void 0 ? void 0 : body.traceUserId,
                                tracePassword: body === null || body === void 0 ? void 0 : body.tracePassword,
                            };
                            const traceDetails = {
                                website: 'Income Tax | Traces (Tax Deductor)',
                                websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                                loginId: (body === null || body === void 0 ? void 0 : body.traceUserId) === '' ? null : body === null || body === void 0 ? void 0 : body.traceUserId,
                                password: (body === null || body === void 0 ? void 0 : body.tracePassword) === '' ? null : body === null || body === void 0 ? void 0 : body.tracePassword,
                                tracesTan: body === null || body === void 0 ? void 0 : body.tanNumber,
                                client: client,
                                isaddAtomPro: password_entity_1.IsExistingAtomPro.YES,
                                userId,
                            };
                            let password = null;
                            const passwordCheck = await password_entity_1.default.findOne({ where: { client, website: details === null || details === void 0 ? void 0 : details.website }, order: { createdAt: 'DESC' } });
                            if (passwordCheck) {
                                password = await (0, atomProReUse_1.updateClientCredentials)(details, passwordCheck === null || passwordCheck === void 0 ? void 0 : passwordCheck.id);
                            }
                            else {
                                password = await (0, atomProReUse_1.createClientCredentials)(details);
                            }
                            const passwordTraceCheck = await password_entity_1.default.findOne({ where: { client, website: traceDetails === null || traceDetails === void 0 ? void 0 : traceDetails.website }, order: { createdAt: 'DESC' } });
                            if (passwordTraceCheck) {
                                const tracePassword = await (0, atomProReUse_1.updateClientCredentials)(traceDetails, passwordTraceCheck === null || passwordTraceCheck === void 0 ? void 0 : passwordTraceCheck.id);
                            }
                            else {
                                const tracePassword = await (0, atomProReUse_1.createTraceClientCredentials)(traceDetails);
                            }
                            const clientCredentials = new tan_client_credentials_entity_1.default();
                            clientCredentials.tanNumber = body === null || body === void 0 ? void 0 : body.tanNumber;
                            clientCredentials.password = (body === null || body === void 0 ? void 0 : body.password) === "" ? null : body === null || body === void 0 ? void 0 : body.password;
                            clientCredentials.client = client;
                            clientCredentials.organizationId = (_g = user === null || user === void 0 ? void 0 : user.organization) === null || _g === void 0 ? void 0 : _g.id;
                            clientCredentials.passwordId = password === null || password === void 0 ? void 0 : password.id;
                            clientCredentials.status = aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE;
                            clientCredentials.traceUserId = (body === null || body === void 0 ? void 0 : body.traceUserId) === '' ? null : body === null || body === void 0 ? void 0 : body.traceUserId;
                            clientCredentials.tracePassword = (body === null || body === void 0 ? void 0 : body.tracePassword) === '' ? null : body === null || body === void 0 ? void 0 : body.tracePassword;
                            await clientCredentials.save();
                        }
                    }
                    else {
                        throw new common_1.BadRequestException('Maximum Income tax TAN Client Count Reached');
                    }
                }
            }
        }
        catch (error) {
            console.log('Error occur while add the incomeTax tan client credentials', error);
            throw new common_1.InternalServerErrorException(error);
        }
    }
    async updateClientTanCredentials(id, body, userId) {
        let queryRunner = (0, typeorm_1.getConnection)().createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const clientCredential = await queryRunner.manager.findOne(tan_client_credentials_entity_1.default, {
                where: { id },
                relations: ['client'],
            });
            if (clientCredential.passwordId) {
                const password = await queryRunner.manager.findOne(password_entity_1.default, {
                    where: { id: clientCredential.passwordId },
                    relations: ['client'],
                });
                password.password = body.password;
                password['userId'] = userId;
                await queryRunner.manager.save(password);
                const tracePassword = await queryRunner.manager.findOne(password_entity_1.default, {
                    where: { client: password.client, tracesTan: password.loginId },
                    relations: ['client'],
                });
                if (tracePassword) {
                    tracePassword.loginId = body.traceUserId;
                    tracePassword.password = body.tracePassword;
                    tracePassword['userId'] = userId;
                    await queryRunner.manager.save(tracePassword);
                }
                else {
                    throw new common_1.BadRequestException('Traces Not Found with this TAN');
                }
            }
            if (clientCredential) {
                clientCredential.password = body.password;
                clientCredential.traceUserId = body.traceUserId;
                clientCredential.tracePassword = body.tracePassword;
                await queryRunner.manager.save(clientCredential);
                await queryRunner.commitTransaction();
                return clientCredential;
            }
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.log('Error occured while updating the income Tax TAN client credentials', error);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async getAllClients(userId, data) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const entityManager = (0, typeorm_1.getManager)();
        let query = `
    SELECT id, display_name as displayName, status
    FROM client
    WHERE organization_id = ${user === null || user === void 0 ? void 0 : user.organization.id}
    AND status != 'DELETED'
    AND id NOT IN (
        SELECT client_id
        FROM tan_client_credentials
        WHERE organization_id = ${user === null || user === void 0 ? void 0 : user.organization.id}
        AND client_id IS NOT NULL  
    )
    `;
        if (data === null || data === void 0 ? void 0 : data.search) {
            query += ` AND display_name LIKE '%${data === null || data === void 0 ? void 0 : data.search}%'`;
        }
        if (data === null || data === void 0 ? void 0 : data.limit) {
            query += ` LIMIT ${data === null || data === void 0 ? void 0 : data.limit}`;
            if (data === null || data === void 0 ? void 0 : data.page) {
                query += ` OFFSET ${data === null || data === void 0 ? void 0 : data.page}`;
            }
        }
        let clients = await entityManager.query(query);
        return clients;
    }
    async getIncomeTaxProfile(userId, id) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const checkClientCredentials = await tan_client_credentials_entity_1.default.findOne({
                where: { id: id, organizationId: user.organization.id },
            });
            if (checkClientCredentials) {
                const checkStatus = await automation_machines_entity_1.default.findOne({
                    where: { autoCredentials: id, status: 'PENDING' },
                });
                if (checkStatus) {
                }
                const lastCompletedMachine = await automation_machines_entity_1.default.findOne({
                    where: { tanCredentials: id, status: 'COMPLETED', type: 'TAN' },
                    order: {
                        id: 'DESC',
                    },
                });
                const lastCompletedTracesMachine = await automation_machines_entity_1.default.findOne({
                    where: { tanCredentials: id, status: 'COMPLETED', type: 'TRACES' },
                    order: {
                        id: 'DESC',
                    },
                });
                try {
                    const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                        where: { id },
                        relations: ['client'],
                    });
                    if (clientCredential) {
                        const profileDetails = await tan_profile_entity_1.default.findOne({
                            where: { clientId: (_a = clientCredential === null || clientCredential === void 0 ? void 0 : clientCredential.client) === null || _a === void 0 ? void 0 : _a.id },
                        });
                        const keyPersonDetails = await tan_key_person_entity_1.default.find({
                            where: { clientId: (_b = clientCredential === null || clientCredential === void 0 ? void 0 : clientCredential.client) === null || _b === void 0 ? void 0 : _b.id },
                        });
                        return {
                            profileDetails,
                            lastCompletedMachine,
                            checkClientCredentials: true,
                            keyPersonDetails,
                            clientCredential,
                            lastCompletedTracesMachine
                        };
                    }
                }
                catch (error) {
                    console.log('error occured while fetching income tax client credentials', error);
                }
            }
            else {
                {
                    checkClientCredentials;
                }
            }
        }
        catch (error) {
            console.log('error occure while getting getIncomeTaxProfile', error);
        }
    }
    async clientEChallan(userId, query, id) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const { limit, offset, search, assessmentYear, sortValue } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const challanDetails = (0, typeorm_1.createQueryBuilder)(tan_e_challan_entity_1.default, 'tanEchallan')
                    .select([
                    'tanEchallan.id',
                    'tanEchallan.assessmentYear',
                    'tanEchallan.acin',
                    'tanEchallan.minorDesc',
                    'tanEchallan.minorHead',
                    'tanEchallan.totalAmt',
                    'tanEchallan.paymentTime',
                    'tanEchallan.basicTax',
                    'tanEchallan.surCharge',
                    'tanEchallan.eduCess',
                    'tanEchallan.interest',
                    'tanEchallan.penalty',
                    'tanEchallan.others',
                    'tanEchallan.natureOfPayment',
                    'client.id',
                    'client.displayName',
                    'tanClientCredentials.id',
                ])
                    .leftJoin('tanEchallan.tanClientCredentials', 'tanClientCredentials')
                    .leftJoin('tanClientCredentials.client', 'client')
                    .where('tanClientCredentials.id =:id', { id: id });
                challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');
                if (search) {
                    challanDetails.andWhere(new typeorm_1.Brackets((qb) => {
                        qb.where('tanEchallan.acin LIKE :acin', {
                            acin: `%${search}%`,
                        });
                        qb.orWhere('tanEchallan.minorDesc LIKE :minorDesc', {
                            minorDesc: `%${search}%`,
                        });
                        qb.orWhere('tanEchallan.minorHead LIKE :minorHead', {
                            minorHead: `%${search}%`,
                        });
                        qb.orWhere('tanEchallan.natureOfPayment LIKE :natureOfPayment', {
                            natureOfPayment: `%${search}%`,
                        });
                    }));
                }
                if (assessmentYear) {
                    if (assessmentYear === 'null') {
                        challanDetails.andWhere('tanEchallan.assessmentYear = 0');
                    }
                    else {
                        challanDetails.andWhere('tanEchallan.assessmentYear like :as', {
                            as: `%${assessmentYear}%`,
                        });
                    }
                }
                if (sortValue) {
                    switch (sortValue) {
                        case 'AMOUNT_DESC':
                            challanDetails.orderBy('tanEchallan.totalAmt', 'DESC');
                            break;
                        case 'AMOUNT_ASC':
                            challanDetails.orderBy('tanEchallan.totalAmt', 'ASC');
                            break;
                        case 'DATE_NEWEST':
                            challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');
                            break;
                        case 'DATE_OLDEST':
                            challanDetails.orderBy('tanEchallan.paymentTime', 'ASC');
                            break;
                        default:
                            break;
                    }
                }
                const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        id: 'tanEchallan.assessmentYear',
                        minorDesc: 'tanEchallan.minorDesc',
                        natureOfPayment: 'tanEchallan.natureOfPayment',
                        paymentTime: 'tanEchallan.paymentTime',
                        acin: 'tanEchallan.acin',
                        totalAmt: 'tanEchallan.totalAmt'
                    };
                    const column = columnMap[sort.column] || sort.column;
                    challanDetails.orderBy(column, sort.direction.toUpperCase());
                }
                else {
                    challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');
                }
                ;
                if (offset) {
                    challanDetails.skip(offset);
                }
                if (limit) {
                    challanDetails.take(limit);
                }
                let result = await challanDetails.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching income tax client e-Challan in TAN', error);
        }
    }
    async exportTanClientChallan(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        const id = query.incometaxid;
        let clients = await this.clientEChallan(userId, newQuery, id);
        if (!clients.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Challans');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'AY', key: 'ay' },
            { header: 'Type of Payment', key: 'typeOfPayment' },
            { header: 'Nature of Payment', key: 'natureOfPayment' },
            { header: 'Date of Payment', key: 'dateOfPayment' },
            { header: 'Alternate CIN', key: 'cin' },
            { header: 'BSR Code', key: 'bsr' },
            { header: 'Challan #', key: 'challan' },
            { header: 'Tax (₹)', key: 'tax' },
            { header: 'Surcharge (₹)', key: 'surCharge' },
            { header: 'Cess (₹)', key: 'cess' },
            { header: 'Interest (₹)', key: 'interest' },
            { header: 'Penality (₹)', key: 'penalty' },
            { header: 'Others (₹)', key: 'others' },
            { header: 'Total (₹)', key: 'total' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        clients.result.forEach((client) => {
            var _a, _b;
            const rowData = {
                serialNo: serialCounter++,
                ay: client === null || client === void 0 ? void 0 : client.assessmentYear,
                typeOfPayment: client === null || client === void 0 ? void 0 : client.minorDesc,
                dateOfPayment: (0, utils_1.formatDate)(client === null || client === void 0 ? void 0 : client.paymentTime),
                natureOfPayment: client === null || client === void 0 ? void 0 : client.natureOfPayment,
                cin: client === null || client === void 0 ? void 0 : client.acin,
                bsr: (_a = client === null || client === void 0 ? void 0 : client.acin) === null || _a === void 0 ? void 0 : _a.slice(0, 7),
                challan: (_b = client === null || client === void 0 ? void 0 : client.acin) === null || _b === void 0 ? void 0 : _b.toString().slice(-5),
                tax: client === null || client === void 0 ? void 0 : client.basicTax,
                surCharge: client === null || client === void 0 ? void 0 : client.surCharge,
                cess: client === null || client === void 0 ? void 0 : client.eduCess,
                interest: client === null || client === void 0 ? void 0 : client.interest,
                penalty: client === null || client === void 0 ? void 0 : client.penalty,
                others: client === null || client === void 0 ? void 0 : client.others,
                total: client === null || client === void 0 ? void 0 : client.totalAmt,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'displayName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async findEchallan(userId, id) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const incomeTaxEchallan = await tan_e_challan_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
            });
            return incomeTaxEchallan;
        }
        catch (error) {
            console.log('error occur while getting findForm', error);
        }
    }
    async findEchallans(userId, query) {
        var _a;
        try {
            const { limit, offset, assessmentYear, clientCategory, sortValue, search } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let eChallans = (0, typeorm_1.createQueryBuilder)(tan_e_challan_entity_1.default, 'tanEChallan')
                .select([
                'tanEChallan.id',
                'tanEChallan.tan',
                'tanEChallan.assessmentYear',
                'tanEChallan.acin',
                'tanEChallan.minorDesc',
                'tanEChallan.minorHead',
                'tanEChallan.totalAmt',
                'tanEChallan.paymentTime',
                'tanEChallan.basicTax',
                'tanEChallan.surCharge',
                'tanEChallan.eduCess',
                'tanEChallan.interest',
                'tanEChallan.penalty',
                'tanEChallan.others',
                'tanEChallan.natureOfPayment',
                'client.id',
                'client.displayName',
                'tanClientCredentials.id',
                'clientManagers.id'
            ])
                .leftJoin('tanEChallan.tanClientCredentials', 'tanClientCredentials')
                .leftJoin('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('tanEChallan.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    id: 'tanEChallan.assessmentYear',
                    name: 'client.displayName',
                    minorDesc: 'tanEChallan.minorDesc',
                    natureOfPayment: 'tanEChallan.natureOfPayment',
                    paymentTime: 'tanEChallan.paymentTime',
                    acin: 'tanEChallan.acin',
                    totalAmt: 'tanEChallan.totalAmt'
                };
                const column = columnMap[sort.column] || sort.column;
                eChallans.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                eChallans.orderBy('tanEChallan.paymentTime', 'DESC');
            }
            ;
            if (ViewAssigned && !ViewAll) {
                eChallans.andWhere('clientManagers.id = :userId', { userId });
            }
            if (search) {
                eChallans.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('tanEChallan.tan LIKE :pansearch', {
                        pansearch: `%${search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${search}%`,
                    });
                }));
            }
            if (assessmentYear) {
                if (assessmentYear === 'null') {
                    eChallans.andWhere('tanEChallan.assessmentYear = 0');
                }
                else {
                    eChallans.andWhere('tanEChallan.assessmentYear like :as', { as: `%${assessmentYear}%` });
                }
            }
            if (clientCategory) {
                eChallans.andWhere(`tanEChallan.minorDesc like :clientCategory`, {
                    clientCategory: clientCategory,
                });
            }
            if (sortValue) {
                switch (sortValue) {
                    case 'AMOUNT_DESC':
                        eChallans.orderBy('tanEChallan.totalAmt', 'DESC');
                        break;
                    case 'AMOUNT_ASC':
                        eChallans.orderBy('tanEChallan.totalAmt', 'ASC');
                        break;
                    case 'DATE_NEWEST':
                        eChallans.orderBy('tanEChallan.paymentTime', 'DESC');
                        break;
                    case 'DATE_OLDEST':
                        eChallans.orderBy('tanEChallan.paymentTime', 'ASC');
                        break;
                    default:
                        break;
                }
            }
            if (offset >= 0) {
                eChallans.skip(offset);
            }
            if (limit) {
                eChallans.take(limit);
            }
            let result = await eChallans.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occured while fetching getIncomeTaxEChallans in TAN', error);
        }
    }
    async exportTanIncomeTaxChallans(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        let clients = await this.findEchallans(userId, newQuery);
        if (!clients.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Challans');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'AY', key: 'ay' },
            { header: 'Type of Payment', key: 'typeOfPayment' },
            { header: 'Nature of Payment', key: 'natureOfPayment' },
            { header: 'Date of Payment', key: 'dateOfPayment' },
            { header: 'Alternate CIN', key: 'cin' },
            { header: 'BSR Code', key: 'bsr' },
            { header: 'Challan #', key: 'challan' },
            { header: 'Tax (₹)', key: 'tax' },
            { header: 'Surcharge (₹)', key: 'surCharge' },
            { header: 'Cess (₹)', key: 'cess' },
            { header: 'Interest (₹)', key: 'interest' },
            { header: 'Penality (₹)', key: 'penalty' },
            { header: 'Others (₹)', key: 'others' },
            { header: 'Total (₹)', key: 'total' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        clients.result.forEach((client) => {
            var _a, _b;
            const rowData = {
                serialNo: serialCounter++,
                ay: client === null || client === void 0 ? void 0 : client.assessmentYear,
                typeOfPayment: client === null || client === void 0 ? void 0 : client.minorDesc,
                dateOfPayment: (0, utils_1.formatDate)(client === null || client === void 0 ? void 0 : client.paymentTime),
                natureOfPayment: client === null || client === void 0 ? void 0 : client.natureOfPayment,
                cin: client === null || client === void 0 ? void 0 : client.acin,
                bsr: (_a = client === null || client === void 0 ? void 0 : client.acin) === null || _a === void 0 ? void 0 : _a.slice(0, 7),
                challan: (_b = client === null || client === void 0 ? void 0 : client.acin) === null || _b === void 0 ? void 0 : _b.toString().slice(-5),
                tax: client === null || client === void 0 ? void 0 : client.basicTax,
                surCharge: client === null || client === void 0 ? void 0 : client.surCharge,
                cess: client === null || client === void 0 ? void 0 : client.eduCess,
                interest: client === null || client === void 0 ? void 0 : client.interest,
                penalty: client === null || client === void 0 ? void 0 : client.penalty,
                others: client === null || client === void 0 ? void 0 : client.others,
                total: client === null || client === void 0 ? void 0 : client.totalAmt,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'displayName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async findForm(userId, id) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const incomeTaxForm = await tan_income_tax_forms_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['tanClientCredentials', 'tanClientCredentials.tanProfile'],
            });
            let myCaDetails;
            if (incomeTaxForm === null || incomeTaxForm === void 0 ? void 0 : incomeTaxForm.caMembershipNo) {
                myCaDetails = await tan_my_cas_entity_1.default.findOne({
                    where: { caMembershipNum: incomeTaxForm === null || incomeTaxForm === void 0 ? void 0 : incomeTaxForm.caMembershipNo },
                });
            }
            return Object.assign(Object.assign({}, incomeTaxForm), { myCaDetails });
        }
        catch (error) {
            console.log('error occur while getting findForm in TAN', error);
        }
    }
    async getClientform(id, query, userId) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        const { limit, offset } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const formDetails = (0, typeorm_1.createQueryBuilder)(tan_income_tax_forms_entity_1.default, 'tanIncomeTaxForms')
                    .leftJoinAndSelect('tanIncomeTaxForms.tanClientCredentials', 'tanClientCredentials')
                    .leftJoinAndSelect('tanClientCredentials.client', 'client')
                    .where('tanClientCredentials.id =:id', { id: id });
                if (offset) {
                    formDetails.skip(offset);
                }
                if (limit) {
                    formDetails.take(limit);
                }
                const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        id: 'tanIncomeTaxForms.refYear',
                        financialQuarter: 'tanIncomeTaxForms.financialQuarter',
                        formDesc: 'tanIncomeTaxForms.formDesc',
                        ackDt: 'tanIncomeTaxForms.ackDt',
                        tempAckNo: 'tanIncomeTaxForms.tempAckNo',
                        filingTypeCd: 'tanIncomeTaxForms.filingTypeCd',
                    };
                    const column = columnMap[sort.column] || sort.column;
                    formDetails.orderBy(column, sort.direction.toUpperCase());
                }
                else {
                }
                ;
                formDetails.addSelect(`COALESCE(
            STR_TO_DATE(NULLIF(tanIncomeTaxForms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
          )`, 'issueDateOrder');
                formDetails.addOrderBy('issueDateOrder', 'DESC');
                let result = await formDetails.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching income tax client forms in TAN', error);
        }
    }
    async exportTanClientForm(userId, query) {
        const id = query.incometaxid;
        let forms = await this.getClientform(id, query, userId);
        if (!forms.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Forms');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'AY', key: 'ay' },
            { header: 'Filing Type', key: 'filingType' },
            { header: 'Form Name', key: 'formName' },
            { header: 'Acknowledgement #', key: 'acknowledgeNum' },
            { header: 'Date of Filing', key: 'dateOfFiling' },
            { header: 'UDIN', key: 'udin' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        forms.result.forEach((form) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: (0, re_use_1.calculateAssessmentYear)(form === null || form === void 0 ? void 0 : form.refYear),
                ay: (0, re_use_1.calculateAdvanceYr)(form === null || form === void 0 ? void 0 : form.refYear),
                filingType: form === null || form === void 0 ? void 0 : form.filingTypeCd,
                formName: form === null || form === void 0 ? void 0 : form.formDesc,
                acknowledgeNum: form === null || form === void 0 ? void 0 : form.ackNum,
                dateOfFiling: form === null || form === void 0 ? void 0 : form.ackDt,
                udin: form === null || form === void 0 ? void 0 : form.udinNum,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async findAllForms(userId, query) {
        var _a;
        try {
            const { limit, offset } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let forms = (0, typeorm_1.createQueryBuilder)(tan_income_tax_forms_entity_1.default, 'forms')
                .leftJoinAndSelect('forms.tanClientCredentials', 'tanClientCredentials')
                .leftJoinAndSelect('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('forms.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            if (ViewAssigned && !ViewAll) {
                forms.andWhere('clientManagers.id = :userId', { userId });
            }
            if (query.search) {
                forms.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
                forms.orWhere('tanClientCredentials.tanNumber LIKE :search', {
                    search: `%${query.search}%`,
                });
            }
            if (query.formCode) {
                forms.andWhere('forms.formDesc = :formCd', {
                    formCd: query.formCode,
                });
            }
            if (query.filingType) {
                if (query.filingType === 'Original/Regular') {
                    forms.andWhere('forms.filingTypeCd IN (:...filingType)', {
                        filingType: ['Original', 'Regular'],
                    });
                }
                else if (query.filingType === 'Revised/Correction') {
                    forms.andWhere('forms.filingTypeCd IN (:...filingType)', {
                        filingType: ['Revised', 'Correction'],
                    });
                }
            }
            if (query.financialQuarter) {
                if (query.financialQuarter === "NA") {
                    forms.andWhere('forms.financialQuarter = :finQ', {
                        finQ: 'Not applicable'
                    });
                }
                else {
                    forms.andWhere('forms.financialQuarter = :finQuarter', {
                        finQuarter: query.financialQuarter,
                    });
                }
            }
            if (query.financialYear) {
                forms.andWhere('forms.refYear = :finY AND forms.refYearType = :financialYearType', {
                    finY: query.financialYear,
                    financialYearType: 'FY',
                });
            }
            if (query.clientCategory) {
                forms.andWhere(`client.category = :clientCategory`, {
                    clientCategory: query.clientCategory,
                });
            }
            if (query.udinStat) {
                switch (query.udinStat) {
                    case 'UDIN_APPLICABLE':
                        forms.andWhere('forms.isUdinApplicable is true');
                        break;
                    case 'UDIN_NOT_APPLICABLE':
                        forms.andWhere('forms.isUdinApplicable is false');
                        break;
                    case 'UDIN_COMPLETED':
                        forms.andWhere('forms.udinNum is not null');
                        break;
                    case 'UDIN_PENDING':
                        forms.andWhere('forms.udinNum is null').andWhere('forms.isUdinApplicable is true');
                    default:
                        break;
                }
            }
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    id: 'forms.refYear',
                    financialQuarter: 'forms.financialQuarter',
                    forms: 'client.displayName',
                    formDesc: 'forms.formDesc',
                    ackDt: 'forms.ackDt',
                    tempAckNo: 'forms.tempAckNo',
                    filingTypeCd: 'forms.filingTypeCd',
                };
                const column = columnMap[sort.column] || sort.column;
                forms.orderBy(column, sort.direction.toUpperCase());
            }
            else {
            }
            ;
            if (offset >= 0) {
                forms.skip(offset);
            }
            if (limit) {
                forms.take(limit);
            }
            forms.addSelect(`COALESCE(
          STR_TO_DATE(NULLIF(forms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
        )`, 'issueDateOrder');
            forms.addOrderBy('issueDateOrder', 'DESC');
            let result = await forms.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occur while getting findAll', error);
        }
    }
    async exportTanIncomeTaxForms(userId, query) {
        const id = query.incometaxid;
        let forms = await this.findAllForms(userId, query);
        if (!forms.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Forms');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'AY', key: 'ay' },
            { header: 'Filing Type', key: 'filingType' },
            { header: 'Form Name', key: 'formName' },
            { header: 'Acknowledgement #', key: 'acknowledgeNum' },
            { header: 'Date of Filing', key: 'dateOfFiling' },
            { header: 'UDIN', key: 'udin' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        forms.result.forEach((form) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: (0, re_use_1.calculateAssessmentYear)(form === null || form === void 0 ? void 0 : form.refYear),
                ay: (0, re_use_1.calculateAdvanceYr)(form === null || form === void 0 ? void 0 : form.refYear),
                filingType: form === null || form === void 0 ? void 0 : form.filingTypeCd,
                formName: form === null || form === void 0 ? void 0 : form.formDesc,
                acknowledgeNum: form === null || form === void 0 ? void 0 : form.ackNum,
                dateOfFiling: form === null || form === void 0 ? void 0 : form.ackDt,
                udin: form === null || form === void 0 ? void 0 : form.udinNum,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getActivityLogData(id, query, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const autActivity = await (0, typeorm_1.createQueryBuilder)(automation_machines_entity_1.default, 'autActivity')
                    .leftJoinAndSelect('autActivity.user', 'user')
                    .where('autActivity.tanCredentials =:id', { id: id });
                autActivity.andWhere('autActivity.type =:type', { type: 'TAN' });
                if (query.fromDate && query.toDate) {
                    const { startTime, endTime } = (0, datesFormation_1.dateFormation)(query.fromDate, query.toDate);
                    autActivity
                        .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
                        .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
                }
                autActivity.orderBy('autActivity.id', 'DESC');
                let result = await autActivity.getMany();
                return { result, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.error('Error fetching activity log data:', error);
            return null;
        }
    }
    async getclientReport(userId, query) {
        try {
            const { limit, offset, status, remarks } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const entityManager = (0, typeorm_1.getRepository)(automation_machines_entity_1.default);
            let sql = await entityManager
                .createQueryBuilder('automationMachines')
                .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
                .leftJoinAndSelect('tanCredentials.client', 'client')
                .where('tanCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanCredentials.status != :disStatus', { disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE });
            if (status) {
                sql = sql.andWhere('automationMachines.status = :status', { status });
            }
            if (remarks) {
                sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
            }
            sql = sql
                .andWhere((qb) => {
                const subQuery = qb
                    .subQuery()
                    .select('MAX(innerAutomationMachines.id)', 'maxId')
                    .from(automation_machines_entity_1.default, 'innerAutomationMachines')
                    .leftJoin('innerAutomationMachines.tanCredentials', 'innerTanCredentials')
                    .where('innerTanCredentials.organizationId = :id', { id: user.organization.id })
                    .andWhere('innerAutomationMachines.type = :type', { type: 'TAN' })
                    .groupBy('innerTanCredentials.id')
                    .getQuery();
                return 'automationMachines.id IN ' + subQuery;
            })
                .limit(limit)
                .offset(offset);
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    id: 'client.clientId',
                    displayName: 'client.displayName',
                    tanNumber: 'tanCredentials.tanNumber',
                    status: 'automationMachines.status',
                    remarks: 'automationMachines.remarks',
                    createdAt: 'automationMachines.createdAt'
                };
                const column = columnMap[sort.column] || sort.column;
                sql.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                sql.orderBy('automationMachines.createdAt', 'DESC');
            }
            ;
            const result = await sql.getManyAndCount();
            return {
                data: result[0],
                count: result[1],
            };
        }
        catch (error) {
            console.log('error occur while getting  getclientReport', error);
        }
    }
    async exportTanSyncStatus(userId, query) {
        var _a;
        const id = query.incometaxId;
        let traces = await this.getclientReport(userId, query);
        if (!((_a = traces === null || traces === void 0 ? void 0 : traces.data) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Income Tax (TAN)');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Id', key: 'clientId' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'TAN', key: 'tan' },
            { header: 'Password', key: 'password' },
            { header: 'Status', key: 'status' },
            { header: 'Remarks', key: 'remarks' },
            { header: 'Last Sync', key: 'lastSync' }
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        traces.data.forEach((trace) => {
            var _a, _b, _c, _d, _e, _f;
            const rowData = {
                serialNo: serialCounter++,
                clientId: ((_b = (_a = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.clientId) || '',
                clientName: ((_d = (_c = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.displayName) || '',
                tan: ((_e = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _e === void 0 ? void 0 : _e.tanNumber) || '',
                password: ((_f = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _f === void 0 ? void 0 : _f.password) || " ",
                status: (0, lodash_1.capitalize)(trace === null || trace === void 0 ? void 0 : trace.status) || '',
                remarks: (trace === null || trace === void 0 ? void 0 : trace.remarks) || '',
                lastSync: (trace === null || trace === void 0 ? void 0 : trace.createdAt) ? moment(trace.createdAt).format("DD-MM-YYYY h:mm a") : '',
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status === 'Pending') {
                statusCell.font = {
                    color: { argb: '800080' },
                    bold: true,
                };
            }
            else if (rowData.status === 'Completed') {
                statusCell.font = {
                    color: { argb: '228B22' },
                    bold: true,
                };
            }
            else if (rowData.status === 'Inqueue') {
                statusCell.font = {
                    color: { argb: 'FF8C00' },
                    bold: true,
                };
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportTanIncomeTaxReports(userId, query) {
        let reports = await this.getclientReport(userId, query);
        if (!reports.data.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax Forms');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'TAN', key: 'tan' },
            { header: 'Password', key: 'password' },
            { header: 'Last Sync Time', key: 'lastSync' },
            { header: 'Status', key: 'status' },
            { header: 'Remarks', key: 'remarks' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        reports.data.forEach((report) => {
            var _a, _b, _c, _d;
            const rowData = {
                serialNo: serialCounter++,
                clientName: (_b = (_a = report === null || report === void 0 ? void 0 : report.tanCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName,
                tan: (_c = report === null || report === void 0 ? void 0 : report.tanCredentials) === null || _c === void 0 ? void 0 : _c.tanNumber,
                password: (_d = report === null || report === void 0 ? void 0 : report.tanCredentials) === null || _d === void 0 ? void 0 : _d.password,
                lastSync: (report === null || report === void 0 ? void 0 : report.createdAt) ? moment(report.createdAt).format('DD-MM-YYYY h:mm a') : null,
                status: report === null || report === void 0 ? void 0 : report.status,
                remarks: report === null || report === void 0 ? void 0 : report.remarks,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getclientAutoStatus(id, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredentials = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
            });
            if (clientCredentials) {
                const lastCompletedMachine = await automation_machines_entity_1.default.findOne({
                    where: { tanCredentials: id, type: 'TAN' },
                    order: {
                        id: 'DESC',
                    },
                    relations: ['tanCredentials', 'tanCredentials.client'],
                });
                let totalInqueueCount = 0;
                if ((lastCompletedMachine === null || lastCompletedMachine === void 0 ? void 0 : lastCompletedMachine.status) === 'INQUEUE') {
                    totalInqueueCount = await automation_machines_entity_1.default.count({
                        where: { status: 'INQUEUE', type: 'TAN', id: (0, typeorm_1.LessThan)(lastCompletedMachine.id) },
                    });
                }
                return { lastCompletedMachine, accessDenied: true, totalInqueueCount };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occur while getting getclientAutoStatus', error);
        }
    }
    async findMycas(userId, query) {
        var _a;
        try {
            const { limit, offset, assessmentYear, clientCategory, sortValue, search, filingValue } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let myCasDetails = (0, typeorm_1.createQueryBuilder)(tan_my_cas_entity_1.default, 'tanMycas')
                .select([
                'tanMycas.id',
                'tanMycas.panNumber',
                'tanMycas.caName',
                'tanMycas.caMembershipNum',
                'tanMycas.caStatus',
                'tanMycas.assignedDate',
                'tanMycas.filingType',
                'tanMycas.filingStatus',
                'tanMycas.formTypeCd',
                'tanMycas.isWithdrawable',
                'tanMycas.formStatus',
                'tanMycas.transactionNo',
                'tanMycas.assessmentYear',
                'tanMycas.financialYear',
                'tanMycas.udinNumber',
                'client.id',
                'client.displayName',
                'tanClientCredentials.id',
                'tanClientCredentials.tanNumber',
            ])
                .leftJoin('tanMycas.tanClientCredentials', 'tanClientCredentials')
                .leftJoin('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('tanMycas.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');
            if (search) {
                myCasDetails.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('tanMycas.tanNumber LIKE :panSearch', {
                        panSearch: `%${search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :nameSearch', {
                        nameSearch: `%${search}%`,
                    });
                    qb.orWhere('tanMycas.caName LIKE :caNameSearch', {
                        caNameSearch: `%${search}%`,
                    });
                    qb.orWhere('tanMycas.caMembershipNum LIKE :membershipSearch', {
                        membershipSearch: `%${search}%`,
                    });
                }));
            }
            if (assessmentYear) {
                if (assessmentYear === 'null') {
                    myCasDetails.andWhere('tanMycas.assessmentYear = 0');
                }
                else {
                    myCasDetails.andWhere('tanMycas.assessmentYear like :as', { as: `%${assessmentYear}%` });
                }
            }
            if (ViewAssigned && !ViewAll) {
                myCasDetails.andWhere('clientManagers.id = :userId', { userId });
            }
            if (clientCategory) {
                myCasDetails.andWhere(`tanMycas.formTypeCd like :clientCategory`, {
                    clientCategory: clientCategory,
                });
            }
            if (sortValue) {
                switch (sortValue) {
                    case 'DATE_NEWEST':
                        myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');
                        break;
                    case 'DATE_OLDEST':
                        myCasDetails.orderBy('tanMycas.assignedDate', 'ASC');
                        break;
                    case 'UDIN_COMPLETED':
                        myCasDetails.andWhere('tanMycas.udinNumber is not null');
                        break;
                    case 'UDIN_PENDING':
                        myCasDetails.andWhere('tanMycas.udinNumber is null');
                    default:
                        break;
                }
            }
            if (filingValue) {
                if (filingValue === 'NA') {
                    myCasDetails.andWhere('tanMycas.filingType IS NULL');
                }
                else {
                    myCasDetails.andWhere('tanMycas.filingType LIKE :filingValue', {
                        filingValue: `%${filingValue}%`,
                    });
                }
            }
            if (offset >= 0) {
                myCasDetails.skip(offset);
            }
            if (limit) {
                myCasDetails.take(limit);
            }
            let result = await myCasDetails.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occured while fetching findMycas in TAN', error);
        }
    }
    async exportTanIncomeTaxMycas(userId, query) {
        let mycas = await this.findMycas(userId, query);
        if (!mycas.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax My CA');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'AY', key: 'fy' },
            { header: 'CA Name', key: 'caName' },
            { header: 'CA Membership #', key: 'caMembership' },
            { header: 'Filing Type', key: 'filingType' },
            { header: 'Form Name', key: 'formName' },
            { header: 'Assigned Date', key: 'assaignedDate' },
            { header: 'Transaction ID', key: 'transactionId' },
            { header: 'UDIN', key: 'udin' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        mycas.result.forEach((myca) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: (0, re_use_1.calculateAssessmentYear)(myca === null || myca === void 0 ? void 0 : myca.refYear),
                caName: myca === null || myca === void 0 ? void 0 : myca.caName,
                caMembership: myca === null || myca === void 0 ? void 0 : myca.caMembershipNum,
                filingType: myca === null || myca === void 0 ? void 0 : myca.filingType,
                formName: myca === null || myca === void 0 ? void 0 : myca.formTypeCd,
                assaignedDate: (0, utils_1.formatDate)(myca === null || myca === void 0 ? void 0 : myca.assaignedDate),
                transactionId: myca === null || myca === void 0 ? void 0 : myca.transactionNo,
                udin: myca === null || myca === void 0 ? void 0 : myca.udinNumber,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getMycaFormTypes(userId) {
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const formTypeData = await (0, typeorm_1.createQueryBuilder)(tan_my_cas_entity_1.default, 'tanMyCas')
                .select('tanMyCas.formTypeCd', 'formType')
                .where('tanMyCas.organizationId = :id', { id: user.organization.id })
                .groupBy('tanMyCas.formTypeCd')
                .getRawMany();
            const filteredSections = formTypeData
                .map((section) => section.formType)
                .filter((section) => section !== '' && section !== null);
            return filteredSections;
        }
        catch (error) {
            console.error('Error occurred while fetching form types in TAN:', error);
            throw error;
        }
    }
    async clientMycas(userId, query, id) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const { limit, offset } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const myCasDetails = await (0, typeorm_1.createQueryBuilder)(tan_my_cas_entity_1.default, 'tanMycas')
                    .select([
                    'tanMycas.id',
                    'tanMycas.panNumber',
                    'tanMycas.caName',
                    'tanMycas.caMembershipNum',
                    'tanMycas.caStatus',
                    'tanMycas.assignedDate',
                    'tanMycas.filingType',
                    'tanMycas.filingStatus',
                    'tanMycas.formTypeCd',
                    'tanMycas.isWithdrawable',
                    'tanMycas.formStatus',
                    'tanMycas.transactionNo',
                    'tanMycas.assessmentYear',
                    'tanMycas.financialYear',
                    'tanMycas.udinNumber',
                    'client.id',
                    'client.displayName',
                    'tanClientCredentials.id',
                ])
                    .leftJoin('tanMycas.tanClientCredentials', 'tanClientCredentials')
                    .leftJoin('tanClientCredentials.client', 'client')
                    .where('tanClientCredentials.id =:id', { id: id });
                myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');
                if (offset) {
                    myCasDetails.skip(offset);
                }
                if (limit) {
                    myCasDetails.take(limit);
                }
                let result = await myCasDetails.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching income tax client clientMycas in TAN', error);
        }
    }
    async exportTanClientMyCas(userId, query) {
        const id = query.incometaxid;
        let mycas = await this.clientMycas(userId, query, id);
        if (!mycas.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inc Tax My CA');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'CA Name', key: 'caName' },
            { header: 'CA Membership #', key: 'caMembership' },
            { header: 'Filing Type', key: 'filingType' },
            { header: 'Form Name', key: 'formName' },
            { header: 'Assigned Date', key: 'assaignedDate' },
            { header: 'Transaction ID', key: 'transactionId' },
            { header: 'UDIN', key: 'udin' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        mycas.result.forEach((myca) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: (0, re_use_1.calculateAssessmentYear)(myca === null || myca === void 0 ? void 0 : myca.refYear),
                caName: myca === null || myca === void 0 ? void 0 : myca.caName,
                caMembership: myca === null || myca === void 0 ? void 0 : myca.caMembershipNum,
                filingType: myca === null || myca === void 0 ? void 0 : myca.filingType,
                formName: myca === null || myca === void 0 ? void 0 : myca.formTypeCd,
                assaignedDate: (0, utils_1.formatDate)(myca === null || myca === void 0 ? void 0 : myca.assaignedDate),
                transactionId: myca === null || myca === void 0 ? void 0 : myca.transactionNo,
                udin: myca === null || myca === void 0 ? void 0 : myca.udinNumber,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'taskName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getClientTraceCommunications(id, query, userId) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const { limit, offset } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const traceData = (0, typeorm_1.createQueryBuilder)(tan_communication_inbox_entity_1.default, 'tanTraces')
                    .leftJoinAndSelect('tanTraces.tanClientCredentials', 'tanClientCredentials')
                    .leftJoinAndSelect('tanClientCredentials.client', 'client')
                    .where('tanClientCredentials.id =:id', { id: id });
                const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        date: 'tanTraces.date',
                        id: 'tanTraces.id',
                        qt: 'tanTraces.qt',
                        formType: 'tanTraces.formType',
                        commCat: 'tanTraces.commCat',
                        description: 'tanTraces.description',
                        type: 'tanTraces.type',
                    };
                    const column = columnMap[sort.column] || sort.column;
                    traceData.orderBy(column, sort.direction.toUpperCase());
                }
                else {
                }
                ;
                if (offset) {
                    traceData.skip(offset);
                }
                if (limit) {
                    traceData.take(limit);
                }
                traceData.addSelect(`COALESCE(
            STR_TO_DATE(NULLIF(tanTraces.date, 'NA'), '%d-%b-%Y'),'0000-01-01'		
          )`, 'issueDateOrder');
                traceData.addOrderBy('issueDateOrder', 'DESC');
                let result = await traceData.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching client Trace Communication data in TAN', error);
        }
    }
    async exportClientTanTracesInbox(userId, query) {
        var _a;
        const id = query.incometaxId;
        let traces = await this.getClientTraceCommunications(id, query, userId);
        if (!((_a = traces === null || traces === void 0 ? void 0 : traces.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Traces Inbox');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Date', key: 'date' },
            { header: 'Reference ID', key: 'referenceID' },
            { header: 'FY', key: 'fy' },
            { header: 'Quarter', key: 'quarter' },
            { header: 'Form Type', key: 'formType' },
            { header: 'Category', key: 'category' },
            { header: 'Description', key: 'description' },
            { header: 'Action', key: 'type' }
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        traces.result.forEach((trace) => {
            const rowData = {
                serialNo: serialCounter++,
                date: (trace === null || trace === void 0 ? void 0 : trace.date) || '',
                referenceID: (trace === null || trace === void 0 ? void 0 : trace.commRefNo) || '',
                fy: (trace === null || trace === void 0 ? void 0 : trace.fy) || '',
                quarter: (trace === null || trace === void 0 ? void 0 : trace.qt) || '',
                formType: (trace === null || trace === void 0 ? void 0 : trace.formType) || '',
                category: (trace === null || trace === void 0 ? void 0 : trace.commCat) || '',
                description: (trace === null || trace === void 0 ? void 0 : trace.description) || '',
                type: (trace === null || trace === void 0 ? void 0 : trace.type) || '',
            };
            const row = worksheet.addRow(rowData);
            const typeCell = row.getCell('type');
            if (rowData.type === 'Required') {
                typeCell.font = {
                    color: { argb: '800080' },
                    bold: true,
                };
            }
            else if (rowData.type === 'Not Required') {
                typeCell.font = {
                    color: { argb: '008080' },
                    bold: true,
                };
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'category' || column.key === 'description') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async findAllTraceCommunication(userId, query) {
        var _a;
        try {
            const { limit, offset } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let tracesData = (0, typeorm_1.createQueryBuilder)(tan_communication_inbox_entity_1.default, 'tanTraces')
                .leftJoinAndSelect('tanTraces.tanClientCredentials', 'tanClientCredentials')
                .leftJoinAndSelect('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('tanTraces.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            if (query.search) {
                tracesData.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
                tracesData.orWhere('tanClientCredentials.tanNumber LIKE :search', {
                    search: `%${query.search}%`,
                });
            }
            if (ViewAssigned && !ViewAll) {
                tracesData.andWhere('clientManagers.id = :userId', { userId });
            }
            if (query.formCode) {
                tracesData.andWhere('tanTraces.formType = :formCd', {
                    formCd: query.formCode,
                });
            }
            if (query.filingType) {
                tracesData.andWhere('tanTraces.type = :filingType', {
                    filingType: query.filingType,
                });
            }
            if (query.financialQuarter) {
                tracesData.andWhere('tanTraces.qt = :finQuarter', {
                    finQuarter: query.financialQuarter,
                });
            }
            if (query.financialYear) {
                tracesData.andWhere('tanTraces.fy = :finY', {
                    finY: query.financialYear,
                });
            }
            ;
            if (query.clientCategory) {
                tracesData.andWhere(`client.category = :clientCategory`, {
                    clientCategory: query.clientCategory,
                });
            }
            if (query.interval) {
                const today = moment().format('DD-MMM-YYYY');
                let fromDate;
                if (query.interval === 'last1week') {
                    fromDate = moment().subtract(7, 'days').format('DD-MMM-YYYY');
                }
                else if (query.interval === 'last15days') {
                    fromDate = moment().subtract(15, 'days').format('DD-MMM-YYYY');
                }
                else if (query.interval === 'last1month') {
                    fromDate = moment().subtract(30, 'days').format('DD-MMM-YYYY');
                }
                tracesData.andWhere('STR_TO_DATE(tanTraces.date, "%d-%b-%Y") BETWEEN STR_TO_DATE(:fromDate, "%d-%b-%Y") AND STR_TO_DATE(:today, "%d-%b-%Y")', {
                    fromDate,
                    today,
                });
            }
            if (offset >= 0) {
                tracesData.skip(offset);
            }
            if (limit) {
                tracesData.take(limit);
            }
            tracesData.addSelect(`COALESCE(
          STR_TO_DATE(NULLIF(tanTraces.date, 'NA'), '%d-%b-%Y'),'0000-01-01'		
        )`, 'issueDateOrder');
            tracesData.addOrderBy('issueDateOrder', 'DESC');
            let result = await tracesData.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occur while getting findAll tan traces communiaction', error);
        }
    }
    async exportClientTracesInbox(userId, query) {
        let traces = await this.findAllTraceCommunication(userId, query);
        if (!traces.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Traces Inbox');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'TAN', key: 'tan' },
            { header: 'Date', key: 'date' },
            { header: 'Reference ID', key: 'referenceID' },
            { header: 'FY', key: 'fy' },
            { header: 'Quarter', key: 'quarter' },
            { header: 'Form Type', key: 'formType' },
            { header: 'Category', key: 'category' },
            { header: 'Description', key: 'description' },
            { header: 'Action', key: 'type' }
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        traces.result.forEach((trace) => {
            var _a, _b, _c;
            const rowData = {
                serialNo: serialCounter++,
                clientName: ((_b = (_a = trace === null || trace === void 0 ? void 0 : trace.tanClientCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName) || '',
                tan: ((_c = trace === null || trace === void 0 ? void 0 : trace.tanClientCredentials) === null || _c === void 0 ? void 0 : _c.tanNumber) || '',
                date: (trace === null || trace === void 0 ? void 0 : trace.date) || '',
                referenceID: (trace === null || trace === void 0 ? void 0 : trace.commRefNo) || '',
                fy: (trace === null || trace === void 0 ? void 0 : trace.fy) || '',
                quarter: (trace === null || trace === void 0 ? void 0 : trace.qt) || '',
                formType: (trace === null || trace === void 0 ? void 0 : trace.formType) || '',
                category: (trace === null || trace === void 0 ? void 0 : trace.commCat) || '',
                description: (trace === null || trace === void 0 ? void 0 : trace.description) || '',
                type: (trace === null || trace === void 0 ? void 0 : trace.type) || '',
            };
            const row = worksheet.addRow(rowData);
            const typeCell = row.getCell('type');
            if (rowData.type === 'Required') {
                typeCell.font = {
                    color: { argb: '800080' },
                    bold: true,
                };
            }
            else if (rowData.type === 'Not Required') {
                typeCell.font = {
                    color: { argb: '008080' },
                    bold: true,
                };
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName' || column.key === 'category' || column.key === 'description') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getActivityLogTracesData(id, query, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const autActivity = await (0, typeorm_1.createQueryBuilder)(automation_machines_entity_1.default, 'autActivity')
                    .leftJoinAndSelect('autActivity.user', 'user')
                    .where('autActivity.tanCredentials =:id', { id: id });
                autActivity.andWhere('autActivity.type =:type', { type: 'TRACES' });
                if (query.fromDate && query.toDate) {
                    const { startTime, endTime } = (0, datesFormation_1.dateFormation)(query.fromDate, query.toDate);
                    autActivity
                        .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
                        .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
                }
                autActivity.orderBy('autActivity.id', 'DESC');
                let result = await autActivity.getMany();
                return { result, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.error('Error fetching activity log data:', error);
            return null;
        }
    }
    async getclientAutoTracesStatus(id, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredentials = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
            });
            if (clientCredentials) {
                const lastCompletedMachine = await automation_machines_entity_1.default.findOne({
                    where: { tanCredentials: id, type: 'TRACES' },
                    order: {
                        id: 'DESC',
                    },
                    relations: ['tanCredentials', 'tanCredentials.client'],
                });
                let totalInqueueCount = 0;
                if ((lastCompletedMachine === null || lastCompletedMachine === void 0 ? void 0 : lastCompletedMachine.status) === 'INQUEUE') {
                    totalInqueueCount = await automation_machines_entity_1.default.count({
                        where: { status: 'INQUEUE', type: 'TRACES', id: (0, typeorm_1.LessThan)(lastCompletedMachine.id) },
                    });
                }
                return { lastCompletedMachine, accessDenied: true, totalInqueueCount };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occur while getting getclientAutoStatus', error);
        }
    }
    async getTraceReport(userId, query) {
        try {
            const { limit, offset, status, remarks } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            const entityManager = (0, typeorm_1.getRepository)(automation_machines_entity_1.default);
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let sql = await entityManager
                .createQueryBuilder('automationMachines')
                .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
                .leftJoinAndSelect('tanCredentials.client', 'client')
                .where('tanCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere('automationMachines.type = :type', { type: 'TRACES' })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanCredentials.status != :disStatus', { disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE });
            if (status) {
                sql = sql.andWhere('automationMachines.status = :status', { status });
            }
            if (remarks) {
                sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
            }
            sql = sql
                .andWhere((qb) => {
                const subQuery = qb
                    .subQuery()
                    .select('MAX(innerAutomationMachines.id)', 'maxId')
                    .from(automation_machines_entity_1.default, 'innerAutomationMachines')
                    .leftJoin('innerAutomationMachines.tanCredentials', 'innerTanCredentials')
                    .where('innerTanCredentials.organizationId = :id', { id: user.organization.id })
                    .andWhere('innerAutomationMachines.type = :type', { type: 'TRACES' })
                    .groupBy('innerTanCredentials.id')
                    .getQuery();
                return 'automationMachines.id IN ' + subQuery;
            })
                .limit(limit)
                .offset(offset);
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    id: 'client.clientId',
                    displayName: 'client.displayName',
                    tanNumber: 'tanCredentials.tanNumber',
                    status: 'automationMachines.status',
                    remarks: 'automationMachines.remarks',
                    createdAt: 'automationMachines.createdAt'
                };
                const column = columnMap[sort.column] || sort.column;
                sql.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                sql.orderBy('automationMachines.createdAt', 'DESC');
            }
            ;
            const result = await sql.getManyAndCount();
            return {
                data: result[0],
                count: result[1],
            };
        }
        catch (error) {
            console.log('error occur while getting  getclientReport', error);
        }
    }
    async exportTanTraceSyncStatus(userId, query) {
        var _a;
        const id = query.incometaxId;
        let traces = await this.getTraceReport(userId, query);
        if (!((_a = traces === null || traces === void 0 ? void 0 : traces.data) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Traces');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Id', key: 'clientId' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Traces User Id', key: 'traceUserId' },
            { header: 'Password', key: 'password' },
            { header: 'TAN', key: 'tan' },
            { header: 'Status', key: 'status' },
            { header: 'Remarks', key: 'remarks' },
            { header: 'Last Sync', key: 'lastSync' }
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        traces.data.forEach((trace) => {
            var _a, _b, _c, _d, _e, _f, _g;
            const rowData = {
                serialNo: serialCounter++,
                clientId: ((_b = (_a = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.clientId) || '',
                clientName: ((_d = (_c = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.displayName) || '',
                traceUserId: ((_e = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _e === void 0 ? void 0 : _e.traceUserId) || " ",
                tan: ((_f = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _f === void 0 ? void 0 : _f.tanNumber) || '',
                password: ((_g = trace === null || trace === void 0 ? void 0 : trace.tanCredentials) === null || _g === void 0 ? void 0 : _g.password) || " ",
                status: (0, lodash_1.capitalize)(trace === null || trace === void 0 ? void 0 : trace.status) || '',
                remarks: (trace === null || trace === void 0 ? void 0 : trace.remarks) || '',
                lastSync: (trace === null || trace === void 0 ? void 0 : trace.createdAt) ? moment(trace.createdAt).format("DD-MM-YYYY h:mm a") : '',
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status === 'Pending') {
                statusCell.font = {
                    color: { argb: '800080' },
                    bold: true,
                };
            }
            else if (rowData.status === 'Completed') {
                statusCell.font = {
                    color: { argb: '228B22' },
                    bold: true,
                };
            }
            else if (rowData.status === 'Inqueue') {
                statusCell.font = {
                    color: { argb: 'FF8C00' },
                    bold: true,
                };
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName' || column.key === 'category' || column.key === 'description') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async findFyaTempNotices(userId, query) {
        var _a;
        try {
            const { limit, offset, fromDate, toDate } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            let epro = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
                .leftJoinAndSelect('tanTempEproFya.client', 'client')
                .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
                .where('tanTempEproFya.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            if (fromDate && toDate) {
                const formattedFromDate = new Date(fromDate)
                    .toLocaleDateString('en-GB')
                    .split('/')
                    .join('-');
                const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');
                epro.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where("STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFya.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFya.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFya.proceedingConcludedDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                }));
            }
            if (query.search) {
                epro.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('tanTempEproFya.pan LIKE :pansearch', {
                        pansearch: `%${query.search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                    qb.orWhere('tanTempEproFya.proceedingName LIKE :prosearch', {
                        prosearch: `%${query.search}%`,
                    });
                }));
            }
            if (query.section) {
                if (query.section === 'null') {
                    epro.andWhere('tanTempEproFya.noticeSection is NULL');
                }
                else {
                    epro.andWhere('tanTempEproFya.noticeSection like :sectionsearch', {
                        sectionsearch: `%${query.section}%`,
                    });
                }
            }
            if (query.assessmentYear) {
                if (query.assessmentYear === 'null') {
                    epro.andWhere('tanTempEproFya.ay = 0');
                }
                else {
                    epro.andWhere('tanTempEproFya.ay like :as', {
                        as: `%${query.assessmentYear}%`,
                    });
                }
            }
            if (query.clientCategory) {
                epro.andWhere(`client.category = :clientCategory`, {
                    clientCategory: query.clientCategory,
                });
            }
            if (query.caseStatus) {
                epro.andWhere('tanTempEproFya.proceedingStatus LIKE :caseStatus', {
                    caseStatus: query === null || query === void 0 ? void 0 : query.caseStatus,
                });
            }
            epro
                .addSelect(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
                .orderBy('parsedDate', 'DESC');
            if (offset) {
                epro.skip(offset);
            }
            if (limit) {
                epro.take(limit);
            }
            let result = await epro.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occured while fetching getIncomeTaxEproceedings', error);
        }
    }
    async findFyiTempNotices(userId, query) {
        var _a;
        try {
            const { limit, offset, fromDate, toDate } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            let epro = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
                .leftJoinAndSelect('tanTempEproFyi.client', 'client')
                .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
                .where('tanTempEproFyi.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            if (fromDate && toDate) {
                const formattedFromDate = new Date(fromDate)
                    .toLocaleDateString('en-GB')
                    .split('/')
                    .join('-');
                const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');
                epro.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where("STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFyi.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFyi.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                    qb.orWhere("STR_TO_DATE(tanTempEproFyi.proceedingConcludedDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate });
                }));
            }
            if (query.caseStatus) {
                epro.andWhere('tanTempEproFyi.proceedingStatus LIKE :caseStatus', {
                    caseStatus: query === null || query === void 0 ? void 0 : query.caseStatus,
                });
            }
            if (query.search) {
                epro.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('tanTempEproFyi.pan LIKE :pansearch', {
                        pansearch: `%${query.search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                    qb.orWhere('tanTempEproFyi.proceedingName LIKE :prosearch', {
                        prosearch: `%${query.search}%`,
                    });
                }));
            }
            if (query.section) {
                if (query.section === 'null') {
                    epro.andWhere('tanTempEproFyi.noticeSection is NUll');
                }
                else {
                    epro.andWhere('tanTempEproFyi.noticeSection like :sectionsearch', {
                        sectionsearch: `%${query.section}%`,
                    });
                }
            }
            if (query.assessmentYear) {
                if (query.assessmentYear === 'null') {
                    epro.andWhere('tanTempEproFyi.ay = 0');
                }
                else {
                    epro.andWhere('tanTempEproFyi.ay like :as', {
                        as: `%${query.assessmentYear}%`,
                    });
                }
            }
            if (query.clientCategory) {
                epro.andWhere(`client.category = :clientCategory`, {
                    clientCategory: query.clientCategory,
                });
            }
            epro
                .addSelect(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
                .orderBy('parsedDate', 'DESC');
            if (offset) {
                epro.skip(offset);
            }
            if (limit) {
                epro.take(limit);
            }
            let result = await epro.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occured while fetching getIncomeTaxFyiEproceedings', error);
        }
    }
    async getExcelFyaSections(userId) {
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const sectionsData = await (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
                .select('tanTempEproFya.noticeSection', 'noticeSection')
                .where('tanTempEproFya.organizationId = :id', { id: user.organization.id })
                .groupBy('tanTempEproFya.noticeSection')
                .getRawMany();
            const filteredSections = sectionsData
                .map((section) => section.noticeSection)
                .filter((section) => section !== '' && section !== null && section !== 'null');
            return filteredSections;
        }
        catch (error) {
            console.error('Error occurred while fetching excel-fya-sections:', error);
            throw error;
        }
    }
    async getExcelFyiSections(userId) {
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const sectionsData = await (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
                .select('tanTempEproFyi.noticeSection', 'noticeSection')
                .where('tanTempEproFyi.organizationId = :id', { id: user.organization.id })
                .groupBy('tanTempEproFyi.noticeSection')
                .getRawMany();
            const filteredSections = sectionsData
                .map((section) => section.noticeSection)
                .filter((section) => section !== '' && section !== null && section !== 'null');
            return filteredSections;
        }
        catch (error) {
            console.error('Error occurred while fetching excel-fyi-sections:', error);
            throw error;
        }
    }
    async getClientExcelProceedingFyi(id, query, userId) {
        var _a, _b;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const { limit, offset, fromDate, toDate } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                let clientEpro = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
                    .leftJoinAndSelect('tanTempEproFyi.client', 'client')
                    .where('client.id = :id', { id: (_b = clientCredential === null || clientCredential === void 0 ? void 0 : clientCredential.client) === null || _b === void 0 ? void 0 : _b.id });
                if (fromDate && toDate) {
                    const formattedFromDate = new Date(fromDate)
                        .toLocaleDateString('en-GB')
                        .split('/')
                        .join('-');
                    const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');
                    clientEpro.andWhere(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y') 
                        OR STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`, { fromDate: formattedFromDate, toDate: formattedToDate });
                }
                if (query.caseStatus) {
                    clientEpro.andWhere('tanTempEproFyi.proceedingStatus LIKE :caseStatus', {
                        caseStatus: query === null || query === void 0 ? void 0 : query.caseStatus,
                    });
                }
                if (query.search) {
                    clientEpro.andWhere('tanTempEproFyi.proceedingName LIKE :prosearch', {
                        prosearch: `%${query.search}%`,
                    });
                }
                if (query.section) {
                    if (query.section === 'null') {
                        clientEpro.andWhere('tanTempEproFyi.noticeSection is NULL');
                    }
                    else {
                        clientEpro.andWhere('tanTempEproFyi.noticeSection like :sectionsearch', {
                            sectionsearch: `%${query.section}%`,
                        });
                    }
                }
                if (query.assessmentYear) {
                    if (query.assessmentYear === 'null') {
                        clientEpro.andWhere('tanTempEproFyi.ay = 0');
                    }
                    else {
                        clientEpro.andWhere('tanTempEproFyi.ay like :as', {
                            as: `%${query.assessmentYear}%`,
                        });
                    }
                }
                if (offset >= 0) {
                    clientEpro.skip(offset);
                }
                if (limit) {
                    clientEpro.take(limit);
                }
                clientEpro
                    .addSelect(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
                    .orderBy('parsedDate', 'DESC');
                let result = await clientEpro.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('Error occurred while fetching income tax excel-fyi', error);
        }
    }
    async getClientExcelProceedingFya(id, query, userId) {
        var _a, _b;
        const { limit, offset, fromDate, toDate } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const clientEpro = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
                    .leftJoinAndSelect('tanTempEproFya.client', 'client')
                    .where('client.id =:id', { id: (_b = clientCredential === null || clientCredential === void 0 ? void 0 : clientCredential.client) === null || _b === void 0 ? void 0 : _b.id });
                clientEpro
                    .addSelect(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
                    .orderBy('parsedDate', 'DESC');
                if (fromDate && toDate) {
                    const formattedFromDate = new Date(fromDate)
                        .toLocaleDateString('en-GB')
                        .split('/')
                        .join('-');
                    const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');
                    clientEpro.andWhere(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y') 
                        OR STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`, { fromDate: formattedFromDate, toDate: formattedToDate });
                }
                if (query.caseStatus) {
                    clientEpro.andWhere('tanTempEproFya.proceedingStatus LIKE :caseStatus', {
                        caseStatus: query === null || query === void 0 ? void 0 : query.caseStatus,
                    });
                }
                if (query.search) {
                    clientEpro.andWhere('tanTempEproFya.proceedingName LIKE :prosearch', {
                        prosearch: `%${query.search}%`,
                    });
                }
                if (query.section) {
                    if (query.section === 'null') {
                        clientEpro.andWhere('tanTempEproFya.noticeSection is NULL');
                    }
                    else {
                        clientEpro.andWhere('tanTempEproFya.noticeSection like :sectionsearch', {
                            sectionsearch: `%${query.section}%`,
                        });
                    }
                }
                if (query.assessmentYear) {
                    if (query.assessmentYear === 'null') {
                        clientEpro.andWhere('tanTempEproFya.ay = 0');
                    }
                    else {
                        clientEpro.andWhere('tanTempEproFya.ay like :as', {
                            as: `%${query.assessmentYear}%`,
                        });
                    }
                }
                if (offset >= 0) {
                    clientEpro.skip(offset);
                }
                if (limit) {
                    clientEpro.take(limit);
                }
                let result = await clientEpro.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching income tax excel-fya', error);
        }
    }
    async getExcelCombinedNotices(userId, query) {
        var _a;
        try {
            const { limit, offset, search, interval, column, assessmentYear, sort } = query;
            const userRepository = (0, typeorm_1.getRepository)(user_entity_1.User);
            const user = await userRepository.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            if (!((_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id)) {
                return { count: 0, result: [] };
            }
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const organizationId = user.organization.id;
            let fyaNoticeQuery = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
                .leftJoinAndSelect('tanTempEproFya.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoin('client.tanClientCredentials', 'tanClientCredentials')
                .where('tanTempEproFya.organizationId = :id', { id: organizationId })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :status', { status: "DISABLE" })
                .addSelect("'FYA'", 'eProType');
            let fyiNoticeQuery = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
                .leftJoinAndSelect('tanTempEproFyi.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoin('client.tanClientCredentials', 'tanClientCredentials')
                .where('tanTempEproFyi.organizationId = :id', { id: organizationId })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :status', { status: "DISABLE" })
                .addSelect("'FYI'", 'eProType');
            if (search) {
                fyaNoticeQuery.andWhere('client.displayName LIKE :search OR tanTempEproFya.noticeDin LIKE :search', {
                    search: `%${search}%`,
                });
                fyiNoticeQuery.andWhere('client.displayName LIKE :search OR tanTempEproFyi.noticeDin LIKE :search', {
                    search: `%${search}%`,
                });
            }
            if (ViewAssigned && !ViewAll) {
                fyaNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
            }
            if (ViewAssigned && !ViewAll) {
                fyiNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
            }
            if (assessmentYear) {
                fyaNoticeQuery.andWhere('tanTempEproFya.ay = :assessmentYear', { assessmentYear });
                fyiNoticeQuery.andWhere('tanTempEproFyi.ay = :assessmentYear', { assessmentYear });
            }
            const applyConditions = (table, alias, column) => {
                if (column === 'dateOfCompliance') {
                    if (interval && column) {
                        const now = moment().startOf('day');
                        let futureDate;
                        if (interval === 'today') {
                            futureDate = now.clone().add(1, 'day');
                        }
                        else if (interval === 'last1week') {
                            futureDate = now.clone().add(7, 'days');
                        }
                        else if (interval === 'last15days') {
                            futureDate = now.clone().add(15, 'days');
                        }
                        else if (interval === 'last1month') {
                            futureDate = now.clone().add(1, 'month');
                        }
                        else if (interval === 'last1year') {
                            futureDate = now.clone().add(1, 'year');
                        }
                        if (futureDate) {
                            table.andWhere(`STR_TO_DATE(${alias}.dateOfCompliance, '%d-%m-%Y') BETWEEN :now AND :futureDate`, {
                                now: now.format('YYYY-MM-DD'),
                                futureDate: futureDate.format('YYYY-MM-DD'),
                            });
                        }
                    }
                }
                if (column === 'noticeSentDate') {
                    if (interval && column) {
                        const now = moment().startOf('day');
                        let pastDate;
                        if (interval === 'today') {
                            pastDate = now;
                        }
                        else if (interval === 'last1week') {
                            pastDate = now.clone().subtract(7, 'days');
                        }
                        else if (interval === 'last15days') {
                            pastDate = now.clone().subtract(15, 'days');
                        }
                        else if (interval === 'last1month') {
                            pastDate = now.clone().subtract(1, 'month');
                        }
                        else if (interval === 'last1year') {
                            pastDate = now.clone().subtract(1, 'year');
                        }
                        if (pastDate) {
                            table.andWhere(`STR_TO_DATE(${alias}.noticeSentDate, '%d-%m-%Y') BETWEEN :pastDate AND :now`, {
                                pastDate: pastDate.format('YYYY-MM-DD'),
                                now: now.format('YYYY-MM-DD'),
                            });
                        }
                    }
                }
                if (column === 'dateResponseSubmitted') {
                    if (interval && column) {
                        const now = moment().startOf('day');
                        let futureDate;
                        if (interval === 'today') {
                            futureDate = now.add(1, 'day');
                        }
                        else if (interval === 'last1week') {
                            futureDate = now.clone().add(7, 'days');
                        }
                        else if (interval === 'last15days') {
                            futureDate = now.clone().add(15, 'days');
                        }
                        else if (interval === 'last1month') {
                            futureDate = now.clone().add(1, 'month');
                        }
                        else if (interval === 'last1year') {
                            futureDate = now.clone().add(1, 'year');
                        }
                        if (futureDate) {
                            table.andWhere(`STR_TO_DATE(${alias}.dateResponseSubmitted, '%d-%m-%Y') BETWEEN :now AND :futureDate`, {
                                now: now.format('YYYY-MM-DD'),
                                futureDate: futureDate.format('YYYY-MM-DD'),
                            });
                        }
                    }
                }
            };
            const applyFormAndToDateFilter = (table, alias, column) => {
                const fromDate = moment(query.fromDate).startOf('day').format('YYYY-MM-DD');
                const toDate = moment(query.toDate).endOf('day').format('YYYY-MM-DD');
                if (column === 'all') {
                    table.where((qb) => {
                        qb.where(`STR_TO_DATE(${alias}.dateOfCompliance,"%d-%m-%Y") BETWEEN :fromDate AND :toDate`, { fromDate, toDate })
                            .orWhere(`STR_TO_DATE(${alias}.noticeSentDate, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`, { fromDate, toDate })
                            .orWhere(`STR_TO_DATE(${alias}.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`, {
                            fromDate,
                            toDate,
                        });
                    });
                }
                else {
                    if (column) {
                        table.andWhere(`STR_TO_DATE(${alias}.${column},"%d-%m-%Y") BETWEEN :fromDate AND :toDate`, {
                            fromDate,
                            toDate,
                        });
                    }
                }
            };
            if (query.fromDate && query.toDate) {
                applyFormAndToDateFilter(fyaNoticeQuery, 'tanTempEproFya', column);
                applyFormAndToDateFilter(fyiNoticeQuery, 'tanTempEproFyi', column);
            }
            else {
                applyConditions(fyaNoticeQuery, 'tanTempEproFya', column);
                applyConditions(fyiNoticeQuery, 'tanTempEproFyi', column);
            }
            if (column && sort) {
                fyaNoticeQuery
                    .addSelect(`STR_TO_DATE(tanTempEproFya.${sort}, '%d-%m-%Y')`, 'formattedDate')
                    .orderBy('formattedDate', 'DESC');
                fyiNoticeQuery
                    .addSelect(`STR_TO_DATE(tanTempEproFyi.${sort}, '%d-%m-%Y')`, 'formattedDate')
                    .orderBy('formattedDate', 'DESC');
            }
            fyaNoticeQuery.skip(offset).take(limit);
            fyiNoticeQuery.skip(offset).take(limit);
            const [fyaNotices, fyaCount] = await fyaNoticeQuery.getManyAndCount();
            const [fyiNotices, fyiCount] = await fyiNoticeQuery.getManyAndCount();
            const formattedFyaNotices = fyaNotices.map((notice) => (Object.assign(Object.assign({}, notice), { eproType: 'FYA' })));
            const formattedFyiNotices = fyiNotices.map((notice) => (Object.assign(Object.assign({}, notice), { eproType: 'FYI' })));
            return {
                count: fyaCount + fyiCount,
                result: [...formattedFyaNotices, ...formattedFyiNotices],
            };
        }
        catch (error) {
            console.log('error occur while getting getCombinedNotices', error);
        }
    }
    async findAllDemands(userId, query) {
        var _a;
        try {
            const { limit, offset } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let demands = (0, typeorm_1.createQueryBuilder)(trace_outstanding_deman_entity_1.default, 'demands')
                .leftJoinAndSelect('demands.tanClientCredentials', 'tanClientCredentials')
                .leftJoinAndSelect('tanClientCredentials.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .where('demands.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('demands.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere('demands.isInPortal IS NULL');
            }));
            ;
            if (ViewAssigned && !ViewAll) {
                demands.andWhere('clientManagers.id = :userId', { userId });
            }
            if (query.search) {
                demands.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('client.displayName LIKE :search', { search: `%${query.search}%` });
                    qb.orWhere('tanClientCredentials.tanNumber LIKE :tanSearch', { tanSearch: `%${query.search}%` });
                }));
            }
            if (query.formCode) {
                demands.andWhere('demands.formType = :formtyp', {
                    formtyp: query.formCode
                });
            }
            if (query.quarter) {
                demands.andWhere('demands.quarter = :finQuarter', {
                    finQuarter: query.quarter,
                });
            }
            if (query.financialYear) {
                demands.andWhere('demands.financialYear = :finY', {
                    finY: query.financialYear,
                });
            }
            if (query.clientCategory) {
                demands.andWhere(`client.category = :clientCategory`, {
                    clientCategory: query.clientCategory,
                });
            }
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    id: 'demands.id',
                    quarter: 'demands.quarter',
                    name: 'client.displayName',
                    financialYear: 'demands.financialYear',
                    formType: 'demands.formType',
                    netPayableAmount: 'demands.netPayableAmount',
                };
                const column = columnMap[sort.column] || sort.column;
                demands.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                demands.orderBy('demands.financialYear', 'DESC');
            }
            ;
            if (offset >= 0) {
                demands.skip(offset);
            }
            if (limit) {
                demands.take(limit);
            }
            let result = await demands.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error occur while getting findAllDemands in tan', error);
        }
    }
    async findDemand(userId, id) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const tracesDemand = await trace_outstanding_deman_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['tanClientCredentials', 'tanClientCredentials.client'],
            });
            return tracesDemand;
        }
        catch (error) {
            console.log('error occur while getting findForm in TAN', error);
        }
    }
    async getClientDemand(id, query, userId) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        const { limit, offset } = query;
        try {
            const clientCredential = await tan_client_credentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const demandDetails = (0, typeorm_1.createQueryBuilder)(trace_outstanding_deman_entity_1.default, 'demands')
                    .leftJoinAndSelect('demands.tanClientCredentials', 'tanClientCredentials')
                    .leftJoinAndSelect('tanClientCredentials.client', 'client')
                    .where('tanClientCredentials.id =:id', { id: id })
                    .andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('demands.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere('demands.isInPortal IS NULL');
                }));
                if (offset) {
                    demandDetails.skip(offset);
                }
                if (limit) {
                    demandDetails.take(limit);
                }
                const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        financialYear: 'demands.financialYear',
                        quarter: 'demands.quarter',
                        formType: 'demands.formType',
                        netPayableAmount: 'demands.netPayableAmount',
                    };
                    const column = columnMap[sort.column] || sort.column;
                    demandDetails.orderBy(column, sort.direction.toUpperCase());
                }
                else {
                    demandDetails.orderBy('demands.financialYear', 'DESC');
                }
                ;
                let result = await demandDetails.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occured while fetching income tax client demands in TAN', error);
        }
    }
    async exportDemands(userId, query) {
        var _a, _b, _c, _d;
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        const { result: demands } = await this.findAllDemands(userId, newQuery);
        if (!demands.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Category', key: 'category' },
            { header: 'Financal Year', key: 'financialYear' },
            { header: 'Quarter', key: 'quarter' },
            { header: 'Form Type', key: 'formType' },
            { header: 'Net Payable Rounded Off', key: 'netPayableAmount' }
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = ['netPayableAmount'];
        for (const demand of demands) {
            const rowData = {
                serialNo: serialCounter++,
                clientName: ((_b = (_a = demand === null || demand === void 0 ? void 0 : demand.tanClientCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName) || '-',
                category: categoryLabels[(_d = (_c = demand === null || demand === void 0 ? void 0 : demand.tanClientCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.category] || '-',
                financialYear: (demand === null || demand === void 0 ? void 0 : demand.financialYear) || '-',
                quarter: (demand === null || demand === void 0 ? void 0 : demand.quarter) || '-',
                formType: (demand === null || demand === void 0 ? void 0 : demand.formType) || '-',
                netPayableAmount: (demand === null || demand === void 0 ? void 0 : demand.netPayableAmount) || "-"
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientDemand(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        const id = query.incometaxid;
        const { result: demands } = await this.getClientDemand(id, newQuery, userId);
        if (!demands.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Financal Year', key: 'financialYear' },
            { header: 'Quarter', key: 'quarter' },
            { header: 'Form Type', key: 'formType' },
            { header: 'Net Payable Rounded Off', key: 'netPayableAmount' }
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = ['netPayableAmount'];
        for (const demand of demands) {
            const rowData = {
                serialNo: serialCounter++,
                financialYear: (demand === null || demand === void 0 ? void 0 : demand.financialYear) || '-',
                quarter: (demand === null || demand === void 0 ? void 0 : demand.quarter) || '-',
                formType: (demand === null || demand === void 0 ? void 0 : demand.formType) || '-',
                netPayableAmount: (demand === null || demand === void 0 ? void 0 : demand.netPayableAmount) || "-"
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
};
TanAutomationService = __decorate([
    (0, common_1.Injectable)()
], TanAutomationService);
exports.TanAutomationService = TanAutomationService;
//# sourceMappingURL=tan-automation.service.js.map