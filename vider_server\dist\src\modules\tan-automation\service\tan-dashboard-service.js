"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanDashboardService = void 0;
const user_entity_1 = require("../../users/entities/user.entity");
const tan_income_tax_forms_entity_1 = require("../entity/tan-income-tax-forms.entity");
const typeorm_1 = require("typeorm");
const tan_client_credentials_entity_1 = require("../entity/tan-client-credentials.entity");
const client_entity_1 = require("../../clients/entity/client.entity");
const automation_machines_entity_1 = require("../../automation/entities/automation_machines.entity");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
const xlsx = require("xlsx");
const permission_1 = require("../../tasks/permission");
const tan_utils_1 = require("../tan-utils");
const common_1 = require("@nestjs/common");
const moment = require("moment");
const tan_temp_epro_fya_entity_1 = require("../entity/tan_temp_epro_fya.entity");
const tan_temp_epro_fyi_entity_1 = require("../entity/tan_temp_epro_fyi.entity");
const tan_communication_inbox_entity_1 = require("../entity/tan-communication-inbox.entity");
class TanDashboardService {
    async getFormsUdinAnalytics(userId, assesmentYear) {
        var _a;
        const user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const formsData = (0, typeorm_1.createQueryBuilder)(tan_income_tax_forms_entity_1.default, 'tanForms')
            .leftJoin('tanForms.tanClientCredentials', 'tanClientCredentials')
            .leftJoin('tanClientCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .where('tanForms.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (ViewAssigned && !ViewAll) {
            formsData.andWhere('clientManagers.id = :userId', { userId });
        }
        if (assesmentYear) {
            formsData.andWhere('tanForms.refYear = :finY AND tanForms.refYearType = :financialYearType', {
                finY: assesmentYear,
                financialYearType: 'FY',
            });
        }
        const result = await formsData.getMany();
        const analytics = {
            totalForms: result.length,
            originalForms: {
                totalOriginalForms: 0,
                udinCompleted: 0,
                udinPending: 0,
                udinNotApplicable: 0,
            },
            revisedFroms: {
                totalRevisedForms: 0,
                udinCompleted: 0,
                udinPending: 0,
                udinNotApplicable: 0,
            },
            notApplicableForms: {
                totalNotApplicableForms: 0,
                udinCompleted: 0,
                udinPending: 0,
                udinNotApplicable: 0,
            },
        };
        result.forEach((form) => {
            if ((form === null || form === void 0 ? void 0 : form.filingTypeCd) === 'Original' || (form === null || form === void 0 ? void 0 : form.filingTypeCd) === 'Regular') {
                analytics.originalForms.totalOriginalForms++;
                if (form === null || form === void 0 ? void 0 : form.isUdinApplicable) {
                    if (form === null || form === void 0 ? void 0 : form.udinNum) {
                        analytics.originalForms.udinCompleted++;
                    }
                    else {
                        analytics.originalForms.udinPending++;
                    }
                }
                else {
                    analytics.originalForms.udinNotApplicable++;
                }
            }
            else if ((form === null || form === void 0 ? void 0 : form.filingTypeCd) === 'Revised' || (form === null || form === void 0 ? void 0 : form.filingTypeCd) === 'Correction') {
                analytics.revisedFroms.totalRevisedForms++;
                if (form === null || form === void 0 ? void 0 : form.isUdinApplicable) {
                    if (form === null || form === void 0 ? void 0 : form.udinNum) {
                        analytics.revisedFroms.udinCompleted++;
                    }
                    else {
                        analytics.revisedFroms.udinPending++;
                    }
                }
                else {
                    analytics.revisedFroms.udinNotApplicable++;
                }
            }
            else {
                analytics.notApplicableForms.totalNotApplicableForms++;
                if (form === null || form === void 0 ? void 0 : form.isUdinApplicable) {
                    if (form === null || form === void 0 ? void 0 : form.udinNum) {
                        analytics.notApplicableForms.udinCompleted++;
                    }
                    else {
                        analytics.notApplicableForms.udinPending++;
                    }
                }
                else {
                    analytics.notApplicableForms.udinNotApplicable++;
                }
            }
        });
        return analytics;
    }
    async incometaxClientCheck(userId, queryy) {
        var _a, _b, _c;
        const { search, offset, limit } = queryy;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization', 'role'],
        });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const entityManager = (0, typeorm_1.getRepository)(tan_client_credentials_entity_1.default);
        const query = await entityManager
            .createQueryBuilder('tanCredentials')
            .leftJoinAndSelect('tanCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .where('tanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanCredentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('LOWER(tanCredentials.tanRemarks) LIKE :remarks', {
            remarks: 'Invalid Password, Please retry.',
        });
        if (search) {
            query.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('tanCredentials.tan_number LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('client.displayName LIKE :namesearch', {
                    namesearch: `%${search}%`,
                });
            }));
        }
        if (ViewAssigned && !ViewAll) {
            query.andWhere('clientManagers.id = :userId', { userId });
        }
        if (offset) {
            query.skip(offset);
        }
        if (limit) {
            query.take(limit);
        }
        const tracesQuery = await entityManager
            .createQueryBuilder('tanCredentials')
            .leftJoinAndSelect('tanCredentials.client', 'client')
            .where('tanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanCredentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('LOWER(tanCredentials.traceRemarks) LIKE :remarks', {
            remarks: 'Invalid details',
        });
        if (search) {
            tracesQuery.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('tanCredentials.tan_number LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('client.displayName LIKE :namesearch', {
                    namesearch: `%${search}%`,
                });
            }));
        }
        if (offset) {
            tracesQuery.skip(offset);
        }
        if (limit) {
            tracesQuery.take(limit);
        }
        const [filteredRows, totalCount] = await query.getManyAndCount();
        const [filteredRowss, totalTracesCount] = await tracesQuery.getManyAndCount();
        const totalClients = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'credentials')
            .leftJoin('credentials.client', 'client')
            .where('credentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('credentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .getCount();
        const totalClientsWithTraces = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'credentials')
            .leftJoin('credentials.client', 'client')
            .where('credentials.organizationId = :organizationId', {
            organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('credentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('credentials.traceUserId IS NOT NULL')
            .getCount();
        const client = await (0, typeorm_1.createQueryBuilder)(client_entity_1.default, 'client')
            .select('COUNT(DISTINCT client.tan_number)', 'count')
            .where('client.organization_id = :orgId', { orgId: (_c = user.organization) === null || _c === void 0 ? void 0 : _c.id })
            .andWhere('client.tan_number IS NOT NULL')
            .getRawOne();
        const count = parseInt(client.count);
        const result = {
            filteredRows,
            totalClients,
            count: filteredRows.length,
            uniquePansCount: count,
            totalCount,
            totalClientsWithTraces,
            traceCount: filteredRowss.length,
            totalTracesCount,
            totalData: [...filteredRows, ...filteredRowss]
        };
        return result;
    }
    async exportTanInvalid(userId, query) {
        let invalidRows = await this.incometaxClientCheck(userId, query);
        let rows = invalidRows === null || invalidRows === void 0 ? void 0 : invalidRows.filteredRows.map((invalidRow) => {
            var _a, _b, _c, _d, _e, _f, _g;
            return {
                'Category': (_b = (_a = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.tanCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.category,
                'Client Name': (_d = (_c = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.tanCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.displayName,
                'Client ID': (_f = (_e = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.tanCredentials) === null || _e === void 0 ? void 0 : _e.client) === null || _f === void 0 ? void 0 : _f.clientId,
                'TAN': (_g = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.tanCredentials) === null || _g === void 0 ? void 0 : _g.tanNumber,
                'Remarks': invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.remarks,
            };
        });
        if (rows !== undefined && rows.length) {
            const worksheet = xlsx.utils.json_to_sheet(rows);
            const workbook = xlsx.utils.book_new();
            xlsx.utils.book_append_sheet(workbook, worksheet, 'Inc Tax Returns');
            let file = xlsx.write(workbook, { type: 'buffer' });
            return file;
        }
        else {
            throw new common_1.BadRequestException('No Data for Export');
        }
    }
    async getIncometaxConfigStatus(userId, query) {
        var _a, _b;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization'] });
        const organizationPref = await organization_preferences_entity_1.default.findOne({
            where: { organization: user === null || user === void 0 ? void 0 : user.organization },
        });
        if (organizationPref) {
            const organizationLimit = ((_a = organizationPref === null || organizationPref === void 0 ? void 0 : organizationPref.automationConfig) === null || _a === void 0 ? void 0 : _a.tanLimit) || 50;
            let clientCredentials = (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'clientCredentials')
                .leftJoinAndSelect('clientCredentials.client', 'client')
                .where('clientCredentials.organizationId = :id', { id: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('clientCredentials.status IS NULL').orWhere('clientCredentials.status = :enabledStatus', { enabledStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE });
            }));
            let result = (await clientCredentials.getCount()) || 0;
            const abc = {
                totalLimit: organizationLimit,
                difference: organizationLimit - result,
                presentClients: result,
            };
            return abc;
        }
    }
    async getFormsAnalytics(userId, financialYear) {
        var _a;
        const user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const tanClientsQUery = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'tanClientCredentials')
            .leftJoinAndSelect('tanClientCredentials.tanForms', 'tanForms', 'tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :finYear', {
            forms: ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
            filingTypeCd: 'Regular',
            finYear: financialYear,
        })
            .leftJoin('tanClientCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoinAndSelect('tanClientCredentials.tanProfile', 'tanProfile')
            .select([
            'tanClientCredentials.id',
            'tanForms.id',
            'tanForms.formDesc',
            'tanForms.financialQuarter',
            'tanForms.refYear',
            'tanProfile.dateOfAllotment',
            'client.id',
            'tanProfile.tanNumber',
        ])
            .where('tanClientCredentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (ViewAssigned && !ViewAll) {
            tanClientsQUery.andWhere('clientManagers.id = :userId', { userId });
        }
        const tanClients = await tanClientsQUery.getMany();
        const forms = ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];
        const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
        const analytics = {};
        forms.forEach((form) => {
            analytics[form] = { filed: 0, notFiled: 0, byQuarter: {} };
            quarters.forEach((quarter) => {
                analytics[form].byQuarter[quarter] = { filed: 0, notFiled: 0 };
            });
        });
        const financialYearStart = new Date(`${financialYear}-04-01`);
        const financialYearEnd = new Date(`${parseInt(financialYear) + 1}-03-31`);
        const today = new Date();
        const currentYearStart = new Date(`${today.getFullYear}-04-01`);
        const currentQuarter = `Q${Math.floor((today.getMonth() + 1) / 3)}`;
        const currentFinancialYear = today.getMonth() + 1 >= 4
            ? `${today.getFullYear()}-${today.getFullYear() + 1}`
            : `${today.getFullYear() - 1}-${today.getFullYear()}`;
        const currentQuarterIndex = quarters.indexOf(currentQuarter) + 1;
        tanClients.forEach((client) => {
            var _a;
            const dateOfAllotment = (_a = client.tanProfile[0]) === null || _a === void 0 ? void 0 : _a.dateOfAllotment;
            if (!dateOfAllotment)
                return;
            const allotmentDate = new Date(dateOfAllotment);
            const allotmentYear = allotmentDate.getFullYear();
            const allotmentQuarter = parseInt((0, tan_utils_1.getQuarterFromMonth)(allotmentDate.getMonth() + 1));
            let startQuarter = 1;
            let endQuarter = 4;
            if (allotmentDate < financialYearStart) {
                startQuarter = 1;
                endQuarter = 4;
            }
            else if (allotmentDate > financialYearEnd) {
                return;
            }
            else if (allotmentYear === today.getFullYear() && allotmentDate < currentYearStart) {
                startQuarter = allotmentQuarter;
                endQuarter = Math.min(4, currentQuarterIndex - 1);
            }
            else {
                startQuarter = allotmentQuarter;
                endQuarter = 4;
            }
            quarters.slice(startQuarter - 1, endQuarter).forEach((quarter) => {
                if (client.tanForms.length === 0) {
                    forms.forEach((form) => {
                        analytics[form].notFiled++;
                        analytics[form].byQuarter[quarter].notFiled++;
                    });
                }
                else {
                    forms.forEach((form) => {
                        const formExists = client.tanForms.some((tanForm) => tanForm.formDesc === form &&
                            tanForm.financialQuarter === quarter &&
                            tanForm.refYear === financialYear);
                        if (formExists) {
                            analytics[form].filed++;
                            analytics[form].byQuarter[quarter].filed++;
                        }
                        else {
                            analytics[form].notFiled++;
                            analytics[form].byQuarter[quarter].notFiled++;
                        }
                    });
                }
            });
        });
        return analytics;
    }
    async getFormsNavigateAnalytics(userId, query) {
        var _a;
        if (!(query === null || query === void 0 ? void 0 : query.financialYear)) {
            return;
        }
        const { financialYear, financialQuarter, formCd } = query;
        const user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const tanClients = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'tanClientCredentials')
            .leftJoinAndSelect('tanClientCredentials.tanForms', 'tanForms', 'tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :financialYear', {
            forms: formCd ? [formCd] : ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
            filingTypeCd: 'Regular',
            financialYear: query === null || query === void 0 ? void 0 : query.financialYear,
        })
            .leftJoin('tanClientCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoinAndSelect('tanClientCredentials.tanProfile', 'tanProfile')
            .select([
            'tanClientCredentials.id',
            'tanForms.id',
            'tanForms.formDesc',
            'tanForms.financialQuarter',
            'tanForms.refYear',
            'tanForms.ackDt',
            'tanForms.tempAckNo',
            'tanForms.filingTypeCd',
            'tanProfile.dateOfAllotment',
            'client.id',
            'client.displayName',
            'clientManagers.id',
            'tanProfile.tanNumber',
        ])
            .where('tanClientCredentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (ViewAssigned && !ViewAll) {
            tanClients.andWhere('clientManagers.id = :userId', { userId });
        }
        const results = await tanClients.getMany();
        const forms = formCd ? [formCd] : ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];
        const formsData = [];
        const today = new Date();
        const currentQuarterIndex = (0, tan_utils_1.getFinancialQuarter)(today);
        const currentYear = today.getFullYear();
        results.forEach((client) => {
            var _a;
            const dateOfAllotment = (_a = client.tanProfile[0]) === null || _a === void 0 ? void 0 : _a.dateOfAllotment;
            if (!dateOfAllotment)
                return;
            const allotmentDate = new Date(dateOfAllotment);
            let startYear = allotmentDate.getFullYear();
            let startQuarterIndex = parseInt((0, tan_utils_1.getQuarterFromMonth)(allotmentDate.getMonth() + 1));
            if (allotmentDate.getMonth() + 1 < 4) {
                startYear--;
                startQuarterIndex = 4;
            }
            let endQuarterIndex = currentQuarterIndex - 1;
            let endYear = query === null || query === void 0 ? void 0 : query.financialYear;
            if (endQuarterIndex === 0) {
                endQuarterIndex = 4;
                endYear -= 1;
            }
            const validQuarters = (0, tan_utils_1.getQuartersBetweenFinancialYear)(allotmentDate, parseInt(financialYear), currentYear, currentQuarterIndex);
            validQuarters.forEach(({ year, quarter }) => {
                forms.forEach((form) => {
                    var _a, _b, _c, _d, _e, _f;
                    const formExists = client.tanForms.some((tanForm) => tanForm.formDesc === form &&
                        tanForm.refYear === year.toString() &&
                        tanForm.financialQuarter === quarter &&
                        tanForm.filingTypeCd === 'Regular');
                    if (formExists) {
                        const filedForm = client.tanForms.find((tanForm) => tanForm.formDesc === form &&
                            tanForm.refYear === year.toString() &&
                            tanForm.financialQuarter === quarter);
                        const filedRecord = {
                            tanClientId: client.id,
                            tanNumber: (_a = client.tanProfile[0]) === null || _a === void 0 ? void 0 : _a.tanNumber,
                            clientName: (_b = client === null || client === void 0 ? void 0 : client.client) === null || _b === void 0 ? void 0 : _b.displayName,
                            dateOfAllotment: (_c = client.tanProfile[0]) === null || _c === void 0 ? void 0 : _c.dateOfAllotment,
                            formDesc: form,
                            financialQuarter: quarter,
                            refYear: year,
                            ackDt: filedForm === null || filedForm === void 0 ? void 0 : filedForm.ackDt,
                            tempAckNo: filedForm === null || filedForm === void 0 ? void 0 : filedForm.tempAckNo,
                            filingStatus: 'Filed',
                        };
                        formsData.push(filedRecord);
                    }
                    else {
                        const notFiledRecord = {
                            tanClientId: client.id,
                            formDesc: form,
                            financialQuarter: quarter,
                            refYear: year,
                            tanNumber: (_d = client.tanProfile[0]) === null || _d === void 0 ? void 0 : _d.tanNumber,
                            clientName: (_e = client === null || client === void 0 ? void 0 : client.client) === null || _e === void 0 ? void 0 : _e.displayName,
                            dateOfAllotment: (_f = client.tanProfile[0]) === null || _f === void 0 ? void 0 : _f.dateOfAllotment,
                            ackDt: null,
                            tempAckNo: null,
                            filingStatus: 'Not Filed',
                        };
                        formsData.push(notFiledRecord);
                    }
                });
            });
        });
        const filteredData = formsData.filter((record) => {
            var _a, _b;
            const matchesFormCd = !query.formCd || record.formDesc.includes(query.formCd);
            const matchesFinancialQuarter = !query.financialQuarter || record.financialQuarter === query.financialQuarter;
            const matchesFinancialYear = !query.financialYear || record.refYear.toString() === query.financialYear;
            const matchesFilingStatus = !query.filingStatus || record.filingStatus.toString() === query.filingStatus;
            const matchesSearch = !query.search ||
                ((_a = record.tanNumber) === null || _a === void 0 ? void 0 : _a.includes(query.search)) ||
                ((_b = record.clientName) === null || _b === void 0 ? void 0 : _b.toLowerCase().includes(query.search.toLowerCase()));
            return (matchesFormCd &&
                matchesFinancialQuarter &&
                matchesFinancialYear &&
                matchesSearch &&
                matchesFilingStatus);
        });
        const limit = query.limit ? parseInt(query.limit, 10) : 10;
        const offset = query.offset ? parseInt(query.offset, 10) : 0;
        const paginatedData = filteredData.slice(offset, offset + limit);
        return {
            totalRecords: filteredData.length,
            data: paginatedData,
        };
    }
    async getFormsCorrectionAnalytics(userId, financialYear) {
        var _a;
        const user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const correctionFormsQuery = await (0, typeorm_1.createQueryBuilder)(tan_income_tax_forms_entity_1.default, 'tanForms')
            .leftJoinAndSelect('tanForms.tanClientCredentials', 'tanClientCredentials')
            .leftJoin('tanClientCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .select([
            'tanClientCredentials.id',
            'tanForms.id',
            'tanForms.formDesc',
            'tanForms.financialQuarter',
            'tanForms.refYear',
            'client.id',
            'clientManagers.id'
        ])
            .where('tanForms.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        })
            .andWhere('tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :finYear', {
            forms: ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
            filingTypeCd: 'Correction',
            finYear: financialYear,
        });
        if (ViewAssigned && !ViewAll) {
            correctionFormsQuery.andWhere('clientManagers.id = :userId', { userId });
        }
        const correctionForms = await correctionFormsQuery.getMany();
        const forms = ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];
        const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
        const analytics = {};
        forms.forEach((form) => {
            analytics[form] = { byQuarter: {} };
            quarters.forEach((quarter) => {
                analytics[form].byQuarter[quarter] = { filed: 0 };
            });
        });
        correctionForms.forEach((formData) => {
            const { formDesc, financialQuarter } = formData;
            if (forms.includes(formDesc) && quarters.includes(financialQuarter)) {
                analytics[formDesc].byQuarter[financialQuarter].filed += 1;
            }
        });
        return analytics;
    }
    async getExcelNoticeDates(organizationId, interval, dateColumn, query, ViewAll, ViewAssigned, userId) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 1 DAY';
                    break;
                case '1week':
                    1;
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fya 
        LEFT JOIN  client ON tan_temp_epro_fya.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_temp_epro_fya.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
            if (query.assessmentYear && query.assessmentYear !== '') {
                sql += ` AND ay = '${query.assessmentYear}'`;
            }
            if (ViewAssigned && !ViewAll) {
                sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getExcelNoticeFyaResponseDueDates(organizationId, interval, dateColumn, query, ViewAll, ViewAssigned, userId) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 1 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fya 
        LEFT JOIN  client ON tan_temp_epro_fya.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
          AND tan_temp_epro_fya.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
            if (query.assessmentYear && query.assessmentYear !== '') {
                sql += ` AND ay = '${query.assessmentYear}'`;
            }
            if (ViewAssigned && !ViewAll) {
                sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getExcelNoticeFyiDates(organizationId, interval, dateColumn, query, ViewAll, ViewAssigned, userId) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 1 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fyi 
        LEFT JOIN  client ON tan_temp_epro_fyi.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_temp_epro_fyi.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
            if (query.assessmentYear && query.assessmentYear !== '') {
                sql += ` AND ay = '${query.assessmentYear}'`;
            }
            if (ViewAssigned && !ViewAll) {
                sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getExcelNoticeFyiResponseDueDates(organizationId, interval, dateColumn, query, ViewAll, ViewAssigned, userId) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 1 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fyi 
        LEFT JOIN  client ON tan_temp_epro_fyi.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery}) 
          AND tan_temp_epro_fyi.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
            if (query.assessmentYear && query.assessmentYear !== '') {
                sql += ` AND ay = '${query.assessmentYear}'`;
            }
            if (ViewAssigned && !ViewAll) {
                sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getExcelCombinedNoticesCount(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            const fyaTodayIssued = await this.getExcelNoticeDates(organizationId, 'today', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyaLast1WeekIssued = await this.getExcelNoticeDates(organizationId, '1week', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyaLast15DaysIssued = await this.getExcelNoticeDates(organizationId, '15days', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyaLast1MonthIssued = await this.getExcelNoticeDates(organizationId, '1month', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyaTodayDue = await this.getExcelNoticeFyaResponseDueDates(organizationId, 'today', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyaLast1WeekDue = await this.getExcelNoticeFyaResponseDueDates(organizationId, '1week', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyaLast15DaysDue = await this.getExcelNoticeFyaResponseDueDates(organizationId, '15days', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyaLast1MonthDue = await this.getExcelNoticeFyaResponseDueDates(organizationId, '1month', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyiTodayIssued = await this.getExcelNoticeFyiDates(organizationId, 'today', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyiLast1WeekIssued = await this.getExcelNoticeFyiDates(organizationId, '1week', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyiLast15DaysIssued = await this.getExcelNoticeFyiDates(organizationId, '15days', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyiLast1MonthIssued = await this.getExcelNoticeFyiDates(organizationId, '1month', 'notice_sent_date', query, ViewAll, ViewAssigned, userId);
            const fyiTodayDue = await this.getExcelNoticeFyiResponseDueDates(organizationId, 'today', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyiLast1WeekDue = await this.getExcelNoticeFyiResponseDueDates(organizationId, '1week', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyiLast15DaysDue = await this.getExcelNoticeFyiResponseDueDates(organizationId, '15days', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            const fyiLast1MonthDue = await this.getExcelNoticeFyiResponseDueDates(organizationId, '1month', 'date_of_compliance', query, ViewAll, ViewAssigned, userId);
            return {
                issueData: {
                    last1WeekIssued: fyaLast1WeekIssued + fyiLast1WeekIssued,
                    last15DaysIssued: fyaLast15DaysIssued + fyiLast15DaysIssued,
                    last1MonthIssued: fyaLast1MonthIssued + fyiLast1MonthIssued,
                    todayIssued: fyaTodayIssued + fyiTodayIssued
                },
                responseDueData: {
                    last1WeekDue: fyaLast1WeekDue + fyiLast1WeekDue,
                    last15DaysDue: fyaLast15DaysDue + fyiLast15DaysDue,
                    last1MonthDue: fyaLast1MonthDue + fyiLast1MonthDue,
                    todayDue: fyaTodayDue + fyiTodayDue
                },
            };
        }
        else {
            return {
                issueData: {
                    last1WeekIssued: 0,
                    last15DaysIssued: 0,
                    last1MonthIssued: 0,
                    todayIssued: 0
                },
                responseDueData: {
                    last1WeekDue: 0,
                    last15DaysDue: 0,
                    last1MonthDue: 0,
                    todayDue: 0
                },
            };
        }
    }
    async getTraceNoticeDates(organizationId, interval, dateColumn, query, ViewAll, ViewAssigned, userId) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 1 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_communication_inbox 
        LEFT JOIN  client ON tan_communication_inbox.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, "%d-%b-%Y") BETWEEN DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_communication_inbox.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
            if (query.assessmentYear && query.assessmentYear !== '') {
                sql += ` AND ay = '${query.assessmentYear}'`;
            }
            if (ViewAssigned && !ViewAll) {
                sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async tracesNotice(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        const today = moment().format('YYYY-MM-DD');
        const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
        const previousFifteenththDay = moment().subtract(15, 'days').format('YYYY-MM-DD');
        const previousThirtythDay = moment().subtract(30, 'days').format('YYYY-MM-DD');
        if (organizationId) {
            const todayIssued = await this.getTraceNoticeDates(organizationId, 'today', 'date', query, ViewAll, ViewAssigned, userId);
            const Last1WeekIssued = await this.getTraceNoticeDates(organizationId, '1week', 'date', query, ViewAll, ViewAssigned, userId);
            const Last15DaysIssued = await this.getTraceNoticeDates(organizationId, '15days', 'date', query, ViewAll, ViewAssigned, userId);
            const Last1MonthIssued = await this.getTraceNoticeDates(organizationId, '1month', 'date', query, ViewAll, ViewAssigned, userId);
            return {
                issueData: {
                    last1WeekIssued: Last1WeekIssued,
                    last15DaysIssued: Last15DaysIssued,
                    last1MonthIssued: Last1MonthIssued,
                    todayIssued: todayIssued
                },
            };
        }
        else {
            return {
                issueData: {
                    last1WeekIssued: 0,
                    last15DaysIssued: 0,
                    last1MonthIssued: 0,
                    todayIssued: 0
                },
            };
        }
    }
    async getExcelFyaEvents(userId, query) {
        var _a, _b;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization'] });
        let fyaNotice = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
            .leftJoinAndSelect('tanTempEproFya.client', 'client')
            .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
            .where('tanTempEproFya.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
            const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
            fyaNotice.andWhere(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        }
        let fyiNotice = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
            .leftJoinAndSelect('tanTempEproFyi.client', 'client')
            .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
            .where('tanTempEproFyi.organizationId = :organizationId', {
            organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
            const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
            fyiNotice.andWhere(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        }
        const result = await fyaNotice.getMany();
        const result2 = await fyiNotice.getMany();
        const fyaNotices = result.map((notice) => (Object.assign(Object.assign({}, notice), { type: 'FYA' })));
        const fyiNotices = result2.map((notice) => (Object.assign(Object.assign({}, notice), { type: 'FYI' })));
        const result1and2 = [...fyaNotices, ...fyiNotices];
        return result1and2;
    }
    async getExcelResponseDueEvents(userId, query) {
        var _a, _b;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization'] });
        let fyaNotice = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
            .leftJoinAndSelect('tanTempEproFya.client', 'client')
            .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
            .where('tanTempEproFya.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
            const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
            fyaNotice.andWhere(`STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        }
        let fyiNotice = (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
            .leftJoinAndSelect('tanTempEproFyi.client', 'client')
            .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
            .where('tanTempEproFyi.organizationId = :organizationId', {
            organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
            const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
            fyiNotice.andWhere(`STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        }
        const result = await fyaNotice.getMany();
        const result2 = await fyiNotice.getMany();
        const fyaNotices = result.map((notice) => (Object.assign(Object.assign({}, notice), { type: 'FYA' })));
        const fyiNotices = result2.map((notice) => (Object.assign(Object.assign({}, notice), { type: 'FYI' })));
        const result1and2 = [...fyaNotices, ...fyiNotices];
        return result1and2;
    }
    async getTracesEvents(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization'] });
        let fyaNotice = (0, typeorm_1.createQueryBuilder)(tan_communication_inbox_entity_1.default, 'tanCommunicationInbox')
            .leftJoinAndSelect('tanCommunicationInbox.client', 'client')
            .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
            .where('tanCommunicationInbox.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanClientCredentials.status != :disStatus', {
            disStatus: tan_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
            const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
            fyaNotice.andWhere(`STR_TO_DATE(tanCommunicationInbox.date, "%d-%b-%Y") BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        }
        const result = await fyaNotice.getMany();
        const tracesNotices = result.map((notice) => (Object.assign(Object.assign({}, notice), { type: 'TRACES' })));
        const result1and2 = [...tracesNotices];
        return result1and2;
    }
    async incometaxTanClientCheck(userId, queryy) {
        var _a, _b;
        const { search, offset, limit } = queryy;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const entityManager = (0, typeorm_1.getRepository)(automation_machines_entity_1.default);
        const query = await entityManager
            .createQueryBuilder('automationMachines')
            .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
            .leftJoinAndSelect('tanCredentials.client', 'client')
            .where('tanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('automationMachines.type = :type', { type: 'TAN' })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanCredentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('LOWER(automationMachines.remarks) LIKE :remarks', {
            remarks: 'Invalid Password, Please retry.',
        })
            .andWhere((qb) => {
            const subQuery = qb
                .subQuery()
                .select('MAX(innerAutomationMachines.id)', 'maxId')
                .from(automation_machines_entity_1.default, 'innerAutomationMachines')
                .leftJoin('innerAutomationMachines.tanCredentials', 'innerAutoCredentials')
                .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere("innerAutomationMachines.type = 'TAN'")
                .groupBy('innerAutoCredentials.id')
                .getQuery();
            return 'automationMachines.id IN ' + subQuery;
        });
        if (search) {
            query.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('tanCredentials.tan_number LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('client.displayName LIKE :namesearch', {
                    namesearch: `%${search}%`,
                });
            }));
        }
        if (offset) {
            query.skip(offset);
        }
        if (limit) {
            query.take(limit);
        }
        const [filteredRows, totalCount] = await query.getManyAndCount();
        const totalClients = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'credentials')
            .leftJoin('credentials.client', 'client')
            .where('credentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('credentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .getCount();
        const client = await (0, typeorm_1.createQueryBuilder)(client_entity_1.default, 'client')
            .select('COUNT(DISTINCT client.tan_number)', 'count')
            .where('client.organization_id = :orgId', { orgId: (_b = user.organization) === null || _b === void 0 ? void 0 : _b.id })
            .andWhere('client.tan_number IS NOT NULL')
            .getRawOne();
        const count = parseInt(client.count);
        const result = {
            filteredRows,
            totalClients,
            count: filteredRows.length,
            uniquePansCount: count,
            totalCount,
        };
        return result;
    }
    async tracesClientCheck(userId, queryy) {
        var _a, _b;
        const { search, offset, limit } = queryy;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const entityManager = (0, typeorm_1.getRepository)(automation_machines_entity_1.default);
        const tracesQuery = await entityManager
            .createQueryBuilder('automationMachines')
            .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
            .leftJoinAndSelect('tanCredentials.client', 'client')
            .where('tanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('automationMachines.type = :type', { type: 'TRACES' })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('tanCredentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('LOWER(automationMachines.remarks) LIKE :remarks', {
            remarks: 'Invalid details',
        })
            .andWhere((qb) => {
            const subQuery = qb
                .subQuery()
                .select('MAX(innerAutomationMachines.id)', 'maxId')
                .from(automation_machines_entity_1.default, 'innerAutomationMachines')
                .leftJoin('innerAutomationMachines.tanCredentials', 'innerAutoCredentials')
                .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere("innerAutomationMachines.type = 'TRACES'")
                .groupBy('innerAutoCredentials.id')
                .getQuery();
            return 'automationMachines.id IN ' + subQuery;
        });
        if (search) {
            tracesQuery.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('tanCredentials.tan_number LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('client.displayName LIKE :namesearch', {
                    namesearch: `%${search}%`,
                });
            }));
        }
        if (offset) {
            tracesQuery.skip(offset);
        }
        if (limit) {
            tracesQuery.take(limit);
        }
        const [filteredRows, totalCount] = await tracesQuery.getManyAndCount();
        const totalClientsWithTraces = await (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'credentials')
            .leftJoin('credentials.client', 'client')
            .where('credentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('credentials.status = :inStatus', { inStatus: tan_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('credentials.traceUserId IS NOT NULL')
            .getCount();
        const client = await (0, typeorm_1.createQueryBuilder)(client_entity_1.default, 'client')
            .select('COUNT(DISTINCT client.tan_number)', 'count')
            .where('client.organization_id = :orgId', { orgId: (_b = user.organization) === null || _b === void 0 ? void 0 : _b.id })
            .andWhere('client.tan_number IS NOT NULL')
            .getRawOne();
        const count = parseInt(client.count);
        const result = {
            uniquePansCount: count,
            filteredRows,
            totalClientsWithTraces,
            totalCount,
        };
        return result;
    }
}
exports.TanDashboardService = TanDashboardService;
//# sourceMappingURL=tan-dashboard-service.js.map