"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationSubscriber = void 0;
const typeorm_1 = require("typeorm");
const organization_entity_1 = require("../modules/organization/entities/organization.entity");
let OrganizationSubscriber = class OrganizationSubscriber {
    constructor(connection) {
        this.connection = connection;
        connection.subscribers.push(this);
    }
    listenTo() {
        return organization_entity_1.Organization;
    }
    async beforeInsert(event) {
    }
    async afterInsert(event) {
        const entityManager = (0, typeorm_1.getManager)();
        const { email, id, legalName } = event === null || event === void 0 ? void 0 : event.entity;
        const data = {
            organizationName: legalName,
            email: email,
        };
        const jsonData = JSON.stringify(data);
        const mailOptions = {
            data: data,
            email: email,
            filePath: 'organization-signup',
            subject: 'Organization Registration - Vider',
            key: "",
            id: 0
        };
    }
};
OrganizationSubscriber = __decorate([
    (0, typeorm_1.EventSubscriber)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], OrganizationSubscriber);
exports.OrganizationSubscriber = OrganizationSubscriber;
//# sourceMappingURL=organization.subscribers.js.map