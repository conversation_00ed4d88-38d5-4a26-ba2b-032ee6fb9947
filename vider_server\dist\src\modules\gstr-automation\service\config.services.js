"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrConfigService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../../users/entities/user.entity");
const axios_1 = require("axios");
const gstrCredentials_entity_1 = require("../entity/gstrCredentials.entity");
const atomProReUse_1 = require("../../../utils/atomProReUse");
const typeorm_1 = require("typeorm");
const ExcelJS = require("exceljs");
const noticeOrders_entity_1 = require("../entity/noticeOrders.entity");
const moment = require("moment");
const gstrProfile_entity_1 = require("../entity/gstrProfile.entity");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
const task_entity_1 = require("../../tasks/entity/task.entity");
const returns_data_entity_1 = require("../../gstr-register/entity/returns-data.entity");
const types_1 = require("../../tasks/dto/types");
const activity_entity_1 = require("../../activity/activity.entity");
const actions_1 = require("../../../event-listeners/actions");
const utils_1 = require("../../../utils");
const task_status_entity_1 = require("../../tasks/entity/task-status.entity");
const storage_service_1 = require("../../storage/storage.service");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
let GstrConfigService = class GstrConfigService {
    constructor(storageService) {
        this.storageService = storageService;
    }
    async gstAtomClient(userId, id) {
        const gstClient = await gstrCredentials_entity_1.default.findOne({
            where: { id: id },
            relations: ['client'],
        });
        return gstClient === null || gstClient === void 0 ? void 0 : gstClient.client;
    }
    async disableAtomProGstrClient(userId, body) {
        const ids = body.ids;
        for (let gstrId of ids) {
            const gstrClient = await gstrCredentials_entity_1.default.findOne({ where: { id: gstrId } });
            if (gstrClient) {
                gstrClient.status = gstrCredentials_entity_1.GstrStatus.DISABLE;
                await gstrClient.save();
            }
        }
    }
    async disableGstrSingleClient(id, userId) {
        const gstrClient = await gstrCredentials_entity_1.default.findOne({ where: { id: id } });
        if (gstrClient) {
            gstrClient.status = gstrCredentials_entity_1.GstrStatus.DISABLE;
            await gstrClient.save();
        }
    }
    async getDeletedGstrClients(userId, query) {
        var _a;
        const { limit, offset } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            let deletedClients = (0, typeorm_1.createQueryBuilder)(gstrCredentials_entity_1.default, 'gstrCredentials')
                .leftJoinAndSelect('gstrCredentials.client', 'client')
                .leftJoinAndSelect('gstrCredentials.profile', 'profile')
                .where('gstrCredentials.organizationId = :organizationId', {
                organizationId: organizationId,
            })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere('gstrCredentials.status = :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE });
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    displayName: 'client.displayName',
                };
                const column = columnMap[sort.column] || sort.column;
                deletedClients.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                deletedClients.orderBy('gstrCredentials.updatedAt', 'DESC');
            }
            if (query.search) {
                deletedClients.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('gstrCredentials.userName LIKE :userName', {
                        userName: `%${query.search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                    qb.orWhere('client.clientId LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                    qb.orWhere('profile.gstin LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                }));
            }
            if (offset >= 0) {
                deletedClients.skip(offset);
            }
            if (limit) {
                deletedClients.take(limit);
            }
            let result = await deletedClients.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
    }
    async exportdeletedGstClient(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let clients = await this.getDeletedGstrClients(userId, newQuery);
        if (!clients.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Deleted GST Clients');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client ID', key: 'clientId' },
            { header: 'Client #', key: 'clientNumber' },
            { header: 'GSTIN', key: 'gstNumber' },
            { header: 'Category', key: 'category' },
            { header: 'Sub Category', key: 'subCategory' },
            { header: 'Client Name', key: 'displayName' },
            { header: 'Username', key: 'userName' },
            { header: 'Status Updated At', key: 'statusUpdatedAt' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        clients.result.forEach((client) => {
            var _a, _b, _c, _d, _e, _f;
            function capitalizeFirstLetter(string) {
                return string.charAt(0).toUpperCase() + string.slice(1);
            }
            const rowData = {
                serialNo: serialCounter++,
                clientId: (_a = client === null || client === void 0 ? void 0 : client.client) === null || _a === void 0 ? void 0 : _a.clientId,
                clientNumber: (_b = client === null || client === void 0 ? void 0 : client.client) === null || _b === void 0 ? void 0 : _b.clientNumber,
                category: ((_c = client === null || client === void 0 ? void 0 : client.client) === null || _c === void 0 ? void 0 : _c.category) ? (0, utils_1.getTitle)((_d = client === null || client === void 0 ? void 0 : client.client) === null || _d === void 0 ? void 0 : _d.category) : "",
                subCategory: ((_e = client === null || client === void 0 ? void 0 : client.client) === null || _e === void 0 ? void 0 : _e.subCategory) ? (0, utils_1.getTitle)((_f = client === null || client === void 0 ? void 0 : client.client) === null || _f === void 0 ? void 0 : _f.subCategory) : "",
                displayName: client.client.displayName,
                gstNumber: client.client.gstNumber,
                userName: client.userName,
                statusUpdatedAt: moment(client === null || client === void 0 ? void 0 : client.updatedAt).format("DD-MM-YYYY h:mm a")
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'displayName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async enableGstrClient(id, userId) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        const gstrClient = await gstrCredentials_entity_1.default.findOne({ where: { id: id } });
        if (gstrClient) {
            const checkGstr = await (0, atomProReUse_1.checkAtomProConfigGstr)(organizationId);
            gstrClient.status = gstrCredentials_entity_1.GstrStatus.ENABLE;
            if (checkGstr === true) {
                await gstrClient.save();
            }
            else {
                throw new common_1.BadRequestException(checkGstr);
            }
        }
    }
    async enableBulkGstrClient(userId, body) {
        var _a, _b, _c, _d;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (!organizationId) {
            throw new common_1.BadRequestException('Organization not found for this user');
        }
        const orgPreferences = await organization_preferences_entity_1.default.findOne({
            where: { organization: organizationId },
        });
        const isGstrEnabled = ((_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.automationConfig) === null || _b === void 0 ? void 0 : _b.gstr) === 'YES';
        if (!isGstrEnabled) {
            throw new common_1.BadRequestException('Subscribe Atom Pro Gstr to access for this Client');
        }
        const gstrCredentialCount = await gstrCredentials_entity_1.default.count({
            where: { organizationId, status: gstrCredentials_entity_1.GstrStatus.ENABLE },
        });
        const organizationGstrLimit = ((_c = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.automationConfig) === null || _c === void 0 ? void 0 : _c.gstrLimit) || 0;
        const clientsToEnable = ((_d = body === null || body === void 0 ? void 0 : body.gstrClients) === null || _d === void 0 ? void 0 : _d.length) || 0;
        if (gstrCredentialCount + clientsToEnable > organizationGstrLimit) {
            throw new common_1.BadRequestException(`Cannot enable clients. You can only enable up to ${organizationGstrLimit} clients in Atom Pro GSTR.`);
        }
        for (let i of body === null || body === void 0 ? void 0 : body.gstrClients) {
            const gstrClient = await gstrCredentials_entity_1.default.findOne({ where: { id: i === null || i === void 0 ? void 0 : i.id } });
            if (gstrClient) {
                gstrClient.status = gstrCredentials_entity_1.GstrStatus.ENABLE;
                await gstrClient.save();
            }
        }
    }
    async getBulkSyncStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let data = '';
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/gstr`,
                headers: {},
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occure while getting into getBulkSyncStatus', error.message);
        }
    }
    async updateEnableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/enable/gstr`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus', error);
        }
    }
    async updateDisableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/disable/gstr`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus', error);
        }
    }
    async organizationGstrScheduling(userId, body) {
        const { periodicity, day, hour, minute, weekDay } = body;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizarionId = user.organization.id;
            const amPm = hour >= 12 ? 'pm' : 'am';
            const adjustedHour = hour > 12 ? hour - 12 : hour;
            const originalBody = [
                {
                    periodicity: periodicity || 'DAILY',
                    days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
                    daysInstance: {},
                    hour: adjustedHour,
                    minute: minute,
                    seconds: 0,
                    amPm: amPm,
                    dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0,
                    month: 0,
                    intervalMinutes: 0,
                },
            ];
            let data = JSON.stringify({
                modules: ['P', 'NAO', 'ANO', 'OD', 'LB'],
                orgId: organizarionId,
                type: 'GSTR',
                schedules: originalBody,
            });
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occur while organizationScheduling', error);
        }
    }
    async createNoticeAndOrderItem(userId, body) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (body === null || body === void 0 ? void 0 : body.gstrCredentialsId) {
                const gstrCreadentials = await gstrCredentials_entity_1.default.findOne({
                    where: { id: body === null || body === void 0 ? void 0 : body.gstrCredentialsId },
                    relations: ['client'],
                });
                const gstProfile = await gstrProfile_entity_1.default.findOne({
                    where: { gstrCredentials: gstrCreadentials },
                });
                const noticeOrders = new noticeOrders_entity_1.default();
                noticeOrders.orderNumber = body.orderNumber;
                noticeOrders.issuedBy = 'Manual';
                noticeOrders.type = body === null || body === void 0 ? void 0 : body.type;
                noticeOrders.amountOfDemand = body === null || body === void 0 ? void 0 : body.amountOfDemand;
                noticeOrders.description = body === null || body === void 0 ? void 0 : body.description;
                noticeOrders.dateOfIssuance = (body === null || body === void 0 ? void 0 : body.dateOfIssuance)
                    ? moment(body === null || body === void 0 ? void 0 : body.dateOfIssuance).format('DD/MM/YYYY')
                    : null;
                noticeOrders.dueDate = (body === null || body === void 0 ? void 0 : body.dueDate) ? moment(body === null || body === void 0 ? void 0 : body.dueDate).format('DD/MM/YYYY') : null;
                noticeOrders.client = gstrCreadentials === null || gstrCreadentials === void 0 ? void 0 : gstrCreadentials.client;
                noticeOrders.gstrCredentialsId = body === null || body === void 0 ? void 0 : body.gstrCredentialsId;
                noticeOrders.organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
                if (gstProfile) {
                    noticeOrders.gstIn = gstProfile === null || gstProfile === void 0 ? void 0 : gstProfile.gstin;
                }
                noticeOrders.createdType = noticeOrders_entity_1.CreatedType.MANUAL;
                await noticeOrders.save();
            }
        }
        catch (error) {
            console.log('Error occur while CreateFyaItem', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    async updateNoticeAndOrder(userId, body) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (body === null || body === void 0 ? void 0 : body.id) {
                const noticeOrders = await noticeOrders_entity_1.default.findOne({
                    where: { id: body === null || body === void 0 ? void 0 : body.id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                });
                if (noticeOrders) {
                    noticeOrders.orderNumber = body.orderNumber;
                    noticeOrders.type = body === null || body === void 0 ? void 0 : body.type;
                    noticeOrders.amountOfDemand = body === null || body === void 0 ? void 0 : body.amountOfDemand;
                    noticeOrders.description = body === null || body === void 0 ? void 0 : body.description;
                    noticeOrders.dateOfIssuance = body === null || body === void 0 ? void 0 : body.dateOfIssuance;
                    noticeOrders.dueDate = body === null || body === void 0 ? void 0 : body.dueDate;
                    await noticeOrders.save();
                    return true;
                }
            }
        }
        catch (error) {
            throw new common_1.BadRequestException('Error Occur while Update FYA Proceeding', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    async deleteNoticeAndOrder(userId, id) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (id) {
                const noticeOrder = await noticeOrders_entity_1.default.findOne({
                    where: { id: id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                    relations: ['storage'],
                });
                if (noticeOrder) {
                    for (const storage of noticeOrder.storage) {
                        await this.storageService.removeFile(storage.id, userId);
                    }
                    await noticeOrders_entity_1.default.delete({ id: id, organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id });
                    return true;
                }
            }
        }
        catch (error) {
            throw new common_1.BadRequestException('Error Occur while deleting Proceeding', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    async createAdditionalNotice(userId, body) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (body === null || body === void 0 ? void 0 : body.gstrCredentialsId) {
                const gstrCreadentials = await gstrCredentials_entity_1.default.findOne({
                    where: { id: body === null || body === void 0 ? void 0 : body.gstrCredentialsId },
                    relations: ['client'],
                });
                const gstProfile = await gstrProfile_entity_1.default.findOne({
                    where: { gstrCredentials: gstrCreadentials },
                });
                const additionalNotice = new gstrAdditionalOrdersAndNotices_entity_1.default();
                additionalNotice.fy = body === null || body === void 0 ? void 0 : body.fiscalYear;
                additionalNotice.arn = body === null || body === void 0 ? void 0 : body.arn;
                additionalNotice.caseId = body === null || body === void 0 ? void 0 : body.caseId;
                additionalNotice.caseTypeId = body === null || body === void 0 ? void 0 : body.caseTypeId;
                additionalNotice.refId = body === null || body === void 0 ? void 0 : body.refId;
                additionalNotice.refNum = body === null || body === void 0 ? void 0 : body.referenceNumber;
                additionalNotice.section = body === null || body === void 0 ? void 0 : body.section;
                additionalNotice.caseFolderTypeName = body === null || body === void 0 ? void 0 : body.caseFolderType;
                additionalNotice.caseTypeName = body === null || body === void 0 ? void 0 : body.caseTypeName;
                additionalNotice.categoryType = body === null || body === void 0 ? void 0 : body.categoryType;
                additionalNotice.venue = body === null || body === void 0 ? void 0 : body.place;
                additionalNotice.personalHearning = body === null || body === void 0 ? void 0 : body.personalHearing;
                additionalNotice.nm = body === null || body === void 0 ? void 0 : body.issuedByName;
                additionalNotice.designation = body === null || body === void 0 ? void 0 : body.issuedByDesignation;
                additionalNotice.summary = body === null || body === void 0 ? void 0 : body.summary;
                additionalNotice.categoryDate = (body === null || body === void 0 ? void 0 : body.dateOfIssuance)
                    ? moment(body === null || body === void 0 ? void 0 : body.dateOfIssuance).format('DD/MM/YYYY')
                    : null;
                additionalNotice.dueDate = (body === null || body === void 0 ? void 0 : body.dueDate)
                    ? moment(body === null || body === void 0 ? void 0 : body.dueDate).format('DD/MM/YYYY')
                    : null;
                additionalNotice.description = body === null || body === void 0 ? void 0 : body.description;
                additionalNotice.client = gstrCreadentials === null || gstrCreadentials === void 0 ? void 0 : gstrCreadentials.client;
                additionalNotice.gstrCredentialsId = body === null || body === void 0 ? void 0 : body.gstrCredentialsId;
                additionalNotice.organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
                if (gstProfile) {
                    additionalNotice.gstIn = gstProfile === null || gstProfile === void 0 ? void 0 : gstProfile.gstin;
                    additionalNotice.name = gstProfile === null || gstProfile === void 0 ? void 0 : gstProfile.legalName;
                }
                additionalNotice.createdType = noticeOrders_entity_1.CreatedType.MANUAL;
                await additionalNotice.save();
                if (additionalNotice) {
                    if (body.type === 'AddReplies') {
                        const additionalItem = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                            where: { id: body === null || body === void 0 ? void 0 : body.additionalNoticeId },
                        });
                        if (additionalItem) {
                            additionalItem.refStatus = 'REPLIED';
                            await additionalItem.save();
                        }
                    }
                    if ((additionalNotice === null || additionalNotice === void 0 ? void 0 : additionalNotice.caseFolderTypeName) === 'ORDERS') {
                        const additionalItem = await gstrAdditionalOrdersAndNotices_entity_1.default.find({
                            where: { arn: additionalNotice.arn, organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id },
                        });
                        if (additionalItem) {
                            for (let item of additionalItem) {
                                item.caseStatus = 'CLOSED';
                                await item.save();
                            }
                        }
                    }
                }
            }
        }
        catch (error) {
            console.log('Error occur while CreateFyaItem', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    async updateAdditionalNotice(userId, body) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (body === null || body === void 0 ? void 0 : body.id) {
                const additionalNotice = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id: body === null || body === void 0 ? void 0 : body.id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                });
                if (additionalNotice) {
                    additionalNotice.fy = body === null || body === void 0 ? void 0 : body.fy;
                    additionalNotice.arn = body === null || body === void 0 ? void 0 : body.arn;
                    additionalNotice.refId = body === null || body === void 0 ? void 0 : body.refId;
                    additionalNotice.refNum = body === null || body === void 0 ? void 0 : body.refNum;
                    additionalNotice.section = body === null || body === void 0 ? void 0 : body.section;
                    additionalNotice.caseFolderTypeName = body === null || body === void 0 ? void 0 : body.caseFolderTypeName;
                    additionalNotice.caseTypeName = body === null || body === void 0 ? void 0 : body.caseTypeName;
                    additionalNotice.categoryType = body === null || body === void 0 ? void 0 : body.categoryType;
                    additionalNotice.venue = body === null || body === void 0 ? void 0 : body.venue;
                    additionalNotice.personalHearning = body === null || body === void 0 ? void 0 : body.personalHearning;
                    additionalNotice.nm = body === null || body === void 0 ? void 0 : body.nm;
                    additionalNotice.designation = body === null || body === void 0 ? void 0 : body.designation;
                    additionalNotice.categoryDate = body === null || body === void 0 ? void 0 : body.categoryDate;
                    additionalNotice.dueDate = body === null || body === void 0 ? void 0 : body.dueDate;
                    additionalNotice.description = body === null || body === void 0 ? void 0 : body.description;
                    additionalNotice.summary = body === null || body === void 0 ? void 0 : body.summary;
                    await additionalNotice.save();
                    return true;
                }
            }
        }
        catch (error) {
            console.log('Error occur while CreateFyaItem', error === null || error === void 0 ? void 0 : error.message);
            throw new common_1.BadRequestException('Error Occur while Update FYA Proceeding', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    async deleteAdditionalNotice(userId, id) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (id) {
                const additionalNotice = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id: id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                    relations: ['storage'],
                });
                if (additionalNotice) {
                    for (const storage of additionalNotice === null || additionalNotice === void 0 ? void 0 : additionalNotice.storage) {
                        await this.storageService.removeFile(storage.id, userId);
                    }
                    await gstrAdditionalOrdersAndNotices_entity_1.default.delete({
                        id: id,
                        organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
                    });
                    return true;
                }
            }
        }
        catch (error) {
            throw new common_1.BadRequestException('Error Occur while deleting Additional Notice', error === null || error === void 0 ? void 0 : error.message);
        }
    }
    normalizeFinancialYear(yearStr) {
        if (yearStr.includes('-')) {
            const parts = yearStr.split('-');
            const first = parts[0];
            let second = parts[1];
            if (second.length === 2) {
                const century = first.slice(0, 2);
                second = century + second;
            }
            return `${first}-${second}`;
        }
        return yearStr;
    }
    async completeTaskGstrOne(userId) {
        var _a, _b, _c;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
            let completedGstrOneTasks = [];
            let completedGstr3BTasks = [];
            let errorMsg = [];
            if (organizationId) {
                const tasks = await (0, typeorm_1.createQueryBuilder)(task_entity_1.default, 'task')
                    .leftJoin('task.organization', 'organization')
                    .leftJoinAndSelect('task.client', 'client')
                    .leftJoin('task.category', 'category')
                    .leftJoin('task.subCategory', 'subCategory')
                    .where('organization.id = :organizationId', { organizationId })
                    .andWhere('category.id = :categoryId', { categoryId: 45 })
                    .andWhere('subCategory.id = :subCategoryId', { subCategoryId: 111 })
                    .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
                    .andWhere('task.status != :status', { status: 'completed' })
                    .andWhere('task.name LIKE :taskName', { taskName: '%GSTR-1%' })
                    .andWhere('task.processInstanceId IS NULL')
                    .andWhere('task.approvalStatus IS NULL')
                    .getMany();
                if ((tasks === null || tasks === void 0 ? void 0 : tasks.length) > 0) {
                    const filedReturns = await (0, typeorm_1.createQueryBuilder)(returns_data_entity_1.ReturnsData, 'returnsData')
                        .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
                        .leftJoin('gstrRegister.organization', 'organization')
                        .leftJoinAndSelect('gstrRegister.client', 'client')
                        .where('organization.id = :organizationId', { organizationId })
                        .andWhere('returnsData.rtntype = :rtntype', { rtntype: 'GSTR1' })
                        .andWhere('returnsData.status = :status', { status: 'Filed' })
                        .getMany();
                    if (filedReturns.length === 0) {
                        errorMsg.push('No filed GSTR1 returns found');
                    }
                    for (const task of tasks) {
                        const taskFY = this.normalizeFinancialYear(task.financialYear);
                        const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
                        if (!match)
                            continue;
                        const [_, monthName, yearStr] = match;
                        const month = moment().month(monthName).format('MM');
                        const year = yearStr;
                        const taskRetPrd = `${month}${year}`;
                        const returnMatch = filedReturns.find((ret) => {
                            var _a, _b, _c, _d, _e;
                            if (!((_b = (_a = ret === null || ret === void 0 ? void 0 : ret.gstrRegister) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.id)) {
                                return null;
                            }
                            const returnFY = this.normalizeFinancialYear(ret === null || ret === void 0 ? void 0 : ret.financialYear);
                            return (returnFY === taskFY &&
                                (ret === null || ret === void 0 ? void 0 : ret.retPrd) === taskRetPrd &&
                                ((_c = task === null || task === void 0 ? void 0 : task.client) === null || _c === void 0 ? void 0 : _c.id) === ((_e = (_d = ret === null || ret === void 0 ? void 0 : ret.gstrRegister) === null || _d === void 0 ? void 0 : _d.client) === null || _e === void 0 ? void 0 : _e.id));
                        });
                        if (returnMatch) {
                            const oldStatus = task === null || task === void 0 ? void 0 : task.status;
                            task.status = types_1.TaskStatusEnum.COMPLETED;
                            task.restore = types_1.TaskStatusEnum.COMPLETED;
                            task.isCompletedBy = 'ATOM Pro - Sync';
                            task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
                            completedGstrOneTasks.push(task);
                            let activity = new activity_entity_1.default();
                            activity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                            activity.actorId = user.id;
                            activity.type = activity_entity_1.ActivityType.TASK;
                            activity.typeId = task === null || task === void 0 ? void 0 : task.id;
                            activity.remarks = `"${task === null || task === void 0 ? void 0 : task.taskNumber}" Task moved from ${(0, utils_1.getTitle)(oldStatus)} to ${(0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status)} by ${user.fullName} - ATOM Pro - Sync`;
                            let clientactivity = new activity_entity_1.default();
                            clientactivity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                            clientactivity.actorId = user.id;
                            clientactivity.type = activity_entity_1.ActivityType.CLIENT;
                            clientactivity.typeId = (_b = task === null || task === void 0 ? void 0 : task.client) === null || _b === void 0 ? void 0 : _b.id;
                            clientactivity.remarks = `"${task === null || task === void 0 ? void 0 : task.taskNumber}" Task moved from ${(0, utils_1.getTitle)(oldStatus)} to ${(0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status)} by ${user.fullName} - ATOM Pro - Sync`;
                            let taskStatus = new task_status_entity_1.default();
                            taskStatus.restore = types_1.TaskStatusEnum.COMPLETED;
                            taskStatus.status = types_1.TaskStatusEnum.COMPLETED;
                            taskStatus.task = task;
                            taskStatus.user = user;
                        }
                    }
                }
                else {
                    errorMsg.push('No Gstr Task are Find to complete');
                }
            }
            if (organizationId) {
                const tasks = await (0, typeorm_1.createQueryBuilder)(task_entity_1.default, 'task')
                    .leftJoin('task.organization', 'organization')
                    .leftJoinAndSelect('task.client', 'client')
                    .leftJoin('task.category', 'category')
                    .leftJoin('task.subCategory', 'subCategory')
                    .where('organization.id = :organizationId', { organizationId })
                    .andWhere('category.id = :categoryId', { categoryId: 45 })
                    .andWhere('subCategory.id = :subCategoryId', { subCategoryId: 111 })
                    .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
                    .andWhere('task.status != :status', { status: 'completed' })
                    .andWhere('task.name LIKE :taskName', { taskName: 'GSTR-3B%' })
                    .andWhere('task.processInstanceId IS NULL')
                    .andWhere('task.approvalStatus IS NULL')
                    .getMany();
                if ((tasks === null || tasks === void 0 ? void 0 : tasks.length) > 0) {
                    const filedReturns = await (0, typeorm_1.createQueryBuilder)(returns_data_entity_1.ReturnsData, 'returnsData')
                        .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
                        .leftJoin('gstrRegister.organization', 'organization')
                        .leftJoinAndSelect('gstrRegister.client', 'client')
                        .where('organization.id = :organizationId', { organizationId })
                        .andWhere('returnsData.rtntype = :rtntype', { rtntype: 'GSTR3B' })
                        .andWhere('returnsData.status = :status', { status: 'Filed' })
                        .getMany();
                    if (filedReturns.length === 0) {
                        errorMsg.push('No filed GSTR3B returns found');
                    }
                    for (const task of tasks) {
                        const taskFY = this.normalizeFinancialYear(task.financialYear);
                        const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
                        if (!match)
                            continue;
                        const [_, monthName, yearStr] = match;
                        const month = moment().month(monthName).format('MM');
                        const year = yearStr;
                        const taskRetPrd = `${month}${year}`;
                        const returnMatch = filedReturns.find((ret) => {
                            var _a, _b, _c, _d, _e;
                            if (!((_b = (_a = ret === null || ret === void 0 ? void 0 : ret.gstrRegister) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.id)) {
                                return null;
                            }
                            const returnFY = this.normalizeFinancialYear(ret === null || ret === void 0 ? void 0 : ret.financialYear);
                            return (returnFY === taskFY &&
                                (ret === null || ret === void 0 ? void 0 : ret.retPrd) === taskRetPrd &&
                                ((_c = task === null || task === void 0 ? void 0 : task.client) === null || _c === void 0 ? void 0 : _c.id) === ((_e = (_d = ret === null || ret === void 0 ? void 0 : ret.gstrRegister) === null || _d === void 0 ? void 0 : _d.client) === null || _e === void 0 ? void 0 : _e.id));
                        });
                        if (returnMatch) {
                            const oldStatus = task === null || task === void 0 ? void 0 : task.status;
                            task.status = types_1.TaskStatusEnum.COMPLETED;
                            task.restore = types_1.TaskStatusEnum.COMPLETED;
                            task.isCompletedBy = 'ATOM Pro - Sync';
                            task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
                            completedGstr3BTasks.push(task);
                            let activity = new activity_entity_1.default();
                            activity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                            activity.actorId = user.id;
                            activity.type = activity_entity_1.ActivityType.TASK;
                            activity.typeId = task === null || task === void 0 ? void 0 : task.id;
                            activity.remarks = `"${task === null || task === void 0 ? void 0 : task.taskNumber}" Task moved from ${(0, utils_1.getTitle)(oldStatus)} to ${(0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status)} by ${user.fullName} - ATOM Pro - Sync`;
                            let clientactivity = new activity_entity_1.default();
                            clientactivity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                            clientactivity.actorId = user.id;
                            clientactivity.type = activity_entity_1.ActivityType.CLIENT;
                            clientactivity.typeId = (_c = task === null || task === void 0 ? void 0 : task.client) === null || _c === void 0 ? void 0 : _c.id;
                            clientactivity.remarks = `"${task === null || task === void 0 ? void 0 : task.taskNumber}" Task moved from ${(0, utils_1.getTitle)(oldStatus)} to ${(0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status)} by ${user.fullName} - ATOM Pro - Sync`;
                            let taskStatus = new task_status_entity_1.default();
                            taskStatus.restore = types_1.TaskStatusEnum.COMPLETED;
                            taskStatus.status = types_1.TaskStatusEnum.COMPLETED;
                            taskStatus.task = task;
                            taskStatus.user = user;
                        }
                    }
                }
                else {
                    errorMsg.push('No GSTR-3B Task are Found to complete');
                }
            }
            return { completedGstrOneTasks, completedGstr3BTasks, errorMsg };
        }
        catch (error) {
            console.log('Error Occur while Complete TaskGstrOne', error === null || error === void 0 ? void 0 : error.message);
            throw new common_1.InternalServerErrorException(Error);
        }
    }
    async completeTasksWithAtomProSync(userId, body) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
            if (!organizationId)
                throw new Error('Organization not found for user');
            const [gstr1Tasks, err1] = await this.processGstrTasks('GSTR1', 111, user, organizationId, body);
            const [gstr3BTasks, err2] = await this.processGstrTasks('GSTR3B', 111, user, organizationId, body);
            const [gstr9Tasks, err3] = await this.processGstrTasks('GSTR9', 111, user, organizationId, body);
            let successRows = [];
            if (gstr1Tasks) {
                let gstrOneCompletedTasks = gstr1Tasks.map((item) => { var _a; return `Task : ${item === null || item === void 0 ? void 0 : item.name} Task Number : ${item === null || item === void 0 ? void 0 : item.taskNumber} Client : ${(_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName}  GSTR-1 Task is Completed`; });
                successRows = [...successRows, ...gstrOneCompletedTasks];
            }
            if (gstr3BTasks) {
                let completedGstr3BTasks = gstr3BTasks.map((item) => { var _a; return `Task : ${item === null || item === void 0 ? void 0 : item.name} Task Number : ${item === null || item === void 0 ? void 0 : item.taskNumber} Client : ${(_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName}  GSTR-3B Task is Completed`; });
                successRows = [...successRows, ...completedGstr3BTasks];
            }
            if (gstr9Tasks) {
                let completedGstr9Tasks = gstr9Tasks.map((item) => { var _a; return `Task : ${item === null || item === void 0 ? void 0 : item.name} Task Number : ${item === null || item === void 0 ? void 0 : item.taskNumber} Client : ${(_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName}  GSTR-9 Task is Completed`; });
                successRows = [...successRows, ...completedGstr9Tasks];
            }
            return {
                gstCompletedTasks: [...gstr1Tasks, ...gstr3BTasks, ...gstr9Tasks],
                successRows,
            };
        }
        catch (error) {
            console.log('Error Occur while Complete TaskGstrOne', error === null || error === void 0 ? void 0 : error.message);
            throw new common_1.InternalServerErrorException(error);
        }
    }
    async processGstrTasks(rtntype, subCategoryId, user, organizationId, body) {
        var _a;
        const completedTasks = [];
        const errorMsg = [];
        const gstrType = {
            GSTR1: 'GSTR-1%',
            GSTR3B: 'GSTR-3B%',
            GSTR9: 'GSTR-9 Annual Return by Normal Taxpayer%',
        };
        const tasks = await (0, typeorm_1.createQueryBuilder)(task_entity_1.default, 'task')
            .leftJoin('task.organization', 'organization')
            .leftJoinAndSelect('task.client', 'client')
            .leftJoin('task.category', 'category')
            .leftJoin('task.subCategory', 'subCategory')
            .where('organization.id = :organizationId', { organizationId })
            .andWhere('client.id IN (:...clientIds)', { clientIds: body === null || body === void 0 ? void 0 : body.clientDetails })
            .andWhere('category.id = 45')
            .andWhere('subCategory.id = :subCategoryId', { subCategoryId })
            .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
            .andWhere('task.status != :status', { status: 'completed' })
            .andWhere('task.name LIKE :taskName', { taskName: gstrType[rtntype] })
            .andWhere('task.processInstanceId IS NULL')
            .andWhere('task.approvalStatus IS NULL')
            .getMany();
        if (!tasks.length) {
            errorMsg.push(`No ${rtntype} Task are Found to complete`);
            return [completedTasks, errorMsg];
        }
        const filedReturns = await (0, typeorm_1.createQueryBuilder)(returns_data_entity_1.ReturnsData, 'returnsData')
            .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
            .leftJoin('gstrRegister.organization', 'organization')
            .leftJoinAndSelect('gstrRegister.client', 'client')
            .where('organization.id = :organizationId', { organizationId })
            .andWhere('client.id IN (:...clientIds)', { clientIds: body === null || body === void 0 ? void 0 : body.clientDetails })
            .andWhere('returnsData.rtntype = :rtntype', { rtntype })
            .andWhere('returnsData.status = :status', { status: 'Filed' })
            .getMany();
        if (!filedReturns.length) {
            errorMsg.push(`No filed ${rtntype} returns found`);
        }
        for (const task of tasks) {
            const taskFY = this.normalizeFinancialYear(task.financialYear);
            const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
            if (!match)
                continue;
            const [_, monthName, yearStr] = match;
            const month = moment().month(monthName).format('MM');
            const year = yearStr;
            const taskRetPrd = `${month}${year}`;
            const returnMatch = filedReturns.find((ret) => {
                var _a, _b, _c;
                const returnFY = this.normalizeFinancialYear(ret.financialYear);
                return (returnFY === taskFY &&
                    ret.retPrd === taskRetPrd &&
                    ((_b = (_a = ret.gstrRegister) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.id) === ((_c = task.client) === null || _c === void 0 ? void 0 : _c.id));
            });
            if (returnMatch) {
                const oldStatus = task.status;
                task.status = types_1.TaskStatusEnum.COMPLETED;
                task.restore = types_1.TaskStatusEnum.COMPLETED;
                task.isCompletedBy = 'ATOM Pro - Sync';
                task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
                completedTasks.push(task);
                const activity = new activity_entity_1.default();
                activity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                activity.actorId = user.id;
                activity.type = activity_entity_1.ActivityType.TASK;
                activity.typeId = task.id;
                activity.remarks = `"${task.taskNumber}" Task moved from ${(0, utils_1.getTitle)(oldStatus)} to ${(0, utils_1.getTitle)(task.status)} by ${user.fullName} - ATOM Pro - Sync`;
                const clientactivity = new activity_entity_1.default();
                clientactivity.action = actions_1.Event_Actions.TASK_STATUS_UPDATED;
                clientactivity.actorId = user.id;
                clientactivity.type = activity_entity_1.ActivityType.CLIENT;
                clientactivity.typeId = (_a = task.client) === null || _a === void 0 ? void 0 : _a.id;
                clientactivity.remarks = activity.remarks;
                const taskStatus = new task_status_entity_1.default();
                taskStatus.restore = types_1.TaskStatusEnum.COMPLETED;
                taskStatus.status = types_1.TaskStatusEnum.COMPLETED;
                taskStatus.task = task;
                taskStatus.user = user;
                await task.save();
                await activity.save();
                await clientactivity.save();
                await taskStatus.save();
            }
        }
        return [completedTasks, errorMsg];
    }
};
GstrConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [storage_service_1.StorageService])
], GstrConfigService);
exports.GstrConfigService = GstrConfigService;
//# sourceMappingURL=config.services.js.map