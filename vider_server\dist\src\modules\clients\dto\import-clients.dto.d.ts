import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { CategoryEnum, SubCategoryEnum } from './types';
export declare class ImportClientsDto {
    displayName: string;
    category: CategoryEnum;
    subCategory: SubCategoryEnum;
    createdBy: User;
    organization: Organization;
    email: string;
    mobileNumber: string;
    gstNumber: string;
    panNumber: string;
    authorizedPerson: string;
    designation: string;
    gstVerified: boolean;
    tradeName: string;
    legalName: string;
    constitutionOfBusiness: string;
    placeOfSupply: string;
    panVerified: boolean;
    firstName: string;
    middleName: string;
    lastName: string;
    fullName: string;
    issameaddress: boolean;
    alternateMobileNumber: string;
    dob: string;
    buildingName: string;
    street: string;
    city: string;
    state: string;
    pincode: string;
    notes: string;
    address: object;
}
