import { Connection, EntitySubscriberInterface, InsertEvent } from 'typeorm';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';
export declare class DocumentInOutSubscriber implements EntitySubscriberInterface<DocumentInOut> {
    private readonly connection;
    constructor(connection: Connection);
    listenTo(): typeof DocumentInOut;
    beforePriority: string;
    beforeStatus: string;
    beforeInsert(event: InsertEvent<DocumentInOut>): Promise<void>;
    afterInsert(event: InsertEvent<DocumentInOut>): Promise<void>;
}
