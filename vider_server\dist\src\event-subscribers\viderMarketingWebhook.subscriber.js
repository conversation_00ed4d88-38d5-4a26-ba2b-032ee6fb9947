"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViderMarketingWebhookSubscriber = void 0;
const typeorm_1 = require("typeorm");
const whatsapp_service_1 = require("../modules/whatsapp/whatsapp.service");
const vider_marketing_webhook_entity_1 = require("../modules/webhook-marketing/entity/vider-marketing-webhook.entity");
const vider_marketing_entity_1 = require("../modules/webhook-marketing/entity/vider-marketing.entity");
const AWS = require("aws-sdk");
const metaTemplatesStatusWebhook_1 = require("../modules/whatsapp/entity/metaTemplatesStatusWebhook");
const axios = require('axios');
const s3 = new AWS.S3();
let ViderMarketingWebhookSubscriber = class ViderMarketingWebhookSubscriber {
    constructor(connection) {
        this.connection = connection;
        connection.subscribers.push(this);
    }
    listenTo() {
        return vider_marketing_webhook_entity_1.default;
    }
    beforeInsert(event) {
    }
    async afterInsert(event) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
        try {
            const payload = JSON.parse(event.entity.payload);
            const changes = (_b = (_a = payload.entry) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.changes;
            if (!changes || changes.length === 0) {
                return;
            }
            const statusData = (_e = (_d = (_c = changes[0]) === null || _c === void 0 ? void 0 : _c.value) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e[0];
            if (((_f = changes[0]) === null || _f === void 0 ? void 0 : _f.field) === "messages") {
                const messages = (_j = (_h = (_g = changes[0]) === null || _g === void 0 ? void 0 : _g.value) === null || _h === void 0 ? void 0 : _h.messages) === null || _j === void 0 ? void 0 : _j[0];
                if (messages) {
                    const { from, text, image, document, type } = messages;
                    const mobileNumber = from;
                    if (type === 'image' && image) {
                        await this.handleMedia(image.id, mobileNumber, image);
                    }
                    if (type === 'document' && document) {
                        await this.handleMedia(document.id, mobileNumber, document);
                    }
                    if (text && text.body) {
                        const messageBody = text.body.toLowerCase();
                        if (messageBody.includes('hello')) {
                            let data = JSON.stringify({
                                "messaging_product": "whatsapp",
                                "to": mobileNumber,
                                "type": "template",
                                "template": {
                                    "name": "flow",
                                    "language": {
                                        "code": "en"
                                    },
                                    "components": [
                                        {
                                            "type": "button",
                                            "sub_type": "flow",
                                            "index": "0",
                                            "parameters": [
                                                {
                                                    "type": "action",
                                                    "action": {
                                                        "flow_token": "1213767040178313",
                                                        "flow_action_data": {}
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            });
                            let config = {
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: 'https://graph.facebook.com/v18.0/531157636751620/messages',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': 'Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD'
                                },
                                data: data
                            };
                            axios.request(config)
                                .then((response) => {
                            })
                                .catch((error) => {
                                console.log(error);
                            });
                        }
                        if (type === 'text' && (text === null || text === void 0 ? void 0 : text.body)) {
                            await (0, whatsapp_service_1.sendConversationData)(from, text.body);
                        }
                    }
                    if (mobileNumber && ((_k = messages.button) === null || _k === void 0 ? void 0 : _k.text)) {
                        if (((_l = messages.button) === null || _l === void 0 ? void 0 : _l.text) === 'Request Demo') {
                            let marketingRecord = await vider_marketing_entity_1.default.findOne({ where: { mobileNumber } });
                            marketingRecord.requestedDemo = 'yes';
                            await marketingRecord.save();
                            const mobileNumberCountryCode = `91${mobileNumber}`;
                            const title = 'response for request demo';
                            const whatsappMessageBody = `
Thank you for your interest in taking demo!

please schedule your demo below

https://vider.in/demo.html

Thank you,
Team Vider`;
                            await (0, whatsapp_service_1.sendWhatsAppMarketingTextMessage)(mobileNumber, whatsappMessageBody, title);
                        }
                        else if (((_m = messages.button) === null || _m === void 0 ? void 0 : _m.text) === 'Visit Website') {
                            let marketingRecord = await vider_marketing_entity_1.default.findOne({ where: { mobileNumber } });
                            marketingRecord.visitWebsite = 'yes';
                            await marketingRecord.save();
                            const mobileNumberCountryCode = `91${mobileNumber}`;
                            const title = 'visit website';
                            const whatsappMessageBody = `
Thank you for your interest to visit our website,

Click here to visit: https://vider.in/

Thank you,
Team Vider`;
                            await (0, whatsapp_service_1.sendWhatsAppMarketingTextMessage)(mobileNumber, whatsappMessageBody, title);
                        }
                        else if (((_o = messages.button) === null || _o === void 0 ? void 0 : _o.text) === 'Contact us') {
                            let marketingRecord = await vider_marketing_entity_1.default.findOne({ where: { mobileNumber } });
                            marketingRecord.contactUs = 'yes';
                            await marketingRecord.save();
                            const title = 'contact us';
                            const whatsappMessageBody = `
Thank you for your interest to contact us,

We will Reach out to you very shortly,

Meanwhile if you want to contact us 
Mobile : 9171121121 | 9044401818
Mail   : <EMAIL>

Thank you,
Team Vider`;
                            await (0, whatsapp_service_1.sendWhatsAppMarketingTextMessage)(mobileNumber, whatsappMessageBody, title);
                        }
                    }
                }
            }
            if (((_p = changes[0]) === null || _p === void 0 ? void 0 : _p.field) === 'message_template_status_update') {
                const templateData = (_q = changes[0]) === null || _q === void 0 ? void 0 : _q.value;
                if (templateData) {
                    const { event: status, message_template_id: templateId, message_template_name: templateName, reason, } = templateData;
                    const whatsappBusinessId = (_s = (_r = payload.entry) === null || _r === void 0 ? void 0 : _r[0]) === null || _s === void 0 ? void 0 : _s.id;
                    const metaStatus = new metaTemplatesStatusWebhook_1.default();
                    metaStatus.templateName = templateName;
                    metaStatus.templateId = String(templateId);
                    metaStatus.status = status;
                    metaStatus.reason = reason || '';
                    metaStatus.whatsappBusinessId = whatsappBusinessId;
                    metaStatus.createdAt = new Date();
                    await metaStatus.save();
                }
            }
            if (statusData) {
                const { status, recipient_id, errors } = statusData;
                const phoneNumber = recipient_id;
                if (phoneNumber) {
                    let marketingRecord = await vider_marketing_entity_1.default.findOne({ where: { mobileNumber: phoneNumber } });
                    if (marketingRecord) {
                        marketingRecord.status = status;
                        if (status === 'failed' && ((_t = errors === null || errors === void 0 ? void 0 : errors[0]) === null || _t === void 0 ? void 0 : _t.title)) {
                            marketingRecord.errorTitle = errors[0].title;
                        }
                        await marketingRecord.save();
                    }
                    else {
                    }
                }
            }
        }
        catch (error) {
            console.error("Error in afterInsert:", error);
        }
    }
    async handleMedia(mediaId, mobileNumber, document) {
        try {
            const mediaUrlResponse = await axios.get(`https://graph.facebook.com/v17.0/${mediaId}`, {
                headers: {
                    Authorization: `Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD`,
                },
            });
            if (!mediaUrlResponse.data.url) {
                console.error('Failed to retrieve media URL');
                return;
            }
            const mediaUrl = mediaUrlResponse.data.url;
            const mediaResponse = await axios.get(mediaUrl, {
                headers: { Authorization: ` Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD` },
                responseType: 'arraybuffer',
            });
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const timestamp = new Date().toISOString();
            const fileExtension = (document === null || document === void 0 ? void 0 : document.mime_type.includes('/')) ? `.${document === null || document === void 0 ? void 0 : document.mime_type.split('/')[1]}` : '';
            const s3Params = {
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: `whatsapp-conversation-${timestamp}${fileExtension}`,
                Body: mediaResponse === null || mediaResponse === void 0 ? void 0 : mediaResponse.data,
                ContentType: document === null || document === void 0 ? void 0 : document.mime_type,
            };
            const uploadResult = await s3.upload(s3Params).promise();
            const pdfLink = uploadResult.Location;
            await (0, whatsapp_service_1.sendDocumentConversationData)(mobileNumber, uploadResult, document === null || document === void 0 ? void 0 : document.mime_type);
        }
        catch (error) {
            console.error(`Error fetching ${document === null || document === void 0 ? void 0 : document.mime_type}:`, error);
        }
    }
};
ViderMarketingWebhookSubscriber = __decorate([
    (0, typeorm_1.EventSubscriber)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], ViderMarketingWebhookSubscriber);
exports.ViderMarketingWebhookSubscriber = ViderMarketingWebhookSubscriber;
//# sourceMappingURL=viderMarketingWebhook.subscriber.js.map