"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrDashboardController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
const dashboard_services_1 = require("../service/dashboard.services");
let GstrDashboardController = class GstrDashboardController {
    constructor(service) {
        this.service = service;
    }
    getNoticeOrdersDateCount(req, query) {
        const { userId } = req.user;
        return this.service.getNoticeOrdersDateCount(userId, query);
    }
    getAdditionalNoticeOrdersDateCount(req, query) {
        const { userId } = req.user;
        return this.service.getAdditionalNoticeOrdersDateCount(userId, query);
    }
    getIncometaxConfigStatus(req, query) {
        const { userId } = req.user;
        return this.service.getGstrConfigStatus(userId, query.startDates);
    }
    clientCheck(req, query) {
        const { userId } = req.user;
        return this.service.clientCheck(userId, query);
    }
    async exportGstrInvalid(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportGstrInvalid(userId, query);
    }
    getAdditionalNoticesStatCount(req, query) {
        const { userId } = req.user;
        return this.service.getAdditionalNoticesStatCount(userId, query);
    }
    outstandingDemandStats(req, query) {
        const { userId } = req.user;
        return this.service.outstandingDemandStats(userId, query);
    }
    getAdditionalNoticeStats(req, query) {
        const { userId } = req.user;
        return this.service.getAdditionalNoticeStats(userId, query);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('not-ord'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "getNoticeOrdersDateCount", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('add-not-ord'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "getAdditionalNoticeOrdersDateCount", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/config-status'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "getIncometaxConfigStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('verification'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "clientCheck", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/exportGstrInvalid'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrDashboardController.prototype, "exportGstrInvalid", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('add-not-ord-stats'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "getAdditionalNoticesStatCount", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('demand-stats'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "outstandingDemandStats", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('additional-notice-stats'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrDashboardController.prototype, "getAdditionalNoticeStats", null);
GstrDashboardController = __decorate([
    (0, common_1.Controller)('gstr-dashboard'),
    __metadata("design:paramtypes", [dashboard_services_1.GstrDashboardService])
], GstrDashboardController);
exports.GstrDashboardController = GstrDashboardController;
//# sourceMappingURL=dashboard.controller.js.map