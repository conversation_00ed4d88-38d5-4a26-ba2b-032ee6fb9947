"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentCategoryService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const user_entity_1 = require("../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const document_category_entity_1 = require("./entity/document-category.entity");
let DocumentCategoryService = class DocumentCategoryService {
    constructor(eventEmitter) {
        this.eventEmitter = eventEmitter;
    }
    async create(userId, data) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        data.name = data.name.trim();
        let documentCategoryExists = await (0, typeorm_1.createQueryBuilder)(document_category_entity_1.default, 'documentCategory')
            .leftJoin('documentCategory.organization', 'organization')
            .where('documentCategory.name = :name', { name: data.name })
            .andWhere('organization.id = :id', { id: user.organization.id })
            .getMany();
        if (documentCategoryExists.length) {
            throw new common_1.BadRequestException('Document Category is Already Exists !');
        }
        const documentCategory = new document_category_entity_1.default();
        documentCategory.name = (_a = (' ' + data['name'])) === null || _a === void 0 ? void 0 : _a.trim();
        documentCategory.type = data['type'];
        documentCategory.organization = user.organization;
        documentCategory['userId'] = user.id;
        await documentCategory.save();
        return documentCategory;
    }
    async get(userId, query) {
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const documentCategory = (0, typeorm_1.createQueryBuilder)(document_category_entity_1.default, 'documentCategory')
            .leftJoin('documentCategory.organization', 'organization')
            .where('organization.id = :organizationId', { organizationId: user.organization.id });
        if (query.search) {
            documentCategory.andWhere('lead.name like :search OR lead.email LIKE :search OR lead.mobileNumber LIKE :search', {
                search: `%${query.search}%`,
            });
        }
        let result = await documentCategory.getMany();
        return result;
    }
    async delete(ids) {
        await (0, typeorm_1.getRepository)(document_category_entity_1.default)
            .createQueryBuilder()
            .delete()
            .where('id IN (:...ids)', { ids: ids })
            .execute();
        return { success: true };
    }
};
DocumentCategoryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_emitter_1.EventEmitter2])
], DocumentCategoryService);
exports.DocumentCategoryService = DocumentCategoryService;
//# sourceMappingURL=doucment-category.service.js.map