"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuartersBetweenFinancialYear = exports.getFormValidQuarters = exports.getFinancialQuarter = exports.getQuartersBetween = exports.getBehindQuarter = exports.getAllotmentQuarter = exports.tanFormTypes = exports.getQuarterFromMonth = exports.getCurrentQuarter = void 0;
function getCurrentQuarter() {
    const currentMonth = new Date().getMonth() + 1;
    return getQuarterFromMonth(currentMonth);
}
exports.getCurrentQuarter = getCurrentQuarter;
function getQuarterFromMonth(month) {
    if (month >= 4 && month <= 6)
        return '1';
    if (month >= 7 && month <= 9)
        return '2';
    if (month >= 10 && month <= 12)
        return '3';
    return '4';
}
exports.getQuarterFromMonth = getQuarterFromMonth;
exports.tanFormTypes = ["Form 24Q", "Form 26Q", "Form 27Q", "Form 27EQ"];
function getAllotmentQuarter(month) {
    if (month >= 4 && month <= 6)
        return 'Q1';
    if (month >= 7 && month <= 9)
        return 'Q2';
    if (month >= 10 && month <= 12)
        return 'Q3';
    return 'Q4';
}
exports.getAllotmentQuarter = getAllotmentQuarter;
function getBehindQuarter(quarter) {
    if (quarter === "Q1") {
        return "Q4";
    }
    else if (quarter === "Q2") {
        return "Q1";
    }
    else if (quarter === "Q3") {
        return "Q2";
    }
    else if (quarter === "Q4") {
        return "Q3";
    }
}
exports.getBehindQuarter = getBehindQuarter;
function getQuartersBetween(startYear, startQuarterIndex, endYear, endQuarterIndex) {
    const quarters = [];
    let year = startYear;
    let quarterIndex = startQuarterIndex;
    while (year < endYear || (year === endYear && quarterIndex <= endQuarterIndex)) {
        quarters.push({ year, quarter: `Q${quarterIndex}` });
        quarterIndex++;
        if (quarterIndex > 4) {
            quarterIndex = 1;
            year++;
        }
    }
    return quarters;
}
exports.getQuartersBetween = getQuartersBetween;
function getFinancialQuarter(date) {
    const month = date.getMonth() + 1;
    if (month >= 4 && month <= 6)
        return 1;
    if (month >= 7 && month <= 9)
        return 2;
    if (month >= 10 && month <= 12)
        return 3;
    return 4;
}
exports.getFinancialQuarter = getFinancialQuarter;
function getFormValidQuarters(startDate, financialYear) {
    const validQuarters = [];
    const startQuarter = parseInt(getQuarterFromMonth(new Date(startDate).getMonth() + 1));
    const startYear = new Date(startDate).getFullYear();
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentQuarter = parseInt(getQuarterFromMonth(today.getMonth() + 1));
    const financialStartDate = `${financialYear}-04-01`;
    const financialEndDate = `${financialYear + 1}-03-31`;
    if (new Date(startDate) < new Date(financialStartDate)) {
        validQuarters.push("Q1", "Q2", "Q3", "Q4");
    }
    else if (new Date(startDate) > new Date(financialEndDate)) {
        return validQuarters;
    }
    else if (new Date(startDate) >= new Date(financialStartDate) &&
        new Date(startDate) <= new Date(financialEndDate)) {
        if (financialYear === currentYear) {
            for (let quarter = startQuarter; quarter < currentQuarter; quarter++) {
                validQuarters.push(`Q${quarter}`);
            }
        }
        else {
            for (let quarter = startQuarter; quarter <= 4; quarter++) {
                validQuarters.push(`Q${quarter}`);
            }
        }
    }
    return validQuarters;
}
exports.getFormValidQuarters = getFormValidQuarters;
function getQuartersBetweenFinancialYear(dateOfAllotment, financialYear, currentYear, currentQuarterIndex) {
    const quarters = [];
    const allotmentQuarterIndex = parseInt(getQuarterFromMonth(dateOfAllotment.getMonth() + 1));
    const financialYearStart = new Date(`${financialYear}-04-01`);
    const financialYearEnd = new Date(`${financialYear + 1}-03-31`);
    const currentYearStart = new Date(`${currentYear}-04-01`);
    if (dateOfAllotment > financialYearEnd) {
        return [];
    }
    else if (dateOfAllotment < financialYearStart) {
        for (let i = 1; i <= 4; i++) {
            quarters.push({ year: financialYear, quarter: `Q${i}` });
        }
    }
    else if (dateOfAllotment >= financialYearStart && dateOfAllotment <= financialYearEnd) {
        const startQuarter = allotmentQuarterIndex;
        const endQuarter = financialYear === currentYear ? currentQuarterIndex : 4;
        for (let i = startQuarter; i <= endQuarter; i++) {
            quarters.push({ year: financialYear, quarter: `Q${i}` });
        }
    }
    else if (allotmentQuarterIndex < currentQuarterIndex &&
        dateOfAllotment >= new Date(`${currentYear}-01-01`) &&
        dateOfAllotment < currentYearStart) {
        for (let i = allotmentQuarterIndex; i < currentQuarterIndex; i++) {
            quarters.push({ year: financialYear, quarter: `Q${i}` });
        }
    }
    return quarters;
}
exports.getQuartersBetweenFinancialYear = getQuartersBetweenFinancialYear;
//# sourceMappingURL=tan-utils.js.map