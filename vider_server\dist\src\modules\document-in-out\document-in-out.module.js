"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentInOutModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const document_in_out_entity_1 = require("./entity/document-in-out.entity");
const document_in_out_controller_1 = require("./document-in-out.controller");
const doucment_in_out_service_1 = require("./doucment-in-out.service");
const documents_data_entity_1 = require("./entity/documents-data.entity");
const storage_service_1 = require("../storage/storage.service");
const upload_service_1 = require("../storage/upload.service");
const bharath_upload_service_1 = require("../storage/bharath-upload.service");
const bharath_storage_service_1 = require("../storage/bharath-storage.service");
const onedrive_storage_service_1 = require("../ondrive-storage/onedrive-storage.service");
const attachments_service_1 = require("../tasks/services/attachments.service");
const documentInOut_subscriber_1 = require("../../event-subscribers/documentInOut.subscriber");
const googledrive_storage_service_1 = require("../ondrive-storage/googledrive-storage.service");
let DocumentInOutModule = class DocumentInOutModule {
};
DocumentInOutModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([document_in_out_entity_1.default, documents_data_entity_1.default])],
        controllers: [document_in_out_controller_1.DocumentInOutController],
        providers: [doucment_in_out_service_1.DocumentInOutService,
            storage_service_1.StorageService,
            upload_service_1.AwsService,
            bharath_upload_service_1.BharathCloudService,
            bharath_storage_service_1.BharathStorageService,
            onedrive_storage_service_1.OneDriveStorageService,
            googledrive_storage_service_1.GoogleDriveStorageService,
            attachments_service_1.AttachmentsService,
            documentInOut_subscriber_1.DocumentInOutSubscriber
        ],
    })
], DocumentInOutModule);
exports.DocumentInOutModule = DocumentInOutModule;
//# sourceMappingURL=document-in-out.module.js.map