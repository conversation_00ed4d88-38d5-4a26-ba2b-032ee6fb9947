import { TanAutomationService } from '../service/tan-automation.service';
export declare class TanAutomationController {
    private service;
    constructor(service: TanAutomationService);
    findAll(req: any, query: any): Promise<{
        count: number;
        data: import("../entity/tan-client-credentials.entity").default[];
    }>;
    exportIncomeTaxTanClients(req: any, body: any): Promise<import("exceljs").Buffer>;
    addClientTanCredentials(body: any, req: any): Promise<void>;
    getAllClients(req: any, query: any): Promise<any>;
    getIncomeTaxProfile(id: number, req: any): Promise<{
        profileDetails: import("../entity/tan-profile.entity").default;
        lastCompletedMachine: import("../../automation/entities/automation_machines.entity").default;
        checkClientCredentials: boolean;
        keyPersonDetails: import("../entity/tan-key-person.entity").default[];
        clientCredential: import("../entity/tan-client-credentials.entity").default;
        lastCompletedTracesMachine: import("../../automation/entities/automation_machines.entity").default;
    }>;
    clientEChallan(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan-e-challan.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientChallan(req: any, body: any): Promise<import("exceljs").Buffer>;
    findEchallan(req: any, id: number): Promise<import("../entity/tan-e-challan.entity").default>;
    findEchallans(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan-e-challan.entity").default[];
    }>;
    exportTanIncomeTaxChallans(req: any, body: any): Promise<import("exceljs").Buffer>;
    getClientform(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan-income-tax-forms.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientForm(req: any, body: any): Promise<import("exceljs").Buffer>;
    findForm(req: any, id: number): Promise<{
        myCaDetails: any;
        id: number;
        formCd: string;
        formDesc: string;
        formName: string;
        formShortName: string;
        ackDt: string;
        ackNum: string;
        caMembershipNo: string;
        caName: string;
        filingTypeCd: string;
        fillingMode: string;
        submitBy: string;
        submitUserId: string;
        udinNum: string;
        activities: object;
        storageFiles: object;
        verStatus: string;
        refYear: string;
        refYearType: string;
        formStatus: string;
        financialQuarter: string;
        isUdinApplicable: boolean;
        transactionNo: string;
        tempAckNo: string;
        createdAt: string;
        updatedAt: string;
        organizationId: number;
        clientId: number;
        transactionType: string;
        tanClientCredentials: import("../entity/tan-client-credentials.entity").default;
        status: string;
        dtOfPrcng: string;
    }>;
    findAllForms(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan-income-tax-forms.entity").default[];
    }>;
    exportTanIncomeTaxForms(req: any, body: any): Promise<import("exceljs").Buffer>;
    getActivityLogData(req: any, id: number, query: any): Promise<{
        result: import("../../automation/entities/automation_machines.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientReport(req: any, query: any): Promise<{
        data: import("../../automation/entities/automation_machines.entity").default[];
        count: number;
    }>;
    exportTanSyncStatus(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportTanIncomeTaxReports(req: any, body: any): Promise<import("exceljs").Buffer>;
    getclientAutoStatus(id: number, req: any): Promise<{
        lastCompletedMachine: import("../../automation/entities/automation_machines.entity").default;
        accessDenied: boolean;
        totalInqueueCount: number;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    findMycas(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan-my-cas.entity").default[];
    }>;
    exportTanIncomeTaxMycas(req: any, body: any): Promise<import("exceljs").Buffer>;
    getMycaFormTypes(req: any): Promise<any[]>;
    clientMycas(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan-my-cas.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportTanClientMyCas(req: any, body: any): Promise<import("exceljs").Buffer>;
    update(id: number, body: any, req: any): Promise<import("../entity/tan-client-credentials.entity").default>;
    getClientTraceCommunications(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan-communication-inbox.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportClientTanTracesInbox(req: any, body: any): Promise<import("exceljs").Buffer>;
    findAllTraceCommunication(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan-communication-inbox.entity").default[];
    }>;
    exportClientTracesInbox(req: any, body: any): Promise<import("exceljs").Buffer>;
    getActivityLogTracesData(req: any, id: number, query: any): Promise<{
        result: import("../../automation/entities/automation_machines.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        result?: undefined;
    }>;
    getclientAutoTracesStatus(id: number, req: any): Promise<{
        lastCompletedMachine: import("../../automation/entities/automation_machines.entity").default;
        accessDenied: boolean;
        totalInqueueCount: number;
    } | {
        accessDenied: boolean;
        lastCompletedMachine?: undefined;
        totalInqueueCount?: undefined;
    }>;
    getTraceReport(req: any, query: any): Promise<{
        data: import("../../automation/entities/automation_machines.entity").default[];
        count: number;
    }>;
    exportTanTraceSyncStatus(req: any, body: any): Promise<import("exceljs").Buffer>;
    findFyaTempNotices(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan_temp_epro_fya.entity").default[];
    }>;
    findFyiTempNotices(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan_temp_epro_fyi.entity").default[];
    }>;
    getExcelFyaSections(req: any): Promise<any[]>;
    getExcelFyiSections(req: any): Promise<any[]>;
    getClientExcelProceedingFyi(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan_temp_epro_fyi.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    getClientExcelProceedingFya(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/tan_temp_epro_fya.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    getExcelFyiNotices(req: any, query: any): Promise<{
        count: number;
        result: {
            eproType: string;
            id: number;
            clientId: number;
            client: import("../../clients/entity/client.entity").default;
            organizationId: number;
            tanClientCredentialsId: number;
            proceedingName: string;
            pan: string;
            ay: string;
            proceedingLimitationDate: string;
            proceedingStatus: string;
            proceedingConcludedDate: string;
            noticeDin: string;
            noticeSentDate: string;
            noticeSection: string;
            dateOfCompliance: string;
            dateResponseSubmitted: string;
            createdAt: Date;
            updatedAt: Date;
            uuid: string;
            type: import("../entity/tan_temp_epro_fya.entity").EproceedingTypeEnum;
        }[];
    }>;
    findAllDemands(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/trace-outstanding-deman.entity").default[];
    }>;
    findDeamnd(req: any, id: number): Promise<import("../entity/trace-outstanding-deman.entity").default>;
    getClientDemand(id: number, query: any, req: any): Promise<{
        count: number;
        result: import("../entity/trace-outstanding-deman.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportDemands(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportClientDemand(req: any, body: any): Promise<import("exceljs").Buffer>;
}
