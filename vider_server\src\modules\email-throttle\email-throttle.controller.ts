import { Controller, Post, Body, Get, BadRequestException } from '@nestjs/common';
import { EmailThrottleService } from './email-throttle.service';

interface QueueEmailDto {
  to: string;
  subject: string;
  body: string;
  organizationId?: number;
  smtpConfig?: any;
  attachments?: any[];
}

interface BulkQueueEmailDto {
  emails: QueueEmailDto[];
}

// Email validation utility
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Input sanitization utility
function sanitizeInput(input: string): string {
  if (!input) return '';
  return input.trim().replace(/[<>]/g, ''); // Basic XSS prevention
}

@Controller('email-throttle')
export class EmailThrottleController {
  constructor(private readonly emailThrottleService: EmailThrottleService) {}

  @Post('queue')
  async queueEmails(@Body() body: QueueEmailDto) {
    // Validate email address
    if (!isValidEmail(body.to)) {
      throw new BadRequestException('Invalid email address');
    }

    // Sanitize inputs
    const sanitizedSubject = sanitizeInput(body.subject);
    const sanitizedBody = sanitizeInput(body.body);

    // Validate required fields
    if (!sanitizedSubject || !sanitizedBody) {
      throw new BadRequestException('Subject and body are required');
    }

    await this.emailThrottleService.enqueueEmail(
      body.to,
      sanitizedSubject,
      sanitizedBody,
      body.organizationId,
      body.smtpConfig,
      body.attachments
    );
    return { message: `Queued email to ${body.to}` };
  }

  // Optional: send multiple at once
  @Post('bulk-queue')
  async queueBulkEmails(@Body() body: BulkQueueEmailDto) {
    if (!body.emails || !Array.isArray(body.emails) || body.emails.length === 0) {
      throw new BadRequestException('Emails array is required and cannot be empty');
    }

    const validEmails = [];
    const invalidEmails = [];

    for (const e of body.emails) {
      // Validate each email
      if (!isValidEmail(e.to)) {
        invalidEmails.push({ email: e.to, reason: 'Invalid email address' });
        continue;
      }

      const sanitizedSubject = sanitizeInput(e.subject);
      const sanitizedBody = sanitizeInput(e.body);

      if (!sanitizedSubject || !sanitizedBody) {
        invalidEmails.push({ email: e.to, reason: 'Subject and body are required' });
        continue;
      }

      try {
        await this.emailThrottleService.enqueueEmail(
          e.to,
          sanitizedSubject,
          sanitizedBody,
          e.organizationId,
          e.smtpConfig,
          e.attachments
        );
        validEmails.push(e.to);
      } catch (error) {
        invalidEmails.push({ email: e.to, reason: error.message });
      }
    }

    return {
      message: `${validEmails.length} emails queued successfully.`,
      queued: validEmails.length,
      failed: invalidEmails.length,
      invalidEmails: invalidEmails
    };
  }

  @Get('status')
  async getQueueStatus() {
    return await this.emailThrottleService.getQueueStatus();
  }

  @Get('failed')
  async getFailedEmails() {
    return await this.emailThrottleService.getFailedEmails();
  }

  @Get('config')
  async getConfiguration() {
    return await this.emailThrottleService.getConfiguration();
  }
}
