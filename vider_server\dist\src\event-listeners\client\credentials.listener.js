"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CredentialsListener = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const client_entity_1 = require("../../modules/clients/entity/client.entity");
const notify_1 = require("../../notifications/notify");
const user_entity_1 = require("../../modules/users/entities/user.entity");
const actions_1 = require("../actions");
let CredentialsListener = class CredentialsListener {
    async handleCredentialCreate(event) {
        try {
            const { userId, clientId } = event;
            let user = await user_entity_1.User.findOne({
                where: {
                    id: userId,
                },
            });
            let client = await client_entity_1.default.findOne({
                where: {
                    id: clientId,
                },
                relations: ['clientManager'],
            });
            if (!client.clientManager)
                return;
            let notification = {
                title: `Client Credentials Added`,
                body: `The credentials for client '${client === null || client === void 0 ? void 0 : client.displayName}' has been added by ${user === null || user === void 0 ? void 0 : user.fullName}`,
            };
            await (0, notify_1.sendNotification)([client.clientManager.id], notification);
        }
        catch (err) {
            console.log(err);
        }
    }
    async handleCredentialUpdate(event) {
        try {
            const { userId, clientId } = event;
            let user = await user_entity_1.User.findOne({
                where: {
                    id: userId,
                },
            });
            let client = await client_entity_1.default.findOne({
                where: {
                    id: clientId,
                },
                relations: ['clientManager'],
            });
            if (!client.clientManager)
                return;
            let notification = {
                title: `Client Credentials Updated`,
                body: `The credentials for client '${client === null || client === void 0 ? void 0 : client.displayName}' has been updated by ${user === null || user === void 0 ? void 0 : user.fullName}`,
            };
            await (0, notify_1.sendNotification)([client.clientManager.id], notification);
        }
        catch (err) {
            console.log(err);
        }
    }
};
__decorate([
    (0, event_emitter_1.OnEvent)(actions_1.Event_Actions.CREDENTIAL_CREATED, { async: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CredentialsListener.prototype, "handleCredentialCreate", null);
__decorate([
    (0, event_emitter_1.OnEvent)(actions_1.Event_Actions.CREDENTIAL_UPDATED, { async: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CredentialsListener.prototype, "handleCredentialUpdate", null);
CredentialsListener = __decorate([
    (0, common_1.Injectable)()
], CredentialsListener);
exports.CredentialsListener = CredentialsListener;
//# sourceMappingURL=credentials.listener.js.map