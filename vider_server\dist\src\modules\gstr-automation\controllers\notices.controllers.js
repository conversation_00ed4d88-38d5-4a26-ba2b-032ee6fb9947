"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
const notices_service_1 = require("../service/notices.service");
const attachments_gst_service_1 = require("../service/attachments-gst.service");
let GstrController = class GstrController {
    constructor(service, attachmentGstService) {
        this.service = service;
        this.attachmentGstService = attachmentGstService;
    }
    async getOrderNotices(req, id, query) {
        const { userId } = req.user;
        return this.service.getOrderNotices(userId, id, query);
    }
    async gstClientNoticeandordersExport(req, body) {
        const { userId } = req.user;
        const query = body;
        return this.service.gstClientNoticeandordersExport(userId, query);
    }
    async getOrderNotice(req, id) {
        const { userId } = req.user;
        return this.service.getOrderNotice(userId, id);
    }
    async getGstrAdditionalDeails(req, id) {
        const { userId } = req.user;
        return this.service.getGstrAdditionalDeailss(userId, id);
    }
    async getGstrProfile(req, id) {
        const { userId } = req.user;
        return this.service.getGstrProfile(userId, id);
    }
    async getGstrClientCompliance(req, id) {
        const { userId } = req.user;
        return this.service.getGstrClientCompliance(userId, id);
    }
    async getAddNoticeAndOrders(req, query) {
        const { userId } = req.user;
        return this.service.getAddNoticeAndOrders(userId, query);
    }
    async exportAdditionalGstNoticeAndOrders(req, body) {
        const { userId } = req.user;
        const query = body;
        return this.service.exportAdditionalGstNoticeAndOrders(userId, query);
    }
    async getNoticeAndOrders(req, query) {
        const { userId } = req.user;
        return this.service.getNoticeAndOrders(userId, query);
    }
    async exportGstNoticeAndOrders(req, body) {
        const { userId } = req.user;
        const query = body;
        return this.service.exportGstNoticeAndOrders(userId, query);
    }
    getclientReport(req, query) {
        const { userId } = req.user;
        return this.service.getGstrReport(userId, query);
    }
    async exportGstClientReport(req, body) {
        const { userId } = req.user;
        const query = body;
        return this.service.exportGstClientReport(userId, query);
    }
    async getGstrAdditionalNoticeOrders(req, id, query) {
        const { userId } = req.user;
        return this.service.getGstrAdditionalNoticeOrders(userId, id, query);
    }
    async exportreferenceBasedNotices(req, body) {
        const { userId } = req.user;
        const query = body;
        return this.service.exportreferenceBasedNotices(userId, query);
    }
    getFyaEvents(req, query) {
        const { userId } = req.user;
        return this.service.getNoticeAndOrderDueDateEvents(userId, query.startDates);
    }
    getNoticeAndOrderIssueDateEvents(req, query) {
        const { userId } = req.user;
        return this.service.getNoticeAndOrderIssueDateEvents(userId, query.startDates);
    }
    getAdditionalNoticeOrderIssueDateEvents(req, query) {
        const { userId } = req.user;
        return this.service.getAdditionalNoticeOrderIssueDateEvents(userId, query.startDates);
    }
    getAdditionalNoticeOrderDueDateEvents(req, query) {
        const { userId } = req.user;
        return this.service.getAdditionalNoticeOrderDueDateEvents(userId, query.startDates);
    }
    getGstrUpdates(req, query) {
        const { userId } = req.user;
        return this.service.getGstrUpdates(userId, query);
    }
    findGstrUpdateItem(req, id) {
        const { userId } = req.user;
        return this.service.findGstrUpdateItem(userId, id);
    }
    async organizationScheduling(req) {
        const { userId } = req.user;
        return this.service.organizationGstrScheduling(userId);
    }
    async getGstrDemands(req, query) {
        const { userId } = req.user;
        return this.service.getAllGstrDemands(userId, query);
    }
    async getClientGstrDemand(req, id) {
        const { userId } = req.user;
        return this.service.getClientDemands(userId, id);
    }
    async getGstrAllLedgers(req, query) {
        const { userId } = req.user;
        return this.service.getAllGstrLedgers(userId, query);
    }
    async getClientLedgers(req, query, id) {
        const { userId } = req.user;
        return this.service.getClientLedgers(userId, query, id);
    }
    async exportDemands(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportDemands(userId, query);
    }
    async exportLedgers(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportLedgers(userId, query);
    }
    async exportClientDemand(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportClientDemand(userId, query);
    }
    async exportClientLedger(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportClientLedger(userId, query);
    }
    getCaseTypeNames(req) {
        const { userId } = req.user;
        return this.service.getCaseTypeNames(userId);
    }
    getCaseFolderTypeNames(req) {
        const { userId } = req.user;
        return this.service.getCaseFolderTypeNames(userId);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('order-notices/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getOrderNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('export-gstr-clientnotices'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "gstClientNoticeandordersExport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('order-notice/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getOrderNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('additional-notice/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrAdditionalDeails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('gstr-profile/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('gstr-compliance/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrClientCompliance", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('gstr-add'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getAddNoticeAndOrders", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('export-gstr-add'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportAdditionalGstNoticeAndOrders", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('gstr-notices-orders'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getNoticeAndOrders", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('export-gstr-notices-orders'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportGstNoticeAndOrders", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('reports'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getclientReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('export-reports'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportGstClientReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('additional/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrAdditionalNoticeOrders", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('export-referencenotices'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportreferenceBasedNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/notice-and-order-dueDate-events'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getFyaEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/notice-and-order-issue-events'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getNoticeAndOrderIssueDateEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/add-notice-issue-events'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getAdditionalNoticeOrderIssueDateEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/add-notice-dueDate-events'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getAdditionalNoticeOrderDueDateEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('new-updates'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getGstrUpdates", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('update/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "findGstrUpdateItem", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('scheduling'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "organizationScheduling", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('demands'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrDemands", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('demand/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getClientGstrDemand", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('ledgers'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getGstrAllLedgers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('ledger/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Number]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "getClientLedgers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/demands-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportDemands", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/ledgers-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportLedgers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/export-clientdemand'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportClientDemand", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/export-clientledger'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrController.prototype, "exportClientLedger", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('case-type-name'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getCaseTypeNames", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('case-folder-type-name'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GstrController.prototype, "getCaseFolderTypeNames", null);
GstrController = __decorate([
    (0, common_1.Controller)('gstr'),
    __metadata("design:paramtypes", [notices_service_1.GstrService, attachments_gst_service_1.AttachmentGstService])
], GstrController);
exports.GstrController = GstrController;
//# sourceMappingURL=notices.controllers.js.map