import { GstrService } from '../service/notices.service';
import { AttachmentGstService } from '../service/attachments-gst.service';
export declare class GstrController {
    private service;
    protected attachmentGstService: AttachmentGstService;
    constructor(service: GstrService, attachmentGstService: AttachmentGstService);
    getOrderNotices(req: any, id: number, query: any): Promise<{
        count: number;
        result: import("../entity/noticeOrders.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    gstClientNoticeandordersExport(req: any, body: any): Promise<import("exceljs").Buffer>;
    getOrderNotice(req: any, id: number): Promise<import("../entity/noticeOrders.entity").default>;
    getGstrAdditionalDeails(req: any, id: number): Promise<{
        additionalData: import("../entity/gstrAdditionalOrdersAndNotices.entity").default;
        categorizedData: {
            type: string;
            records: unknown;
        }[];
    }>;
    getGstrProfile(req: any, id: number): Promise<{
        gstrProfile: import("../entity/gstrProfile.entity").default;
        lastCompletedMachine: import("../../automation/entities/automation_machines.entity").default;
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        gstrProfile?: undefined;
        lastCompletedMachine?: undefined;
    }>;
    getGstrClientCompliance(req: any, id: number): Promise<import("../../clients/entity/client.entity").default | {
        accessDenied: boolean;
    }>;
    getAddNoticeAndOrders(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrAdditionalOrdersAndNotices.entity").default[];
    }>;
    exportAdditionalGstNoticeAndOrders(req: any, body: any): Promise<import("exceljs").Buffer>;
    getNoticeAndOrders(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/noticeOrders.entity").default[];
    }>;
    exportGstNoticeAndOrders(req: any, body: any): Promise<import("exceljs").Buffer>;
    getclientReport(req: any, query: any): Promise<{
        data: import("../../automation/entities/automation_machines.entity").default[];
        count: number;
    }>;
    exportGstClientReport(req: any, body: any): Promise<import("exceljs").Buffer>;
    getGstrAdditionalNoticeOrders(req: any, id: number, query: any): Promise<{
        count: number;
        result: import("../entity/gstrAdditionalOrdersAndNotices.entity").default[];
        accessDenied: boolean;
    } | {
        accessDenied: boolean;
        count?: undefined;
        result?: undefined;
    }>;
    exportreferenceBasedNotices(req: any, body: any): Promise<import("exceljs").Buffer>;
    getFyaEvents(req: any, query: any): Promise<import("../entity/noticeOrders.entity").default[]>;
    getNoticeAndOrderIssueDateEvents(req: any, query: any): Promise<import("../entity/noticeOrders.entity").default[]>;
    getAdditionalNoticeOrderIssueDateEvents(req: any, query: any): Promise<import("../entity/gstrAdditionalOrdersAndNotices.entity").default[]>;
    getAdditionalNoticeOrderDueDateEvents(req: any, query: any): Promise<import("../entity/gstrAdditionalOrdersAndNotices.entity").default[]>;
    getGstrUpdates(req: any, query: any): Promise<import("../entity/gstr_update_tracker.entity").default[]>;
    findGstrUpdateItem(req: any, id: number): Promise<import("../entity/gstr_update_tracker.entity").default>;
    organizationScheduling(req: any): Promise<void>;
    getGstrDemands(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrDemands.entity").GstrOutstandingDemand[];
    }>;
    getClientGstrDemand(req: any, id: number): Promise<{
        result: import("../entity/gstrDemands.entity").GstrOutstandingDemand[];
        count: number;
    }>;
    getGstrAllLedgers(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrLedgersBalance.entity").GstrLedgerBalance[];
    }>;
    getClientLedgers(req: any, query: any, id: number): Promise<{
        result: import("../entity/gstrLedgersBalance.entity").GstrLedgerBalance[];
        count: number;
    }>;
    exportDemands(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportLedgers(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportClientDemand(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportClientLedger(req: any, body: any): Promise<import("exceljs").Buffer>;
    getCaseTypeNames(req: any): Promise<any[]>;
    getCaseFolderTypeNames(req: any): Promise<any[]>;
}
