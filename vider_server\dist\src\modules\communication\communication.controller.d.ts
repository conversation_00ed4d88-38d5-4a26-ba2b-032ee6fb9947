/// <reference types="multer" />
import { CommunicationService } from './communication.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IUploadBody } from '../storage/storage.controller';
export declare class CommunicationController {
    eventEmitter: EventEmitter2;
    service: CommunicationService;
    constructor(eventEmitter: EventEmitter2, service: CommunicationService);
    static withEventEmitter(eventEmitter: EventEmitter2): CommunicationController;
    static withService(service: CommunicationService): CommunicationController;
    create(req: any, body: any): Promise<import("./entity/client-group-broadcast.entity").default>;
    getClientGroup(req: any, query: any): Promise<{
        clientCount: number;
        id: number;
        groupName: string;
        broadcastActivities: import("./entity/broadcast-activity.entity").default[];
        description: string;
        createdAt: string;
        updatedAt: string;
        organization: import("../organization/entities/organization.entity").Organization;
        label: import("../labels/label.entity").default;
        clients: import("../clients/entity/client.entity").default[];
    }[]>;
    deleteClientGroup(id: number, req: any): Promise<import("typeorm").DeleteResult>;
    update(id: number, body: any, req: any): Promise<import("./entity/client-group-broadcast.entity").default>;
    addClientstoClientGroup(id: number, body: any, req: any): Promise<import("./entity/client-group-broadcast.entity").default>;
    removeClientsFromClientGroup(id: number, body: any, req: any): Promise<import("./entity/client-group-broadcast.entity").default>;
    getOneClientGroup(id: number, req: any, query: any): Promise<{
        clientGroup: import("./entity/client-group-broadcast.entity").default;
        clientCount: number;
        checkAccess: boolean;
    }>;
    createEmailTemplate(req: any, body: any): Promise<import("./entity/broadcast-email-templates-entity").default>;
    sendEmailTemplatetoclients(body: any): Promise<void>;
    getEmailTemplates(req: any, query: any): Promise<import("./entity/broadcast-email-templates-entity").default[] | {
        labelColor: string;
        id: number;
        title: string;
        labels: string;
        createdAt: string;
        broadcastActivities: import("./entity/broadcast-activity.entity").default[];
        content: string;
        subject: string;
        default: number;
        updatedAt: string;
        organization: import("../organization/entities/organization.entity").Organization;
        user: import("../users/entities/user.entity").User;
        label: import("../labels/label.entity").default;
    }[]>;
    getOneEmailTemplate(id: number, req: any): Promise<import("./entity/broadcast-email-templates-entity").default>;
    updateEmailTemplate(id: number, body: any, req: any): Promise<import("./entity/broadcast-email-templates-entity").default>;
    deleteEmailTemplate(id: number, req: any): Promise<import("typeorm").DeleteResult>;
    createBroadActivity(req: any, body: any): Promise<import("./entity/broadcast-activity.entity").default>;
    getBroadcastActivity(req: any, query: any): Promise<[import("./entity/broadcast-activity.entity").default[], number]>;
    getBroadcastActivityDetails(id: number, req: any, query: any): Promise<{
        getOneEmailTemplate: import("./entity/broadcast-activity.entity").default;
        checkAccess: boolean;
    }>;
    get(req: any, id: number, query: any): Promise<{
        count: number;
        result: import("../clients/entity/client.entity").default[];
    }>;
    uploadFile(file: Express.Multer.File, body: IUploadBody, req: any): Promise<unknown>;
}
