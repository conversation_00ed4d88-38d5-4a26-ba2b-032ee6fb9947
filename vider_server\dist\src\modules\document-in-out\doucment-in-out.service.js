"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentInOutService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const client_entity_1 = require("../clients/entity/client.entity");
const document_in_out_entity_1 = require("./entity/document-in-out.entity");
const documents_data_entity_1 = require("./entity/documents-data.entity");
const documents_data_entity_2 = require("./entity/documents-data.entity");
const kyb_entity_1 = require("../kyb/kyb.entity");
const client_group_entity_1 = require("../client-group/client-group.entity");
const lodash_1 = require("lodash");
const moment = require("moment");
const storage_entity_1 = require("../storage/storage.entity");
const storage_service_1 = require("../storage/storage.service");
const onedrive_storage_service_1 = require("../ondrive-storage/onedrive-storage.service");
const bharath_storage_service_1 = require("../storage/bharath-storage.service");
const attachments_service_1 = require("../tasks/services/attachments.service");
const re_use_1 = require("../../utils/re-use");
const newemails_1 = require("../../emails/newemails");
const organization_preferences_entity_1 = require("../organization-preferences/entity/organization-preferences.entity");
const viderWhatsappSessions_1 = require("../whatsapp/entity/viderWhatsappSessions");
const whatsapp_service_1 = require("../whatsapp/whatsapp.service");
const organization_entity_1 = require("../organization/entities/organization.entity");
let DocumentInOutService = class DocumentInOutService {
    constructor(storageService, oneDriveService, bharathService, attachementService) {
        this.storageService = storageService;
        this.oneDriveService = oneDriveService;
        this.bharathService = bharathService;
        this.attachementService = attachementService;
    }
    async create(userId, data) {
        var _a, _b, _c, _d, _e, _f, _g;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let documentInOut = new document_in_out_entity_1.default();
        documentInOut.documentType = data === null || data === void 0 ? void 0 : data.inOut;
        documentInOut.useType = data === null || data === void 0 ? void 0 : data.type;
        documentInOut.description = data === null || data === void 0 ? void 0 : data.description;
        documentInOut.client = ((_a = data === null || data === void 0 ? void 0 : data.clients) === null || _a === void 0 ? void 0 : _a.type) ? null : data === null || data === void 0 ? void 0 : data.clients;
        documentInOut.clientGroup = ((_b = data === null || data === void 0 ? void 0 : data.clients) === null || _b === void 0 ? void 0 : _b.type) ? data === null || data === void 0 ? void 0 : data.clients : null;
        documentInOut.organization = user.organization;
        documentInOut.receivedBy = data === null || data === void 0 ? void 0 : data.receivedBy;
        documentInOut.receivedTo = data === null || data === void 0 ? void 0 : data.receivedTo;
        documentInOut.givenBy = data === null || data === void 0 ? void 0 : data.givenTo;
        documentInOut.user = user;
        documentInOut.updatedBy = user;
        const mobileNumbers = ((_c = data === null || data === void 0 ? void 0 : data.clientWhatsapp) === null || _c === void 0 ? void 0 : _c.map((item, index) => ({
            id: index,
            number: item.number,
            countryCode: item.countryCode,
        }))) || [];
        const mail = ((_d = data === null || data === void 0 ? void 0 : data.clientMail) === null || _d === void 0 ? void 0 : _d.map((item, index) => ({
            id: index,
            email: item === null || item === void 0 ? void 0 : item.email,
        }))) || [];
        documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
        if ((_e = data === null || data === void 0 ? void 0 : data.keptat) === null || _e === void 0 ? void 0 : _e.name) {
            documentInOut.keptAtName = ((_f = data === null || data === void 0 ? void 0 : data.keptat) === null || _f === void 0 ? void 0 : _f.name) || null;
            documentInOut.keptAt = (data === null || data === void 0 ? void 0 : data.keptat) || null;
        }
        documentInOut.task = data === null || data === void 0 ? void 0 : data.task;
        const documentData = new documents_data_entity_2.default();
        documentData.type = 'original';
        documentData.returnable = "yes";
        documentData.mode = 'hard';
        documentData.manner = 'handdelivery';
        documentData.documentType = (_g = data === null || data === void 0 ? void 0 : data.inOut) !== null && _g !== void 0 ? _g : null;
        documentData.documentInOut = documentInOut;
        documentInOut.documentData = await Promise.all([documentData]);
        documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        const response = await documentInOut.save();
        await Promise.all(documentInOut.documentData.map(doc => doc.save()));
        return response === null || response === void 0 ? void 0 : response.id;
    }
    async createDocumentItem(userId, data) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let documentInOut = await (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .leftJoinAndSelect('documentInOut.client', 'client')
            .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
            .leftJoinAndSelect('documentInOut.documentData', 'documentData')
            .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
            .leftJoinAndSelect('documentData.kyb', 'kyb')
            .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
            .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
            .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
            .leftJoinAndSelect('documentInOut.task', 'task')
            .leftJoin('documentInOut.organization', 'organization')
            .where('documentInOut.id = :id', { id: data.id })
            .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
            .getOne();
        const documentData = new documents_data_entity_2.default();
        documentData.type = 'original';
        documentData.returnable = "yes";
        documentData.mode = 'hard';
        documentData.manner = 'handdelivery';
        documentData.documentType = (_a = data === null || data === void 0 ? void 0 : data.inOut) !== null && _a !== void 0 ? _a : null;
        documentData.documentInOut = documentInOut;
        await documentData.save();
        return documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.id;
    }
    async deleteDocumentItem(userId, data) {
        var _a;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const existingDocument = await documents_data_entity_2.default.findOne({ where: { id: data.id }, relations: ['kyb'] });
        if (existingDocument.kyb) {
            let kybs = await (0, typeorm_1.createQueryBuilder)(kyb_entity_1.default, 'kyb')
                .where('kyb.id = :id', { id: (_a = existingDocument === null || existingDocument === void 0 ? void 0 : existingDocument.kyb) === null || _a === void 0 ? void 0 : _a.id });
            const data = await kybs.getOne();
            await data.remove();
        }
        await existingDocument.remove();
    }
    async createAndSave(userId, data) {
        var _a, _b, _c, _d, _e, _f;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let documentInOut = new document_in_out_entity_1.default();
        documentInOut.documentType = data === null || data === void 0 ? void 0 : data.inOut;
        documentInOut.useType = data === null || data === void 0 ? void 0 : data.type;
        documentInOut.description = data === null || data === void 0 ? void 0 : data.description;
        documentInOut.client = ((_a = data === null || data === void 0 ? void 0 : data.clients) === null || _a === void 0 ? void 0 : _a.type) ? null : data === null || data === void 0 ? void 0 : data.clients;
        documentInOut.clientGroup = ((_b = data === null || data === void 0 ? void 0 : data.clients) === null || _b === void 0 ? void 0 : _b.type) ? data === null || data === void 0 ? void 0 : data.clients : null;
        documentInOut.organization = user.organization;
        documentInOut.receivedBy = data === null || data === void 0 ? void 0 : data.receivedBy;
        documentInOut.receivedTo = data === null || data === void 0 ? void 0 : data.receivedTo;
        documentInOut.givenBy = data === null || data === void 0 ? void 0 : data.givenTo;
        documentInOut.user = user;
        documentInOut.updatedBy = user;
        const mobileNumbers = ((_c = data === null || data === void 0 ? void 0 : data.clientWhatsapp) === null || _c === void 0 ? void 0 : _c.map((item, index) => ({
            id: index,
            number: item.number,
            countryCode: item.countryCode,
        }))) || [];
        const mail = ((_d = data === null || data === void 0 ? void 0 : data.clientMail) === null || _d === void 0 ? void 0 : _d.map((item, index) => ({
            id: index,
            email: item === null || item === void 0 ? void 0 : item.email,
        }))) || [];
        documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
        if ((_e = data === null || data === void 0 ? void 0 : data.keptat) === null || _e === void 0 ? void 0 : _e.name) {
            documentInOut.keptAtName = ((_f = data === null || data === void 0 ? void 0 : data.keptat) === null || _f === void 0 ? void 0 : _f.name) || null;
            documentInOut.keptAt = (data === null || data === void 0 ? void 0 : data.keptat) || null;
        }
        documentInOut.task = data === null || data === void 0 ? void 0 : data.task;
        if ((data === null || data === void 0 ? void 0 : data.type) === "kyb") {
            documentInOut.documentData = await Promise.all(((data === null || data === void 0 ? void 0 : data.documents) || []).map(async (item) => {
                var _a, _b, _c, _d, _e, _f, _g;
                const documentData = new documents_data_entity_2.default();
                documentData.type = (_a = item.type) !== null && _a !== void 0 ? _a : null;
                documentData.returnable = (_b = item.returnable) !== null && _b !== void 0 ? _b : null;
                documentData.mode = (_c = item.mode) !== null && _c !== void 0 ? _c : null;
                documentData.manner = (_d = item.manner) !== null && _d !== void 0 ? _d : null;
                documentData.documentInOut = documentInOut;
                let client = await client_entity_1.default.findOne({ where: { id: (_e = documentInOut.client) === null || _e === void 0 ? void 0 : _e.id } });
                let clientGroup = await client_group_entity_1.default.findOne({ where: { id: (_f = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _f === void 0 ? void 0 : _f.id } });
                let kyb = new kyb_entity_1.default();
                kyb.documentName = item.documentName === "custom" ? item === null || item === void 0 ? void 0 : item.customName : item.documentName;
                kyb.documentNumber = (_g = item.documentNumber) !== null && _g !== void 0 ? _g : null;
                kyb.client = client;
                kyb.clientGroup = clientGroup;
                kyb.user = user;
                kyb.documentsData = documentData;
                documentData.kyb = kyb;
                return documentData;
            }));
        }
        else {
            documentInOut.documentData = await Promise.all(((data === null || data === void 0 ? void 0 : data.documents) || []).map((item) => {
                var _a, _b, _c, _d, _e;
                const documentData = new documents_data_entity_2.default();
                documentData.type = (_a = item.type) !== null && _a !== void 0 ? _a : null;
                documentData.returnable = (_b = item.returnable) !== null && _b !== void 0 ? _b : null;
                documentData.mode = (_c = item.mode) !== null && _c !== void 0 ? _c : null;
                documentData.manner = (_d = item.manner) !== null && _d !== void 0 ? _d : null;
                documentData.documentType = (_e = data === null || data === void 0 ? void 0 : data.inOut) !== null && _e !== void 0 ? _e : null;
                documentData.documentInOut = documentInOut;
                documentData.documentName = item.documentName;
                documentData.documentCategory = item === null || item === void 0 ? void 0 : item.documentCategory;
                return documentData;
            }));
        }
        documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        await documentInOut.save();
        await Promise.all(documentInOut.documentData.map(doc => doc.save()));
        if ((data === null || data === void 0 ? void 0 : data.type) === "kyb") {
            await Promise.all(documentInOut.documentData.map(doc => { var _a; return (_a = doc.kyb) === null || _a === void 0 ? void 0 : _a.save(); }));
        }
    }
    async get(userId, query) {
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const documentInOut = (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .select([
            'documentInOut',
            'client.id',
            'client.displayName',
            'clientGroup.id',
            'clientGroup.displayName',
            'clientGroup.type',
            'task.id',
            'task.name',
            'task.taskNumber',
            'user.id',
            'user.fullName',
            'updatedBy.id',
            'updatedBy.fullName',
            'organization.id'
        ])
            .leftJoin('documentInOut.client', 'client')
            .leftJoin('documentInOut.clientGroup', 'clientGroup')
            .leftJoin('documentInOut.task', 'task')
            .leftJoin('documentInOut.user', 'user')
            .leftJoin('documentInOut.updatedBy', 'updatedBy')
            .leftJoin('documentInOut.organization', 'organization')
            .where('organization.id = :organizationId', { organizationId: user.organization.id });
        const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                documentId: 'documentInOut.documentId',
                useType: 'documentInOut.useType',
                displayName: 'client.displayName',
                name: 'task.name',
                createdBy: 'user.fullName',
                updatedBy: 'updatedBy.fullName',
                updatedAt: 'documentInOut.updatedDateTime'
            };
            const column = columnMap[sort.column] || sort.column;
            documentInOut.orderBy(column, sort.direction.toUpperCase());
        }
        else {
            documentInOut.orderBy('documentInOut.updatedDateTime', 'DESC');
        }
        ;
        if (query.type !== "all") {
            documentInOut.andWhere('documentInOut.documentType = :useType', {
                useType: query.type,
            });
        }
        if (query.search) {
            documentInOut.andWhere('client.displayName like :search or clientGroup.displayName like :search or task.taskNumber like :search or task.name like :search', {
                search: `%${query.search}%`,
            });
        }
        if ((query === null || query === void 0 ? void 0 : query.offset) >= 0) {
            documentInOut.skip(query === null || query === void 0 ? void 0 : query.offset);
        }
        if (query === null || query === void 0 ? void 0 : query.limit) {
            documentInOut.take(query === null || query === void 0 ? void 0 : query.limit);
        }
        let result = await documentInOut.getManyAndCount();
        return result;
    }
    async findOne(id, userId) {
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const documentInOut = (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .select([
            'documentInOut',
            'client.id',
            'client.displayName',
            'client.email',
            'client.mobileNumber',
            'clientGroup.id',
            'clientGroup.displayName',
            'clientGroup.type',
            'clientGroup.email',
            'clientGroup.mobileNumber',
            'documentData',
            'attachments',
            'documentCategory',
            'kyb',
            'kybStorage',
            'receivedBy.id',
            'receivedBy.fullName',
            'givenBy.id',
            'givenBy.fullName',
            'keptAt',
            'task.id',
            'task.name',
            'task.taskNumber',
            'user.id',
            'user.fullName',
            'updatedBy.id',
            'updatedBy.fullName',
            'organization.id'
        ])
            .leftJoin('documentInOut.client', 'client')
            .leftJoin('documentInOut.clientGroup', 'clientGroup')
            .leftJoin('documentInOut.documentData', 'documentData')
            .leftJoin('documentData.attachments', 'attachments')
            .leftJoin('documentData.documentCategory', 'documentCategory')
            .leftJoin('documentData.kyb', 'kyb')
            .leftJoin('kyb.storage', 'kybStorage')
            .leftJoin('documentInOut.receivedBy', 'receivedBy')
            .leftJoin('documentInOut.givenBy', 'givenBy')
            .leftJoin('documentInOut.keptAt', 'keptAt')
            .leftJoin('documentInOut.task', 'task')
            .leftJoin('documentInOut.user', 'user')
            .leftJoin('documentInOut.updatedBy', 'updatedBy')
            .leftJoin('documentInOut.organization', 'organization')
            .where('documentInOut.id = :id', { id })
            .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });
        let result = await documentInOut.getOne();
        return result;
    }
    async update(id, body, userId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41, _42, _43, _44, _45, _46, _47, _48, _49, _50, _51, _52, _53, _54, _55, _56, _57, _58, _59, _60, _61, _62, _63;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const documentRepo = (0, typeorm_1.getRepository)(documents_data_entity_2.default);
        const data = body;
        let documentInOut = await (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .leftJoinAndSelect('documentInOut.client', 'client')
            .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
            .leftJoinAndSelect('documentInOut.documentData', 'documentData')
            .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
            .leftJoinAndSelect('documentData.kyb', 'kyb')
            .leftJoinAndSelect('kyb.storage', 'storage')
            .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
            .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
            .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
            .leftJoinAndSelect('documentInOut.task', 'task')
            .leftJoin('documentInOut.organization', 'organization')
            .where('documentInOut.id = :id', { id })
            .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
            .getOne();
        const oldDocumentDataIds = documentInOut.documentData.map(item => item.id);
        documentInOut.documentType = data === null || data === void 0 ? void 0 : data.documentType;
        documentInOut.useType = data === null || data === void 0 ? void 0 : data.useType;
        documentInOut.description = data === null || data === void 0 ? void 0 : data.description;
        documentInOut.client = data === null || data === void 0 ? void 0 : data.client;
        documentInOut.clientGroup = data === null || data === void 0 ? void 0 : data.clientGroup;
        documentInOut.organization = user.organization;
        if ((data === null || data === void 0 ? void 0 : data.documentType) === "in") {
            documentInOut.receivedBy = data === null || data === void 0 ? void 0 : data.receivedBy;
        }
        documentInOut.receivedTo = data === null || data === void 0 ? void 0 : data.receivedTo;
        documentInOut.givenBy = data === null || data === void 0 ? void 0 : data.givenBy;
        documentInOut.updatedBy = user;
        const mobileNumbers = ((_a = data === null || data === void 0 ? void 0 : data.clientWhatsapp) === null || _a === void 0 ? void 0 : _a.map((item, index) => ({
            id: index,
            number: item.number,
            countryCode: item.countryCode,
        }))) || [];
        const mail = ((_b = data === null || data === void 0 ? void 0 : data.clientMail) === null || _b === void 0 ? void 0 : _b.map((item, index) => ({
            id: index,
            email: item === null || item === void 0 ? void 0 : item.email,
        }))) || [];
        documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
        documentInOut.keptAtName = (_c = data === null || data === void 0 ? void 0 : data.keptAt) === null || _c === void 0 ? void 0 : _c.name;
        documentInOut.keptAt = data === null || data === void 0 ? void 0 : data.keptAt;
        documentInOut.task = data === null || data === void 0 ? void 0 : data.task;
        if ((data === null || data === void 0 ? void 0 : data.useType) === "kyb") {
            documentInOut.documentData = await Promise.all(((data === null || data === void 0 ? void 0 : data.documents) || []).map(async (item) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8;
                if ((0, lodash_1.isNumber)(item.id)) {
                    const existingDocument = await documents_data_entity_2.default.findOne({ where: { id: item.id } });
                    existingDocument.type = (_a = item.type) !== null && _a !== void 0 ? _a : null;
                    existingDocument.returnable = (_b = item.returnable) !== null && _b !== void 0 ? _b : null;
                    existingDocument.mode = (_c = item.mode) !== null && _c !== void 0 ? _c : null;
                    existingDocument.manner = (_d = item.manner) !== null && _d !== void 0 ? _d : null;
                    existingDocument.documentType = (_e = data === null || data === void 0 ? void 0 : data.type) !== null && _e !== void 0 ? _e : null;
                    existingDocument.documentInOut = documentInOut;
                    existingDocument.documentName = item.documentName;
                    let client = await client_entity_1.default.findOne({ where: { id: (_f = documentInOut.client) === null || _f === void 0 ? void 0 : _f.id } });
                    let clientGroup = await client_group_entity_1.default.findOne({ where: { id: (_g = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _g === void 0 ? void 0 : _g.id } });
                    if ((0, lodash_1.isNumber)((_h = item === null || item === void 0 ? void 0 : item.kyb) === null || _h === void 0 ? void 0 : _h.id)) {
                        const existingKyb = await kyb_entity_1.default.findOne({ where: { id: item.kyb.id }, relations: ['storage'] });
                        let storage;
                        existingKyb.documentName = ((_j = item === null || item === void 0 ? void 0 : item.kyb) === null || _j === void 0 ? void 0 : _j.documentName) === "custom" ? (_k = item === null || item === void 0 ? void 0 : item.kyb) === null || _k === void 0 ? void 0 : _k.customName : (_l = item === null || item === void 0 ? void 0 : item.kyb) === null || _l === void 0 ? void 0 : _l.documentName;
                        existingKyb.documentNumber = (_m = item === null || item === void 0 ? void 0 : item.kyb) === null || _m === void 0 ? void 0 : _m.documentNumber;
                        existingKyb.user = user;
                        existingKyb.documentsData = item;
                        if ((_o = item === null || item === void 0 ? void 0 : item.kyb) === null || _o === void 0 ? void 0 : _o.storage) {
                            if ((_p = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _p === void 0 ? void 0 : _p.id) {
                                if (item.kyb.storage.name !== ((_q = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _q === void 0 ? void 0 : _q.name)) {
                                    if ((existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage.storageSystem) === storage_entity_1.StorageSystem.AMAZON) {
                                        this.storageService.deleteAwsFile((_r = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _r === void 0 ? void 0 : _r.file);
                                    }
                                    else if ((existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage.storageSystem) === storage_entity_1.StorageSystem.MICROSOFT) {
                                        this.oneDriveService.deleteOneDriveFile(userId, (_s = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _s === void 0 ? void 0 : _s.fileId);
                                    }
                                    else if ((existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage.storageSystem) === storage_entity_1.StorageSystem.BHARATHCLOUD) {
                                        this.bharathService.deleteB3File(userId, (_t = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _t === void 0 ? void 0 : _t.file);
                                    }
                                }
                                existingKyb.storage = storage;
                            }
                            else {
                                storage = await this.storageService.addAttachements(userId, item.kyb.storage);
                                existingKyb.storage = storage;
                            }
                        }
                        else {
                            if ((_u = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _u === void 0 ? void 0 : _u.id) {
                                const existingStorage = await storage_entity_1.default.findOne({ where: { id: (_v = existingKyb === null || existingKyb === void 0 ? void 0 : existingKyb.storage) === null || _v === void 0 ? void 0 : _v.id } });
                                await existingStorage.remove();
                                if (existingStorage) {
                                    if (existingStorage.storageSystem === storage_entity_1.StorageSystem.AMAZON) {
                                        this.storageService.deleteAwsFile(existingKyb.storage.file);
                                    }
                                    else if (existingStorage.storageSystem === storage_entity_1.StorageSystem.MICROSOFT) {
                                        this.oneDriveService.deleteOneDriveFile(userId, existingKyb.storage.fileId);
                                    }
                                    else if (existingStorage.storageSystem === storage_entity_1.StorageSystem.BHARATHCLOUD) {
                                        this.bharathService.deleteB3File(user.organization.id, existingKyb.storage.file);
                                    }
                                }
                                existingKyb.storage = null;
                            }
                        }
                        existingKyb.client = client;
                        existingKyb.clientGroup = clientGroup;
                        existingDocument.kyb = existingKyb;
                    }
                    else {
                        let kyb = new kyb_entity_1.default();
                        kyb.documentName = item.documentName === "custom" ? item === null || item === void 0 ? void 0 : item.customName : item.documentName;
                        kyb.documentNumber = item.documentNumber;
                        kyb.client = client;
                        kyb.clientGroup = clientGroup;
                        kyb.user = user;
                        kyb.documentsData = item;
                        let storage = null;
                        if ((_w = item === null || item === void 0 ? void 0 : item.kyb) === null || _w === void 0 ? void 0 : _w.storage) {
                            storage = await this.storageService.addAttachements(userId, (_x = item === null || item === void 0 ? void 0 : item.kyb) === null || _x === void 0 ? void 0 : _x.storage);
                            kyb.storage = storage;
                        }
                        existingDocument.kyb = kyb;
                    }
                    if (!existingDocument.documentInOut) {
                        if ((0, lodash_1.isNumber)(item.kyb.id)) {
                            const existingKyb = await kyb_entity_1.default.findOne({ where: { id: item.kyb.id } });
                            await existingKyb.remove();
                            existingDocument.kyb = null;
                        }
                        await existingDocument.remove();
                        return null;
                    }
                    else {
                        return existingDocument !== null && existingDocument !== void 0 ? existingDocument : item;
                    }
                }
                else {
                    const documentData = new documents_data_entity_2.default();
                    documentData.type = (_y = item.type) !== null && _y !== void 0 ? _y : null;
                    documentData.returnable = (_z = item.returnable) !== null && _z !== void 0 ? _z : null;
                    documentData.mode = (_0 = item.mode) !== null && _0 !== void 0 ? _0 : null;
                    documentData.manner = (_1 = item.manner) !== null && _1 !== void 0 ? _1 : null;
                    documentData.documentType = (_2 = data === null || data === void 0 ? void 0 : data.useType) !== null && _2 !== void 0 ? _2 : null;
                    documentData.documentInOut = documentInOut;
                    documentData.documentName = item.documentName;
                    let client = await client_entity_1.default.findOne({ where: { id: (_3 = documentInOut.client) === null || _3 === void 0 ? void 0 : _3.id } });
                    let clientGroup = await client_group_entity_1.default.findOne({ where: { id: (_4 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _4 === void 0 ? void 0 : _4.id } });
                    let kyb = new kyb_entity_1.default();
                    kyb.documentName = ((_5 = item.kyb) === null || _5 === void 0 ? void 0 : _5.documentName) === "custom" ? (_6 = item === null || item === void 0 ? void 0 : item.kyb) === null || _6 === void 0 ? void 0 : _6.customName : (_7 = item === null || item === void 0 ? void 0 : item.kyb) === null || _7 === void 0 ? void 0 : _7.documentName;
                    kyb.documentNumber = (_8 = item === null || item === void 0 ? void 0 : item.kyb) === null || _8 === void 0 ? void 0 : _8.documentNumber;
                    kyb.client = client;
                    kyb.clientGroup = clientGroup;
                    kyb.user = user;
                    kyb.documentsData = documentData;
                    documentData.kyb = kyb;
                    return documentData;
                }
            }));
        }
        else {
            documentInOut.documentData = await Promise.all(((data === null || data === void 0 ? void 0 : data.documents) || []).map(async (item) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
                if ((0, lodash_1.isNumber)(item.id)) {
                    const existingDocument = await documents_data_entity_2.default.findOne({ where: { id: item.id } });
                    existingDocument.type = (_a = item.type) !== null && _a !== void 0 ? _a : null;
                    existingDocument.returnable = (_b = item.returnable) !== null && _b !== void 0 ? _b : null;
                    existingDocument.mode = (_c = item.mode) !== null && _c !== void 0 ? _c : null;
                    existingDocument.manner = (_d = item.manner) !== null && _d !== void 0 ? _d : null;
                    existingDocument.documentType = (_e = data === null || data === void 0 ? void 0 : data.useType) !== null && _e !== void 0 ? _e : null;
                    existingDocument.documentInOut = documentInOut;
                    existingDocument.documentName = item.documentName;
                    existingDocument.documentCategory = item === null || item === void 0 ? void 0 : item.documentCategory;
                    if (!existingDocument.documentInOut) {
                        await existingDocument.remove();
                        return null;
                    }
                    else {
                        return existingDocument !== null && existingDocument !== void 0 ? existingDocument : item;
                    }
                }
                else {
                    const documentData = new documents_data_entity_2.default();
                    documentData.type = (_f = item.type) !== null && _f !== void 0 ? _f : null;
                    documentData.returnable = (_g = item.returnable) !== null && _g !== void 0 ? _g : null;
                    documentData.mode = (_h = item.mode) !== null && _h !== void 0 ? _h : null;
                    documentData.manner = (_j = item.manner) !== null && _j !== void 0 ? _j : null;
                    documentData.documentType = (_k = data === null || data === void 0 ? void 0 : data.useType) !== null && _k !== void 0 ? _k : null;
                    documentData.documentInOut = documentInOut;
                    documentData.documentName = item.documentName;
                    documentData.documentCategory = item === null || item === void 0 ? void 0 : item.documentCategory;
                    return documentData;
                }
            }));
        }
        const newDocumentDataIds = documentInOut.documentData.map(item => item === null || item === void 0 ? void 0 : item.id);
        const removedDocumentDataIds = oldDocumentDataIds.filter(item => !newDocumentDataIds.includes(item));
        if (removedDocumentDataIds.length) {
            const getDataToRemove = await (0, typeorm_1.createQueryBuilder)(documents_data_entity_1.default, 'documentdata')
                .leftJoinAndSelect('documentdata.kyb', 'kyb')
                .where('documentdata.id in (:...ids)', { ids: removedDocumentDataIds })
                .getMany();
            for (let i of getDataToRemove) {
                if (documentInOut.useType === 'kyb') {
                    getDataToRemove.map(async (item) => await item.kyb.remove());
                }
                getDataToRemove.map(async (item) => await item.remove());
            }
        }
        documentInOut.updatedBy = user;
        documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm");
        await documentInOut.save();
        await documentRepo.save(documentInOut.documentData);
        if ((data === null || data === void 0 ? void 0 : data.useType) === "kyb") {
            await Promise.all(documentInOut.documentData.map(doc => { var _a; return (_a = doc.kyb) === null || _a === void 0 ? void 0 : _a.save(); }));
        }
        const usersList = [
            ...documentInOut.givenBy.map(item => item.id),
            ...documentInOut.receivedBy.map(item => item.id)
        ];
        const usersLists = [
            ...documentInOut.givenBy,
            ...documentInOut.receivedBy
        ];
        const users = [...new Set(usersList)];
        const uniqueUsersList = [...new Map(usersLists.map(item => [item.id, item])).values()];
        const orgId = (_d = user === null || user === void 0 ? void 0 : user.organization) === null || _d === void 0 ? void 0 : _d.id;
        const orgdetails = await organization_entity_1.Organization.findOne({ id: orgId });
        const addressParts = [
            orgdetails.buildingNo || '',
            orgdetails.floorNumber || '',
            orgdetails.buildingName || '',
            orgdetails.street || '',
            orgdetails.location || '',
            orgdetails.city || '',
            orgdetails.district || '',
            orgdetails.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
        const address = addressParts.join(', ') + pincode;
        if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "kyb") {
            const title = ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_e = documentInOut.receivedBy) === null || _e === void 0 ? void 0 : _e.map(item => item.fullName).join(",")}</strong> ${(data === null || data === void 0 ? void 0 : data.created) ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to KYB of <strong>${documentInOut.client ? (_f = documentInOut.client) === null || _f === void 0 ? void 0 : _f.displayName : (_g = documentInOut.clientGroup) === null || _g === void 0 ? void 0 : _g.displayName}</strong>, was subsequently handed over to <strong>${(_h = documentInOut.givenBy) === null || _h === void 0 ? void 0 : _h.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${(_j = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _j === void 0 ? void 0 : _j.map(item => { var _a; return `<li>${(_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.documentName}</li>`; }).join("")}
        </ol>
  </box></box>
  `;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['created'] = data === null || data === void 0 ? void 0 : data.created;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-kyb',
                    subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName}, 
Your document ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} operation has been ${(data === null || data === void 0 ? void 0 : data.created) ? 'recorded' : 'updated'} successfully.
${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_k = documentInOut.receivedBy) === null || _k === void 0 ? void 0 : _k.map(item => item.fullName).join(", ")} ${(data === null || data === void 0 ? void 0 : data.created) ? "recorded" : "updated"}
Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? (_l = documentInOut.client) === null || _l === void 0 ? void 0 : _l.displayName : (_m = documentInOut.clientGroup) === null || _m === void 0 ? void 0 : _m.displayName},
was subsequently handed over to ${(_o = documentInOut.givenBy) === null || _o === void 0 ? void 0 : _o.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `
and is currently kept at ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}` : ""}. The list of document(s) include: ${(_p = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _p === void 0 ? void 0 : _p.map((item, idx) => { var _a, _b; return `${idx + 1}. ${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}`; }).join(" | ")}.
             
Thanks, Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.mailCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_q = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _q === void 0 ? void 0 : _q.email;
                const key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_r = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _r === void 0 ? void 0 : _r.email : (_s = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _s === void 0 ? void 0 : _s.email;
                    const allMails = (_t = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _t === void 0 ? void 0 : _t['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_u = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _u === void 0 ? void 0 : _u.displayName : (_v = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _v === void 0 ? void 0 : _v.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                tradeName: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-kyb',
                            subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.whatsappCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const templateName = 'document_inout_client';
                const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm");
                const receiver = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out"
                    ? documentInOut.receivedTo
                    : (_w = documentInOut.receivedBy) === null || _w === void 0 ? void 0 : _w.map(item => item.fullName).join(", ");
                const inOut = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward";
                const docId = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId;
                const clientOrGroup = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client)
                    ? (_x = documentInOut.client) === null || _x === void 0 ? void 0 : _x.displayName
                    : (((_y = documentInOut.clientGroup) === null || _y === void 0 ? void 0 : _y.displayName) || "our Organization");
                const handoverTo = (_z = documentInOut.givenBy) === null || _z === void 0 ? void 0 : _z.map(item => item.fullName).join(", ");
                const keptAt = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) || "";
                const docList = (_0 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _0 === void 0 ? void 0 : _0.map((item, index) => {
                    var _a;
                    const name = ((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                    return `${index + 1}. ${name}`;
                }).join(" | ");
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_1 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _1 === void 0 ? void 0 : _1.email;
                const key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMobileNumber = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_2 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _2 === void 0 ? void 0 : _2.mobileNumber : (_3 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _3 === void 0 ? void 0 : _3.mobileNumber;
                    const allMobileNumbers = (_4 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _4 === void 0 ? void 0 : _4['whatsapp'].map((item) => item.number);
                    const mobileNumbers = [clientMobileNumber, ...allMobileNumbers];
                    try {
                        for (let mobileNumber of mobileNumbers) {
                            const whatsappOptions = {
                                to: mobileNumber,
                                name: templateName,
                                header: [
                                    {
                                        type: 'text',
                                        text: clientOrGroup,
                                    },
                                ],
                                body: [
                                    receiver,
                                    inOut,
                                    docId,
                                    dateTime,
                                    clientOrGroup,
                                    handoverTo,
                                    keptAt,
                                    docList
                                ],
                                title: 'document_inout_client',
                                userId: user.id,
                                orgId: user.organization.id,
                                key: 'DOCUMENT_IN_OUT_WHATSAPP',
                            };
                            await (0, whatsapp_service_1.sendClientWhatsAppTemplateMessage)(whatsappOptions);
                        }
                    }
                    catch (_64) {
                        console.log('cannot send whatsapp document-in-out message to client');
                    }
                }
            }
        }
        else if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "task") {
            const title = ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_5 = documentInOut.receivedBy) === null || _5 === void 0 ? void 0 : _5.map(item => item.fullName).join(",")}</strong> ${(data === null || data === void 0 ? void 0 : data.created) ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? (_6 = documentInOut.client) === null || _6 === void 0 ? void 0 : _6.displayName : (_7 = documentInOut.clientGroup) === null || _7 === void 0 ? void 0 : _7.displayName}</strong>, Task Id <strong>${(_8 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _8 === void 0 ? void 0 : _8['taskNumber']}</strong>, and Task Name <strong>${(_9 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _9 === void 0 ? void 0 : _9['name']}</strong> was subsequently handed over to <strong>${(_10 = documentInOut.givenBy) === null || _10 === void 0 ? void 0 : _10.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${(_11 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _11 === void 0 ? void 0 : _11.map(item => { var _a, _b; return `<li>${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}</li>`; }).join("")}
        </ol>
  </box></box>
  `;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['created'] = data === null || data === void 0 ? void 0 : data.created;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-task',
                    subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName}, 
        
Your document ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} operation has been ${(data === null || data === void 0 ? void 0 : data.created) ? 'recorded' : 'updated'} successfully.
${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_12 = documentInOut.receivedBy) === null || _12 === void 0 ? void 0 : _12.map(item => item.fullName).join(", ")} ${(data === null || data === void 0 ? void 0 : data.created) ? "recorded" : "updated"}
Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? (_13 = documentInOut.client) === null || _13 === void 0 ? void 0 : _13.displayName : (_14 = documentInOut.clientGroup) === null || _14 === void 0 ? void 0 : _14.displayName},
Task Id ${(_15 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _15 === void 0 ? void 0 : _15['taskNumber']}, and Task Name ${(_16 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _16 === void 0 ? void 0 : _16['name']} was subsequently handed over to ${(_17 = documentInOut.givenBy) === null || _17 === void 0 ? void 0 : _17.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `
and is currently kept at ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}` : ""}. The list of document(s) include: ${(_18 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _18 === void 0 ? void 0 : _18.map((item, idx) => { var _a, _b; return `${idx + 1}. ${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}`; }).join(" | ")}.
             
Thanks, Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.mailCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const mailOptions = {
                    id: user === null || user === void 0 ? void 0 : user.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_19 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _19 === void 0 ? void 0 : _19.email : (_20 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _20 === void 0 ? void 0 : _20.email,
                    clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                    data: {
                        adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_21 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _21 === void 0 ? void 0 : _21.displayName : (_22 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _22 === void 0 ? void 0 : _22.displayName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: user === null || user === void 0 ? void 0 : user.id,
                        legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                        adress: address,
                        phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                        mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                    },
                    filePath: 'docu-in-out-task',
                    subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                };
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_23 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _23 === void 0 ? void 0 : _23.email;
                let key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_24 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _24 === void 0 ? void 0 : _24.email : (_25 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _25 === void 0 ? void 0 : _25.email;
                    const allMails = (_26 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _26 === void 0 ? void 0 : _26['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_27 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _27 === void 0 ? void 0 : _27.displayName : (_28 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _28 === void 0 ? void 0 : _28.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-task',
                            subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.whatsappCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const templateName = 'document_inout_client';
                const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm");
                const receiver = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out"
                    ? documentInOut.receivedTo
                    : (_29 = documentInOut.receivedBy) === null || _29 === void 0 ? void 0 : _29.map(item => item.fullName).join(", ");
                const inOut = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward";
                const docId = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId;
                const clientOrGroup = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client)
                    ? (_30 = documentInOut.client) === null || _30 === void 0 ? void 0 : _30.displayName
                    : (((_31 = documentInOut.clientGroup) === null || _31 === void 0 ? void 0 : _31.displayName) || "our Organization");
                const handoverTo = (_32 = documentInOut.givenBy) === null || _32 === void 0 ? void 0 : _32.map(item => item.fullName).join(", ");
                const keptAt = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) || "";
                const docList = (_33 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _33 === void 0 ? void 0 : _33.map((item, index) => {
                    var _a;
                    const name = ((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                    return `${index + 1}. ${name}`;
                }).join(" | ");
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_34 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _34 === void 0 ? void 0 : _34.email;
                const key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMobileNumber = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_35 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _35 === void 0 ? void 0 : _35.mobileNumber : (_36 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _36 === void 0 ? void 0 : _36.mobileNumber;
                    const allMobileNumbers = (_37 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _37 === void 0 ? void 0 : _37['whatsapp'].map((item) => item.number);
                    const mobileNumbers = [clientMobileNumber, ...allMobileNumbers];
                    try {
                        for (let mobileNumber of mobileNumbers) {
                            const whatsappOptions = {
                                to: mobileNumber,
                                name: templateName,
                                header: [
                                    {
                                        type: 'text',
                                        text: clientOrGroup,
                                    },
                                ],
                                body: [
                                    receiver,
                                    inOut,
                                    docId,
                                    dateTime,
                                    clientOrGroup,
                                    handoverTo,
                                    keptAt,
                                    docList
                                ],
                                title: 'document_inout_client',
                                userId: user.id,
                                orgId: user.organization.id,
                                key: 'DOCUMENT_IN_OUT_WHATSAPP',
                            };
                            await (0, whatsapp_service_1.sendClientWhatsAppTemplateMessage)(whatsappOptions);
                        }
                    }
                    catch (_65) {
                    }
                }
            }
        }
        else if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "general") {
            const title = ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_38 = documentInOut.receivedBy) === null || _38 === void 0 ? void 0 : _38.map(item => item.fullName).join(",")}</strong> ${(data === null || data === void 0 ? void 0 : data.created) ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? (_39 = documentInOut.client) === null || _39 === void 0 ? void 0 : _39.displayName : ((_40 = documentInOut.clientGroup) === null || _40 === void 0 ? void 0 : _40.displayName) ? (_41 = documentInOut.clientGroup) === null || _41 === void 0 ? void 0 : _41.displayName : "our Organization"}</strong> was subsequently handed over to <strong>${(_42 = documentInOut.givenBy) === null || _42 === void 0 ? void 0 : _42.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${(_43 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _43 === void 0 ? void 0 : _43.map(item => { var _a, _b; return `<li>${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}</li>`; }).join("")}
      </ol>
</box></box>
`;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")} 
                            ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['created'] = data === null || data === void 0 ? void 0 : data.created;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-general',
                    subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName}, 
        
Your document ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} operation has been ${(data === null || data === void 0 ? void 0 : data.created) ? 'recorded' : 'updated'} successfully.
${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_44 = documentInOut.receivedBy) === null || _44 === void 0 ? void 0 : _44.map(item => item.fullName).join(", ")} ${(data === null || data === void 0 ? void 0 : data.created) ? "recorded" : "updated"}
Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? (_45 = documentInOut.client) === null || _45 === void 0 ? void 0 : _45.displayName : (_46 = documentInOut.clientGroup) === null || _46 === void 0 ? void 0 : _46.displayName},
was subsequently handed over to ${(_47 = documentInOut.givenBy) === null || _47 === void 0 ? void 0 : _47.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `
and is currently kept at ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}` : ""}. The list of document(s) include: ${(_48 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _48 === void 0 ? void 0 : _48.map((item, idx) => { var _a, _b; return `${idx + 1}. ${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}`; }).join(" | ")}.
             
Thanks, Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.mailCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_49 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _49 === void 0 ? void 0 : _49.email;
                let key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_50 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _50 === void 0 ? void 0 : _50.email : (_51 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _51 === void 0 ? void 0 : _51.email;
                    const allMails = (_52 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _52 === void 0 ? void 0 : _52['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_53 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _53 === void 0 ? void 0 : _53.displayName : (_54 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _54 === void 0 ? void 0 : _54.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-general',
                            subject: ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" && (data === null || data === void 0 ? void 0 : data.created)) ? "Document Outward" : ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" && (data === null || data === void 0 ? void 0 : data.created)) ? 'Document Inward Create' : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Update" : (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? "Document Outward Update" : "Document Outward Update",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
            if ((data === null || data === void 0 ? void 0 : data.whatsappCheck) && ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup))) {
                const templateName = 'document_inout_client';
                const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm");
                const receiver = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out"
                    ? documentInOut.receivedTo
                    : (_55 = documentInOut.receivedBy) === null || _55 === void 0 ? void 0 : _55.map(item => item.fullName).join(", ");
                const inOut = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward";
                const docId = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId;
                const clientOrGroup = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client)
                    ? (_56 = documentInOut.client) === null || _56 === void 0 ? void 0 : _56.displayName
                    : (((_57 = documentInOut.clientGroup) === null || _57 === void 0 ? void 0 : _57.displayName) || "our Organization");
                const handoverTo = (_58 = documentInOut.givenBy) === null || _58 === void 0 ? void 0 : _58.map(item => item.fullName).join(", ");
                const keptAt = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) || "";
                const docList = (_59 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _59 === void 0 ? void 0 : _59.map((item, index) => {
                    var _a;
                    const name = ((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                    return `${index + 1}. ${name}`;
                }).join(" | ");
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const clientPreferences = (_60 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _60 === void 0 ? void 0 : _60.email;
                const key = 'DOCUMENT_IN_OUT_MAIL';
                if (clientPreferences && clientPreferences[key]) {
                    const clientMobileNumber = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_61 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _61 === void 0 ? void 0 : _61.mobileNumber : (_62 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _62 === void 0 ? void 0 : _62.mobileNumber;
                    const allMobileNumbers = (_63 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _63 === void 0 ? void 0 : _63['whatsapp'].map((item) => item.number);
                    const mobileNumbers = [clientMobileNumber, ...allMobileNumbers];
                    try {
                        for (let mobileNumber of mobileNumbers) {
                            const whatsappOptions = {
                                to: mobileNumber,
                                name: templateName,
                                header: [
                                    {
                                        type: 'text',
                                        text: clientOrGroup,
                                    },
                                ],
                                body: [
                                    receiver,
                                    inOut,
                                    docId,
                                    dateTime,
                                    clientOrGroup,
                                    handoverTo,
                                    keptAt,
                                    docList
                                ],
                                title: 'document_inout_client',
                                userId: user.id,
                                orgId: user.organization.id,
                                key: 'DOCUMENT_IN_OUT_WHATSAPP',
                            };
                            await (0, whatsapp_service_1.sendClientWhatsAppTemplateMessage)(whatsappOptions);
                        }
                    }
                    catch (_66) {
                        console.log('cannot send whatsapp document-in-out message to client');
                    }
                }
            }
        }
    }
    async delete(id, userId, query) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let documentInOut = await (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .leftJoinAndSelect('documentInOut.client', 'client')
            .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
            .leftJoinAndSelect('documentInOut.documentData', 'documentData')
            .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
            .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
            .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
            .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
            .leftJoinAndSelect('documentInOut.task', 'task')
            .leftJoin('documentInOut.organization', 'organization')
            .where('documentInOut.id = :id', { id })
            .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
            .getOne();
        const usersList = [
            ...documentInOut.givenBy.map(item => item.id),
            ...documentInOut.receivedBy.map(item => item.id)
        ];
        const usersLists = [
            ...documentInOut.givenBy,
            ...documentInOut.receivedBy
        ];
        const users = [...new Set(usersList)];
        const uniqueUsersList = [...new Map(usersLists.map(item => [item.id, item])).values()];
        const orgId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        const loggedInUserDetails = await (0, re_use_1.getUserDetails)(userId);
        const { full_name: loggedInUser } = loggedInUserDetails;
        if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "kyb") {
            const title = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_b = documentInOut.receivedBy) === null || _b === void 0 ? void 0 : _b.map(item => item.fullName).join(",")} deleted </strong> Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${documentInOut.updatedDateTime}</strong> related to KYB of <strong>${documentInOut.client ? (_c = documentInOut.client) === null || _c === void 0 ? void 0 : _c.displayName : (_d = documentInOut.clientGroup) === null || _d === void 0 ? void 0 : _d.displayName}</strong>, was subsequently handed over to <strong>${(_e = documentInOut.givenBy) === null || _e === void 0 ? void 0 : _e.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${(_f = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _f === void 0 ? void 0 : _f.map(item => { var _a; return `<li>${(_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.documentName}</li>`; }).join("")}
        </ol>
  </box></box>
  `;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['deleted'] = true;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-kyb',
                    subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName},
The ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} on ${documentInOut.updatedDateTime} related to KYB of ${documentInOut.client ? (_g = documentInOut.client) === null || _g === void 0 ? void 0 : _g.displayName : (_h = documentInOut.clientGroup) === null || _h === void 0 ? void 0 : _h.displayName},
and it was subsequently handed over to ${(_j = documentInOut.givenBy) === null || _j === void 0 ? void 0 : _j.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? ` and is currently kept at ${documentInOut.keptAtName}` : ""}.
The list of document(s) include:

${(_k = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _k === void 0 ? void 0 : _k.map((item, idx) => {
                                var _a, _b;
                                const name = ((_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.documentName) || ((_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                                return `${idx + 1}. ${name}`;
                            }).join(" | ")}

Thanks,  
Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if (((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup)) && (query.emailCheck === "true")) {
                const key = 'DOCUMENT_IN_OUT_MAIL';
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const orgdetails = await organization_entity_1.Organization.findOne({ id: orgId });
                const addressParts = [
                    orgdetails.buildingNo || '',
                    orgdetails.floorNumber || '',
                    orgdetails.buildingName || '',
                    orgdetails.street || '',
                    orgdetails.location || '',
                    orgdetails.city || '',
                    orgdetails.district || '',
                    orgdetails.state || ''
                ].filter(part => part && part.trim() !== '');
                const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
                const address = addressParts.join(', ') + pincode;
                const clientPreferences = (_l = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _l === void 0 ? void 0 : _l.email;
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_m = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _m === void 0 ? void 0 : _m.email : (_o = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _o === void 0 ? void 0 : _o.email;
                    const allMails = (_p = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _p === void 0 ? void 0 : _p['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_q = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _q === void 0 ? void 0 : _q.displayName : (_r = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _r === void 0 ? void 0 : _r.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                tradeName: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-kyb',
                            subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
        }
        else if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "task") {
            const title = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_s = documentInOut.receivedBy) === null || _s === void 0 ? void 0 : _s.map(item => item.fullName).join(",")} deleted</strong> Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${documentInOut.updatedDateTime}</strong> related to <strong>${documentInOut.client ? (_t = documentInOut.client) === null || _t === void 0 ? void 0 : _t.displayName : (_u = documentInOut.clientGroup) === null || _u === void 0 ? void 0 : _u.displayName}</strong>, Task Id <strong>${(_v = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _v === void 0 ? void 0 : _v['taskNumber']}</strong>, and Task Name <strong>${(_w = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _w === void 0 ? void 0 : _w['name']}</strong> related to KYB of <strong>${documentInOut.client ? (_x = documentInOut.client) === null || _x === void 0 ? void 0 : _x.displayName : (_y = documentInOut.clientGroup) === null || _y === void 0 ? void 0 : _y.displayName}</strong>, was subsequently handed over to <strong>${(_z = documentInOut.givenBy) === null || _z === void 0 ? void 0 : _z.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${(_0 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _0 === void 0 ? void 0 : _0.map(item => { var _a, _b; return `<li>${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}</li>`; }).join("")}
      </ol>
</box></box>
`;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['deleted'] = true;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-task',
                    subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName},

The ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} on ${documentInOut.updatedDateTime} related to KYB of ${documentInOut.client ? (_1 = documentInOut.client) === null || _1 === void 0 ? void 0 : _1.displayName : (_2 = documentInOut.clientGroup) === null || _2 === void 0 ? void 0 : _2.displayName},
Task Id ${(_3 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _3 === void 0 ? void 0 : _3['taskNumber']}, and Task Name ${(_4 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.task) === null || _4 === void 0 ? void 0 : _4['name']} and it was subsequently handed over to ${(_5 = documentInOut.givenBy) === null || _5 === void 0 ? void 0 : _5.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `
and is currently kept at ${documentInOut.keptAtName}` : ""}.

The list of document(s) include:

${(_6 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _6 === void 0 ? void 0 : _6.map((item, idx) => {
                                var _a, _b;
                                const name = ((_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.documentName) || ((_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                                return `${idx + 1}. ${name}`;
                            }).join(" | ")}
Thanks,  
Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if (((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup)) && (query.emailCheck === "true")) {
                const key = 'DOCUMENT_IN_OUT_MAIL';
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const orgdetails = await organization_entity_1.Organization.findOne({ id: orgId });
                const addressParts = [
                    orgdetails.buildingNo || '',
                    orgdetails.floorNumber || '',
                    orgdetails.buildingName || '',
                    orgdetails.street || '',
                    orgdetails.location || '',
                    orgdetails.city || '',
                    orgdetails.district || '',
                    orgdetails.state || ''
                ].filter(part => part && part.trim() !== '');
                const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
                const address = addressParts.join(', ') + pincode;
                const clientPreferences = (_7 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _7 === void 0 ? void 0 : _7.email;
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_8 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _8 === void 0 ? void 0 : _8.email : (_9 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _9 === void 0 ? void 0 : _9.email;
                    const allMails = (_10 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _10 === void 0 ? void 0 : _10['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_11 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _11 === void 0 ? void 0 : _11.displayName : (_12 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _12 === void 0 ? void 0 : _12.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-task',
                            subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
        }
        else if ((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.useType) === "general") {
            const title = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete";
            const body = `<strong>${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "out" ? documentInOut.receivedTo : (_13 = documentInOut.receivedBy) === null || _13 === void 0 ? void 0 : _13.map(item => item.fullName).join(",")}</strong> deleted Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? (_14 = documentInOut.client) === null || _14 === void 0 ? void 0 : _14.displayName : ((_15 = documentInOut.clientGroup) === null || _15 === void 0 ? void 0 : _15.displayName) ? (_16 = documentInOut.clientGroup) === null || _16 === void 0 ? void 0 : _16.displayName : "our Organization"}</strong> was subsequently handed over to <strong>${(_17 = documentInOut.givenBy) === null || _17 === void 0 ? void 0 : _17.map(item => item.fullName).join(",")}</strong> ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? `and is currently kept at <strong>${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${(_18 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _18 === void 0 ? void 0 : _18.map(item => { var _a, _b; return `<li>${((_a = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _a === void 0 ? void 0 : _a.name) ? (_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name : item === null || item === void 0 ? void 0 : item.documentName}</li>`; }).join("")}
      </ol>
</box></box>
`;
            const key = 'DOCUMENT_IN_OUT_PUSH';
            (0, re_use_1.insertINTONotificationUpdate)(title, body, users, orgId, key);
            const organization = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
            ].filter(part => part && part.trim() !== '');
            const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const currentDate = new Date();
            const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
            documentInOut['deleted'] = true;
            for (let userData of uniqueUsersList) {
                await (0, newemails_1.sendnewMail)({
                    id: userData === null || userData === void 0 ? void 0 : userData.id,
                    key: 'DOCUMENT_IN_OUT_MAIL',
                    email: userData === null || userData === void 0 ? void 0 : userData.email,
                    data: {
                        adminName: userData === null || userData === void 0 ? void 0 : userData.fullName,
                        documentInOut: documentInOut,
                        formattedDate: formattedDate,
                        userId: userData === null || userData === void 0 ? void 0 : userData.id,
                        adress: address,
                        phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                        mail: organization === null || organization === void 0 ? void 0 : organization.email,
                        legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName)
                    },
                    filePath: 'docu-in-out-general',
                    subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                });
                try {
                    if ((userData === null || userData === void 0 ? void 0 : userData.id) !== undefined) {
                        const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                            where: { userId: userData.id, status: 'ACTIVE' },
                        });
                        if (sessionValidation) {
                            const adminUserDetails = await (0, re_use_1.getUserDetails)(userData.id);
                            const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id, } = adminUserDetails;
                            const key = 'DOCUMENT_IN_OUT_WHATSAPP';
                            const whatsappMessageBody = `Hi ${userFullName},
The ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentId} on ${documentInOut.updatedDateTime}
related to KYB of ${documentInOut.client ? (_19 = documentInOut.client) === null || _19 === void 0 ? void 0 : _19.displayName : (_20 = documentInOut.clientGroup) === null || _20 === void 0 ? void 0 : _20.displayName}, and it was subsequently handed over to ${(_21 = documentInOut.givenBy) === null || _21 === void 0 ? void 0 : _21.map(item => item.fullName).join(", ")}${(documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.keptAtName) ? ` and is currently kept at ${documentInOut.keptAtName}` : ""}.
The list of document(s) include:

${(_22 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _22 === void 0 ? void 0 : _22.map((item, idx) => {
                                var _a, _b;
                                const name = ((_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.documentName) || ((_b = item === null || item === void 0 ? void 0 : item.documentCategory) === null || _b === void 0 ? void 0 : _b.name) || (item === null || item === void 0 ? void 0 : item.documentName) || "Unnamed Document";
                                return `${idx + 1}. ${name}`;
                            }).join(" | ")}
Thanks,  
Team`;
                            await (0, whatsapp_service_1.sendWhatsAppTextMessage)(`91${userPhoneNumber}`, whatsappMessageBody, organization_id, title, id, key);
                        }
                    }
                }
                catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                }
            }
            if (((documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) || (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup)) && (query.emailCheck === "true")) {
                const key = 'DOCUMENT_IN_OUT_MAIL';
                const orgPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: orgId },
                });
                const orgdetails = await organization_entity_1.Organization.findOne({ id: orgId });
                const addressParts = [
                    orgdetails.buildingNo || '',
                    orgdetails.floorNumber || '',
                    orgdetails.buildingName || '',
                    orgdetails.street || '',
                    orgdetails.location || '',
                    orgdetails.city || '',
                    orgdetails.district || '',
                    orgdetails.state || ''
                ].filter(part => part && part.trim() !== '');
                const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
                const address = addressParts.join(', ') + pincode;
                const clientPreferences = (_23 = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _23 === void 0 ? void 0 : _23.email;
                if (clientPreferences && clientPreferences[key]) {
                    const clientMail = (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_24 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _24 === void 0 ? void 0 : _24.email : (_25 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _25 === void 0 ? void 0 : _25.email;
                    const allMails = (_26 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.mailData) === null || _26 === void 0 ? void 0 : _26['mails'].map((item) => item.email);
                    const mails = [clientMail, ...allMails];
                    for (let mail of mails) {
                        const mailOptions = {
                            id: user === null || user === void 0 ? void 0 : user.id,
                            key: 'DOCUMENT_IN_OUT_MAIL',
                            email: mail,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                adminName: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) ? (_27 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.client) === null || _27 === void 0 ? void 0 : _27.displayName : (_28 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.clientGroup) === null || _28 === void 0 ? void 0 : _28.displayName,
                                documentInOut: documentInOut,
                                formattedDate: formattedDate,
                                userId: user === null || user === void 0 ? void 0 : user.id,
                                legalName: (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.tradeName) || (orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.legalName),
                                adress: address,
                                phoneNumber: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.mobileNumber,
                                mail: orgdetails === null || orgdetails === void 0 ? void 0 : orgdetails.email,
                            },
                            filePath: 'docu-in-out-general',
                            subject: (documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentType) === "in" ? "Document Inward Delete" : "Document Outward Delete",
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
        }
        for (let i of documentInOut.documentData) {
            if (documentInOut.useType === 'kyb') {
                (_29 = documentInOut === null || documentInOut === void 0 ? void 0 : documentInOut.documentData) === null || _29 === void 0 ? void 0 : _29.map(async (item) => { var _a; return await ((_a = item === null || item === void 0 ? void 0 : item.kyb) === null || _a === void 0 ? void 0 : _a.remove()); });
            }
            documentInOut.documentData.map(async (item) => await item.remove());
        }
        await documentInOut.remove();
        return { success: true };
    }
    async saveAttachments(taskId, docId, files, userId) {
        return this.attachementService.saveAttachments(taskId, files, userId, docId);
    }
    ;
};
DocumentInOutService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [storage_service_1.StorageService,
        onedrive_storage_service_1.OneDriveStorageService,
        bharath_storage_service_1.BharathStorageService,
        attachments_service_1.AttachmentsService])
], DocumentInOutService);
exports.DocumentInOutService = DocumentInOutService;
//# sourceMappingURL=doucment-in-out.service.js.map