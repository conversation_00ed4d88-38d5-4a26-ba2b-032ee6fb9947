import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EmailQueue } from './email-queue.entity';
import { EmailThrottleService } from './email-throttle.service';
import { EmailThrottleController } from './email-throttle.controller';

@Module({
    imports: [
        // 🔹 Create a separate TypeORM connection for SQLite (file-based for persistence)
        TypeOrmModule.forRoot({
            name: 'sqliteConnection', // important: unique connection name
            type: 'sqlite',
            database: ':memory:',
            entities: [EmailQueue],
            synchronize: true,
            logging: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : false,
        }),

        // 🔹 Register entity with that connection
        TypeOrmModule.forFeature([EmailQueue], 'sqliteConnection'),

        // 🔹 Cron job for email processing
        ScheduleModule.forRoot(),
    ],
    controllers: [EmailThrottleController],
    providers: [EmailThrottleService],
    exports: [EmailThrottleService],
})
export class EmailThrottleModule { }
