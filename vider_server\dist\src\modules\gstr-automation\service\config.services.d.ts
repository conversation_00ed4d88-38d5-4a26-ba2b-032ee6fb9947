import { User } from 'src/modules/users/entities/user.entity';
import GstrCredentials from '../entity/gstrCredentials.entity';
import * as ExcelJS from 'exceljs';
import Task from 'src/modules/tasks/entity/task.entity';
import { StorageService } from 'src/modules/storage/storage.service';
export declare class GstrConfigService {
    private storageService;
    constructor(storageService: StorageService);
    gstAtomClient(userId: number, id: number): Promise<import("../../clients/entity/client.entity").default>;
    disableAtomProGstrClient(userId: number, body: any): Promise<void>;
    disableGstrSingleClient(id: number, userId: number): Promise<void>;
    getDeletedGstrClients(userId: number, query: any): Promise<{
        count: number;
        result: GstrCredentials[];
    }>;
    exportdeletedGstClient(userId: number, query: any): Promise<ExcelJS.Buffer>;
    enableGstrClient(id: number, userId: number): Promise<void>;
    enableBulkGstrClient(userId: number, body: any): Promise<void>;
    getBulkSyncStatus(userId: number): Promise<string>;
    updateEnableStatus(userId: number): Promise<void>;
    updateDisableStatus(userId: number): Promise<void>;
    organizationGstrScheduling(userId: any, body: any): Promise<string>;
    createNoticeAndOrderItem(userId: number, body: any): Promise<void>;
    updateNoticeAndOrder(userId: number, body: any): Promise<boolean>;
    deleteNoticeAndOrder(userId: number, id: number): Promise<boolean>;
    createAdditionalNotice(userId: number, body: any): Promise<void>;
    updateAdditionalNotice(userId: number, body: any): Promise<boolean>;
    deleteAdditionalNotice(userId: number, id: number): Promise<boolean>;
    normalizeFinancialYear(yearStr: string): string;
    completeTaskGstrOne(userId: number): Promise<{
        completedGstrOneTasks: any[];
        completedGstr3BTasks: any[];
        errorMsg: any[];
    }>;
    completeTasksWithAtomProSync(userId: number, body: any): Promise<{
        gstCompletedTasks: Task[];
        successRows: any;
    }>;
    processGstrTasks(rtntype: 'GSTR1' | 'GSTR3B' | 'GSTR9', subCategoryId: number, user: User, organizationId: number, body: any): Promise<[Task[], string[]]>;
}
