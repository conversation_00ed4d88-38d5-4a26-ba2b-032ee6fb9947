"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestTimeMiddleware = void 0;
const common_1 = require("@nestjs/common");
let RequestTimeMiddleware = class RequestTimeMiddleware {
    use(req, res, next) {
        const start = Date.now();
        res.on('finish', () => {
            const duration = Date.now() - start;
            if (duration > 10000) {
                console.log(`${req.method} ${req.originalUrl} Took- ${duration}ms`);
            }
        });
        next();
    }
};
RequestTimeMiddleware = __decorate([
    (0, common_1.Injectable)()
], RequestTimeMiddleware);
exports.RequestTimeMiddleware = RequestTimeMiddleware;
//# sourceMappingURL=timeLogger.js.map