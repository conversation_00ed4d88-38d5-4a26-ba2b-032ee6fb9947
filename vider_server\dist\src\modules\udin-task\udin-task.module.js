"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UdinTaskModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const udin_task_entity_1 = require("./udin-task.entity");
const udin_task_service_1 = require("./udin-task.service");
const udin_task_controller_1 = require("./udin-task.controller");
let UdinTaskModule = class UdinTaskModule {
};
UdinTaskModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([udin_task_entity_1.default])],
        controllers: [udin_task_controller_1.UdinTaskController],
        providers: [udin_task_service_1.UdinTaskService],
    })
], UdinTaskModule);
exports.UdinTaskModule = UdinTaskModule;
//# sourceMappingURL=udin-task.module.js.map