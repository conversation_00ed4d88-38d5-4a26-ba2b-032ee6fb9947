/// <reference types="node" />
/// <reference types="node" />
import { User } from '../users/entities/user.entity';
import ClientGroupBroadcast from './entity/client-group-broadcast.entity';
import BroadcastEmailTemplates from './entity/broadcast-email-templates-entity';
import BroadcastActivity from './entity/broadcast-activity.entity';
import { AwsService } from '../storage/upload.service';
import Label from '../labels/label.entity';
import Client from '../clients/entity/client.entity';
export declare enum BroadcastMessageTo {
    CLIENT = "CLIENT",
    CLIENTGROUP = "CLIENTGROUP"
}
export declare class CommunicationService {
    private awsService;
    constructor(awsService: AwsService);
    sendEmailTemplatetoclients(data: any): Promise<void>;
    cronEmailTemplatetoclients(): Promise<void>;
    createClientGroup(data: any, userId: any): Promise<ClientGroupBroadcast>;
    findClientGroup(userId: any, query: any): Promise<{
        clientCount: number;
        id: number;
        groupName: string;
        broadcastActivities: BroadcastActivity[];
        description: string;
        createdAt: string;
        updatedAt: string;
        organization: import("../organization/entities/organization.entity").Organization;
        label: Label;
        clients: Client[];
    }[]>;
    deleteClientGroup(ids: number, userId: any): Promise<import("typeorm").DeleteResult>;
    updateClientGroup(userId: any, id: any, body: any): Promise<ClientGroupBroadcast>;
    addClientstoClientGroup(userId: any, id: any, body: any): Promise<ClientGroupBroadcast>;
    removeClientsFromClientGroup(userId: any, id: any, body: any): Promise<ClientGroupBroadcast>;
    findOneClientGroup(userId: any, id: any, query: any): Promise<{
        clientGroup: ClientGroupBroadcast;
        clientCount: number;
        checkAccess: boolean;
    }>;
    createEmailTemplate(data: any, userId: any): Promise<BroadcastEmailTemplates>;
    getEmailTemplates(userId: string, query: any): Promise<BroadcastEmailTemplates[] | {
        labelColor: string;
        id: number;
        title: string;
        labels: string;
        createdAt: string;
        broadcastActivities: BroadcastActivity[];
        content: string;
        subject: string;
        default: number;
        updatedAt: string;
        organization: import("../organization/entities/organization.entity").Organization;
        user: User;
        label: Label;
    }[]>;
    getOneEmailTemplate(userId: any, id: any): Promise<BroadcastEmailTemplates>;
    updateEmailTemplate(userId: any, id: any, body: any): Promise<BroadcastEmailTemplates>;
    deleteEmailTemplate(ids: number, userId: any): Promise<import("typeorm").DeleteResult>;
    createBroadcastActivity(data: any, userId: any): Promise<BroadcastActivity>;
    getBroadcastActivity(userId: any, query: any): Promise<[BroadcastActivity[], number]>;
    getBroadcastActivityDetails(userId: any, id: any, query: any): Promise<{
        getOneEmailTemplate: BroadcastActivity;
        checkAccess: boolean;
    }>;
    upload(buffer: Buffer, key: any, contentType?: string): Promise<unknown>;
    filteredClients(userId: number, id: number, query: any): Promise<{
        count: number;
        result: Client[];
    }>;
}
