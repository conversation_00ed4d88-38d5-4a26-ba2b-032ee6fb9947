"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customsendmail = void 0;
const nodemailer = require("nodemailer");
let defaultsmtp = {
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
};
async function customsendmail({ to, org, data, }) {
    return new Promise(async (resolve, reject) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const mailOptions = {
            from: (org === null || org === void 0 ? void 0 : org.smtp) ? ((_a = org === null || org === void 0 ? void 0 : org.smtp) === null || _a === void 0 ? void 0 : _a.name) ? `"${(_b = org === null || org === void 0 ? void 0 : org.smtp) === null || _b === void 0 ? void 0 : _b.name}" <${(_d = (_c = org === null || org === void 0 ? void 0 : org.smtp) === null || _c === void 0 ? void 0 : _c.auth) === null || _d === void 0 ? void 0 : _d.user}>` : (_f = (_e = org === null || org === void 0 ? void 0 : org.smtp) === null || _e === void 0 ? void 0 : _e.auth) === null || _f === void 0 ? void 0 : _f.user : "<EMAIL>",
            to: to,
            subject: data.subject,
            html: data.html,
            attachments: data.attachments
        };
        if (org === null || org === void 0 ? void 0 : org.smtp) {
            (_g = org === null || org === void 0 ? void 0 : org.smtp) === null || _g === void 0 ? true : delete _g.name;
        }
        if (((_h = org === null || org === void 0 ? void 0 : org.smtp) === null || _h === void 0 ? void 0 : _h.service) === "outlook" || ((_j = org === null || org === void 0 ? void 0 : org.smtp) === null || _j === void 0 ? void 0 : _j.service) === "yahoo") {
            (_k = org === null || org === void 0 ? void 0 : org.smtp) === null || _k === void 0 ? true : delete _k.service;
        }
        const smtp = (org === null || org === void 0 ? void 0 : org.smtp) ? org === null || org === void 0 ? void 0 : org.smtp : defaultsmtp;
        const customtransporter = nodemailer.createTransport(smtp);
        await this.emailThrottleService.enqueueEmail(mailOptions.to, mailOptions.subject, mailOptions.html, org === null || org === void 0 ? void 0 : org.id, org === null || org === void 0 ? void 0 : org.smtp, mailOptions.attachments);
    });
}
exports.customsendmail = customsendmail;
//# sourceMappingURL=customSendMail.js.map