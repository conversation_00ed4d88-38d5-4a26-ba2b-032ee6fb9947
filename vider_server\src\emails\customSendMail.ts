import * as nodemailer from 'nodemailer';
import { Injectable } from '@nestjs/common';
import { EmailThrottleService } from '../modules/email-throttle/email-throttle.service';

let defaultsmtp: any = {
  host: 'email-smtp.ap-south-1.amazonaws.com',
  port: 587,
  auth: {
    user: 'AKIA5GHOVJDTRJ3PAQ6E',
    pass: 'BFt/gc++ytmTt24jK/**************************',
  },
};

@Injectable()
export class CustomMailService {
  constructor(private readonly emailThrottleService: EmailThrottleService) {}

  async customsendmailthrottled({ to, org, data, useThrottling = true }: any) {
    return new Promise(async (resolve, reject) => {
      try {
        const mailOptions = {
          from: org?.smtp ? org?.smtp?.name ? `"${org?.smtp?.name}" <${org?.smtp?.auth?.user}>` : org?.smtp?.auth?.user : "<EMAIL>",
          to: to,
          subject: data.subject,
          html: data.html,
          attachments: data.attachments
        };

        if (org?.smtp) {
          delete org?.smtp?.name;
        }

        if (org?.smtp?.service === "outlook" || org?.smtp?.service === "yahoo") {
          delete org?.smtp?.service;
        }

        if (useThrottling) {
          // Use email throttling service
          await this.emailThrottleService.enqueueEmail(
            mailOptions.to,
            mailOptions.subject,
            mailOptions.html,
            org?.id,
            org?.smtp,
            mailOptions.attachments
          );
          resolve('Email queued successfully');
        } else {
          // Send immediately without throttling
          const smtp = org?.smtp ? org?.smtp : defaultsmtp;
          const customtransporter = nodemailer.createTransport(smtp);

          customtransporter.sendMail(mailOptions, function (error: any, info: any) {
            if (error) {
              console.log(error.message);
              reject(error.message);
            } else {
              resolve(info.response);
            }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }
}

// Backward compatibility function (for existing code that uses the function directly)
export async function customsendmail({ to, org, data, }: any) {
  return new Promise(async (resolve, reject) => {
    try {
      const mailOptions = {
        from: org?.smtp ? org?.smtp?.name ? `"${org?.smtp?.name}" <${org?.smtp?.auth?.user}>` : org?.smtp?.auth?.user : "<EMAIL>",
        to: to,
        subject: data.subject,
        html: data.html,
        attachments: data.attachments
      };

      if (org?.smtp) {
        delete org?.smtp?.name;
      }

      if (org?.smtp?.service === "outlook" || org?.smtp?.service === "yahoo") {
        delete org?.smtp?.service;
      }

      // Send immediately without throttling (for backward compatibility)
      const smtp = org?.smtp ? org?.smtp : defaultsmtp;
      const customtransporter = nodemailer.createTransport(smtp);

      customtransporter.sendMail(mailOptions, function (error: any, info: any) {
        if (error) {
          console.log(error.message);
          reject(error.message);
        } else {
          resolve(info.response);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}