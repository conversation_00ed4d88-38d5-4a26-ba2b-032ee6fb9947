"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailThrottleModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const schedule_1 = require("@nestjs/schedule");
const email_queue_entity_1 = require("./email-queue.entity");
const email_throttle_service_1 = require("./email-throttle.service");
const email_throttle_controller_1 = require("./email-throttle.controller");
let EmailThrottleModule = class EmailThrottleModule {
};
EmailThrottleModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRoot({
                name: 'sqliteConnection',
                type: 'sqlite',
                database: ':memory:',
                entities: [email_queue_entity_1.EmailQueue],
                synchronize: true,
                logging: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : false,
            }),
            typeorm_1.TypeOrmModule.forFeature([email_queue_entity_1.EmailQueue], 'sqliteConnection'),
            schedule_1.ScheduleModule.forRoot(),
        ],
        controllers: [email_throttle_controller_1.EmailThrottleController],
        providers: [email_throttle_service_1.EmailThrottleService],
        exports: [email_throttle_service_1.EmailThrottleService],
    })
], EmailThrottleModule);
exports.EmailThrottleModule = EmailThrottleModule;
//# sourceMappingURL=email-throttle.module.js.map