"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PosterModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const poster_config_entity_1 = require("./poster-config.entity");
const poster_controller_1 = require("./poster.controller");
const poster_service_1 = require("./poster.service");
const upload_service_1 = require("../storage/upload.service");
const posters_entity_1 = require("./posters.entity");
const poster_events_entity_1 = require("./poster-events.entity");
const poster_event_types_entity_1 = require("./poster-event-types.entity");
const bharath_upload_service_1 = require("../storage/bharath-upload.service");
const bharath_storage_service_1 = require("../storage/bharath-storage.service");
const onedrive_storage_service_1 = require("../ondrive-storage/onedrive-storage.service");
const storage_service_1 = require("../storage/storage.service");
const attachments_service_1 = require("../tasks/services/attachments.service");
const googledrive_storage_service_1 = require("../ondrive-storage/googledrive-storage.service");
const newsletter_entity_1 = require("./newsletter.entity");
let PosterModule = class PosterModule {
};
PosterModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([poster_config_entity_1.default, poster_events_entity_1.default, poster_event_types_entity_1.default, posters_entity_1.default, newsletter_entity_1.Newsletter])],
        controllers: [poster_controller_1.PosterController],
        providers: [poster_service_1.PosterService,
            upload_service_1.AwsService,
            bharath_upload_service_1.BharathCloudService,
            bharath_storage_service_1.BharathStorageService,
            onedrive_storage_service_1.OneDriveStorageService,
            googledrive_storage_service_1.GoogleDriveStorageService,
            storage_service_1.StorageService,
            attachments_service_1.AttachmentsService
        ],
    })
], PosterModule);
exports.PosterModule = PosterModule;
//# sourceMappingURL=poster.module.js.map