{"version": 3, "file": "email-throttle.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/email-throttle/email-throttle.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,+CAAwD;AACxD,6DAAkD;AAClD,yCAAyC;AAGzC,MAAM,WAAW,GAAQ;IACrB,IAAI,EAAE,qCAAqC;IAC3C,IAAI,EAAE,GAAG;IACT,IAAI,EAAE;QACF,IAAI,EAAE,sBAAsB;QAC5B,IAAI,EAAE,8CAA8C;KACvD;CACJ,CAAC;AAGK,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAQ7B,YAEqB,SAAiC;QAAjC,cAAS,GAAT,SAAS,CAAwB;QATrC,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;QACxD,YAAO,GAAG,KAAK,CAAC;QAWpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;IAC9E,CAAC;IAED,KAAK,CAAC,YAAY,CACd,EAAU,EACV,OAAe,EACf,IAAS,EACT,cAAuB,EACvB,UAAgB,EAChB,WAAmB;QAEnB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAChC,EAAE;YACF,OAAO;YACP,IAAI;YACJ,cAAc;YACd,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;YAC1D,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI;SAChE,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC3C,CAAC;IAGK,AAAN,KAAK,CAAC,gBAAgB;QAClB,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI;YACA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBACpC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;gBAC3B,IAAI,EAAE,IAAI,CAAC,SAAS;aACvB,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC5B,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;gBACvB,IAAI;oBACA,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC5B,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;iBAC1E;gBAAC,OAAO,GAAG,EAAE;oBACV,KAAK,CAAC,UAAU,EAAE,CAAC;oBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE,kBAAkB,KAAK,CAAC,UAAU,YAAY,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;oBAE1G,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;wBACpC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;qBAC1D;yBAAM;wBACH,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,IAAI,CAAC,UAAU,iCAAiC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;qBACnH;iBACJ;aACJ;SACJ;gBAAS;YACN,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,KAAiB;QACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;YACnC,IAAI;gBAEA,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC1E,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAG7E,MAAM,WAAW,GAAG;oBAChB,IAAI,EAAE,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI;wBAClB,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,MAAM,MAAA,UAAU,CAAC,IAAI,0CAAE,IAAI,GAAG;wBACnD,CAAC,CAAC,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,0CAAE,IAAI,KAAI,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,eAAe;oBACzE,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,WAAW,EAAE,WAAW;iBAC3B,CAAC;gBAGF,IAAI,UAAU,EAAE;oBACL,UAAU,aAAV,UAAU,4BAAV,UAAU,CAAE,IAAI,CAAC;oBACxB,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,MAAK,SAAS,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,MAAK,OAAO,EAAE;wBAC/D,UAAU,aAAV,UAAU,4BAAV,UAAU,CAAE,OAAO,CAAC;qBAC9B;iBACJ;gBAGD,MAAM,IAAI,GAAG,UAAU,IAAI,WAAW,CAAC;gBACvC,MAAM,iBAAiB,GAAG,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE3D,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAU,EAAE,IAAS,EAAE,EAAE;oBAC9D,IAAI,KAAK,EAAE;wBACP,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM,CAAC,KAAK,CAAC,CAAC;qBACjB;yBAAM;wBACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;wBAC1D,OAAO,EAAE,CAAC;qBACb;gBACL,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC7E,MAAM,CAAC,KAAK,CAAC,CAAC;aACjB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;YAC9C,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE;SAC3B,CAAC,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;YAC7C,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;SAC7C,CAAC,CAAC;QAEH,OAAO;YACH,WAAW;YACX,aAAa;YACb,cAAc;YACd,aAAa;YACb,YAAY,EAAE,IAAI,CAAC,OAAO;SAC7B,CAAC;IACN,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC7B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;YAC1C,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,EAAE;SACX,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,cAAc,EAAE,cAAc;YAC9B,WAAW,EAAE,wCAAwC;SACxD,CAAC;IACN,CAAC;CACJ,CAAA;AAnHS;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,YAAY,CAAC;;;;4DAgCjC;AAtEQ,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAUJ,WAAA,IAAA,0BAAgB,EAAC,+BAAU,EAAE,kBAAkB,CAAC,CAAA;qCACrB,oBAAU;GAVjC,oBAAoB,CA0JhC;AA1JY,oDAAoB"}