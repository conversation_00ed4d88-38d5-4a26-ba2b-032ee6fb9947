export declare function sendEspoMail(data: any): void;
declare function organizationSignup(data: any): Promise<void>;
declare function leadCreated(lead: any): Promise<void>;
declare function taskCreated(taskCreationData: any): Promise<void>;
declare function subTaskCreated(subTaskCreationData: any): Promise<void>;
declare function taskDeleted(taskDeletedData: any): Promise<void>;
declare function taskUpdateStatusDone(taskStatusUpdatedData: any): Promise<void>;
declare function dscRegisterIssued(dscIssueData: any): Promise<void>;
declare function dscRegisterReceived(dscReceivedData: any): Promise<void>;
declare function dscRegisterExpire(dscExpireData: any): Promise<void>;
declare function clientCreated(clientCreatedData: any): Promise<void>;
declare function eventCreated(eventCreatedData: any): Promise<void>;
declare function eventCreatedWithTaskData(eventCreatedData: any): Promise<void>;
declare function userInvite(userInviteData: any): Promise<void>;
declare function dscRegisterAdd(dscRegisterAddData: any): Promise<void>;
declare const _default: {
    leadCreated: typeof leadCreated;
    organizationSignup: typeof organizationSignup;
    taskCreated: typeof taskCreated;
    subTaskCreated: typeof subTaskCreated;
    taskDeleted: typeof taskDeleted;
    taskUpdateStatusDone: typeof taskUpdateStatusDone;
    dscRegisterIssued: typeof dscRegisterIssued;
    dscRegisterReceived: typeof dscRegisterReceived;
    dscRegisterExpire: typeof dscRegisterExpire;
    clientCreated: typeof clientCreated;
    eventCreated: typeof eventCreated;
    eventCreatedWithTaskData: typeof eventCreatedWithTaskData;
    userInvite: typeof userInvite;
    dscRegisterAdd: typeof dscRegisterAdd;
};
export default _default;
