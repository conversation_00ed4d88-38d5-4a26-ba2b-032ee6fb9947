"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentCategoryModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const document_category_entity_1 = require("./entity/document-category.entity");
const document_category_controller_1 = require("./document-category.controller");
const doucment_category_service_1 = require("./doucment-category.service");
let DocumentCategoryModule = class DocumentCategoryModule {
};
DocumentCategoryModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([document_category_entity_1.default])],
        controllers: [document_category_controller_1.DocumentInOutController],
        providers: [doucment_category_service_1.DocumentCategoryService],
    })
], DocumentCategoryModule);
exports.DocumentCategoryModule = DocumentCategoryModule;
//# sourceMappingURL=document-category.module.js.map