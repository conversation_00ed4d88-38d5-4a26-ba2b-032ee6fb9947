import { DocumentCategoryService } from './doucment-category.service';
export declare class DocumentInOutController {
    private service;
    constructor(service: DocumentCategoryService);
    create(body: any, req: any): Promise<import("./entity/document-category.entity").default>;
    get(req: any, query: any): Promise<import("./entity/document-category.entity").default[]>;
    delete(body: any): Promise<{
        success: boolean;
    }>;
}
