import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity()
export class EmailQueue {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  to: string;

  @Column()
  subject: string;

  @Column('text')
  body: string;

  @Column({ default: 0 })
  retryCount: number;

  @Column({ nullable: true })
  organizationId: number;

  @Column('text', { nullable: true })
  smtpConfig: string; // JSON string of SMTP configuration

  @Column('text', { nullable: true })
  attachments: string; // JSON string of attachments

  @CreateDateColumn()
  createdAt: Date;
}
