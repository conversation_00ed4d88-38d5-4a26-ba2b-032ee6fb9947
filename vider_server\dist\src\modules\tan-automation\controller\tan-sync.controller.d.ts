import { TanSyncService } from "../service/tan-sync.service";
export declare class TanSyncController {
    private service;
    constructor(service: TanSyncService);
    createMachine(id: number, body: any, req: any): Promise<any>;
    bulkAutomationSync(body: any, req: any): Promise<void>;
    checkAutomationInOrganization(req: any): Promise<number>;
    getIncometexUpdates(req: any, query: any): Promise<import("../entity/tan-update-tracker.entity").default[]>;
    findForm(req: any, id: number): Promise<import("../entity/tan-update-tracker.entity").default>;
    disableIncomeTaxClient(body: any, req: any): Promise<void>;
    disableIncomeTaxSingleClient(req: any, id: number): Promise<void>;
    enableIncometaxClient(req: any, id: number): Promise<void>;
    enableBulkIncometaxClient(req: any, body: any): Promise<void>;
    getDeletedIncomeTaxClients(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/tan-client-credentials.entity").default[];
    }>;
    exportIncomeTaxTandeletedClients(req: any, body: any): Promise<import("exceljs").Buffer>;
    getTanBulkSyncStatus(req: any): Promise<string>;
    enableTanStatus(req: any): Promise<void>;
    updateTanDisableStatus(req: any): Promise<void>;
    organizationTanScheduling(req: any, body: any): Promise<string>;
    getTraceBulkSyncStatus(req: any): Promise<string>;
    enableTraceStatus(req: any): Promise<void>;
    updateTraceDisableStatus(req: any): Promise<void>;
    organizationTraceScheduling(req: any, body: any): Promise<string>;
}
