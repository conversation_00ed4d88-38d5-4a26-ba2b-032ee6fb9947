"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanSyncService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
const aut_client_credentials_entity_1 = require("../../automation/entities/aut_client_credentials.entity");
const automation_machines_entity_1 = require("../../automation/entities/automation_machines.entity");
const client_group_entity_1 = require("../../client-group/client-group.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const tan_update_tracker_entity_1 = require("../entity/tan-update-tracker.entity");
const tan_client_credentials_entity_1 = require("../entity/tan-client-credentials.entity");
const atomProReUse_1 = require("../../../utils/atomProReUse");
const permission_1 = require("../../tasks/permission");
const ExcelJS = require("exceljs");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
const utils_1 = require("../../../utils");
const moment = require("moment");
let TanSyncService = class TanSyncService {
    async createMachine(userId, id, data) {
        var _a, _b;
        let data1 = [
            {
                modules: (_a = data === null || data === void 0 ? void 0 : data.tanData) === null || _a === void 0 ? void 0 : _a.requests,
                tanCredentialsId: id,
                type: automation_machines_entity_1.TypeEnum.TAN,
            },
            {
                modules: (_b = data === null || data === void 0 ? void 0 : data.tracesData) === null || _b === void 0 ? void 0 : _b.requests,
                tanCredentialsId: id,
                type: automation_machines_entity_1.TypeEnum.TRACES,
            },
        ];
        data1 = data1.filter((item) => item.modules && item.modules.length > 0);
        return this.sendSingleIncometaxTanAutomationRequestToCamunda(userId, data1);
    }
    async sendSingleIncometaxTanAutomationRequestToCamunda(userId, data) {
        try {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(data),
            };
            const response = await (0, axios_1.default)(config);
            const responseData = response === null || response === void 0 ? void 0 : response.data;
            return responseData[JSON.stringify(data[0].tanCredentialsId)];
        }
        catch (error) {
            console.log('error in sendSingleIncometaxAutomationRequestToCamunda');
        }
    }
    async sendIncometaxAutomationRequestToCamunda(userId, data) {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
            headers: {
                'X-USER-ID': userId,
                'Content-Type': 'application/json',
            },
            data: data,
        };
        axios_1.default
            .request(config)
            .then((response) => {
        })
            .catch((error) => {
            console.log(error);
        });
    }
    async bulkAutomationSync(userId, data) {
        try {
            if (data === null || data === void 0 ? void 0 : data.selectedIds) {
                let abc = [];
                for (let tanClient of data === null || data === void 0 ? void 0 : data.selectedIds) {
                    const { tanRequest, tracesRequest } = data === null || data === void 0 ? void 0 : data.requests;
                    if ((tanRequest === null || tanRequest === void 0 ? void 0 : tanRequest.length) > 0) {
                        abc.push({
                            modules: tanRequest,
                            tanCredentialsId: tanClient === null || tanClient === void 0 ? void 0 : tanClient.id,
                            type: automation_machines_entity_1.TypeEnum.TAN,
                        });
                    }
                    if ((tracesRequest === null || tracesRequest === void 0 ? void 0 : tracesRequest.length) > 0) {
                        abc.push({
                            modules: tracesRequest,
                            tanCredentialsId: tanClient === null || tanClient === void 0 ? void 0 : tanClient.id,
                            type: automation_machines_entity_1.TypeEnum.TRACES,
                        });
                    }
                }
                this.sendIncometaxAutomationRequestToCamunda(userId, JSON.stringify(abc));
            }
        }
        catch (error) {
            console.log('error occur while bulkAutomationSync', error);
        }
    }
    async checkAutomationInOrganization(userId) {
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const sectionsData = await (0, typeorm_1.createQueryBuilder)(automation_machines_entity_1.default, 'automationMachines')
                .leftJoin('automationMachines.tanCredentials', 'tanCredentials')
                .where('tanCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere('automationMachines.status = :status', { status: 'PENDING' })
                .getCount();
            return sectionsData;
        }
        catch (error) {
            console.error('Error fetching checkAutomationInOrganization', error);
        }
    }
    async getIncometexUpdates(userId) {
        var _a;
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let tanUpdateTracker = (0, typeorm_1.createQueryBuilder)(tan_update_tracker_entity_1.default, 'tanUpdateTracker')
                .leftJoinAndSelect('tanUpdateTracker.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
                .where('tanUpdateTracker.organizationId = :organizationId', {
                organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
            })
                .andWhere('tanUpdateTracker.isChange = :isChange', { isChange: true })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('tanClientCredentials.status != :disStatus', {
                disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE,
            });
            if (ViewAssigned && !ViewAll) {
                tanUpdateTracker.andWhere('clientManagers.id = :userId', { userId });
            }
            const result = await tanUpdateTracker.getMany();
            return result;
        }
        catch (error) {
            console.log('error occur while getting  getIncometexUpdates', error);
        }
    }
    async getUpdatedItem(userId, id) {
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let updateTracker = (0, typeorm_1.getConnection)()
                .createQueryBuilder(tan_update_tracker_entity_1.default, 'tanUpdateTracker')
                .leftJoinAndSelect('tanUpdateTracker.client', 'client')
                .leftJoin('client.organization', 'organization')
                .where('tanUpdateTracker.id = :id', { id: id })
                .andWhere('organization.id = :organization', { organization: user.organization.id })
                .getOne();
            return updateTracker;
        }
        catch (error) {
            console.log('error occur while getting  getUpdatedItem', error);
        }
    }
    async disableIncomeTaxClient(userId, body) {
        const ids = body.ids;
        for (let incomeTaxId of ids) {
            const incomeTaxClient = await tan_client_credentials_entity_1.default.findOne({ where: { id: incomeTaxId } });
            if (incomeTaxClient) {
                incomeTaxClient.status = aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE;
                await incomeTaxClient.save();
            }
        }
    }
    async disableIncomeTaxSingleClient(id, userId) {
        const incomeTaxClient = await tan_client_credentials_entity_1.default.findOne({ where: { id: id } });
        if (incomeTaxClient) {
            incomeTaxClient.status = aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE;
            await incomeTaxClient.save();
        }
    }
    async enableIncometaxClient(id, userId) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        const incomeTaxClient = await tan_client_credentials_entity_1.default.findOne({ where: { id: id } });
        if (incomeTaxClient) {
            const checkIncomeTaxTan = await (0, atomProReUse_1.checkAtomProConfigIncomeTaxTan)(organizationId);
            incomeTaxClient.status = aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE;
            if (checkIncomeTaxTan === true) {
                await incomeTaxClient.save();
            }
            else {
                throw new common_1.BadRequestException(checkIncomeTaxTan);
            }
        }
    }
    async enableBulkIncometaxClient(userId, body) {
        var _a, _b, _c, _d;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (!organizationId) {
            throw new common_1.BadRequestException('Organization not found for this user');
        }
        const orgPreferences = await organization_preferences_entity_1.default.findOne({
            where: { organization: organizationId },
        });
        const isIncomeTaxTanEnabled = ((_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.automationConfig) === null || _b === void 0 ? void 0 : _b.tan) === 'YES';
        if (!isIncomeTaxTanEnabled) {
            throw new common_1.BadRequestException('Subscribe Atom Pro Income Tax TAN to enable clients for this organization');
        }
        const tanClientCredentialCount = await tan_client_credentials_entity_1.default.count({
            where: { organizationId, status: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE },
        });
        const organizationIncomeTaxLimit = ((_c = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.automationConfig) === null || _c === void 0 ? void 0 : _c.tanLimit) || 0;
        const clientsToEnable = ((_d = body === null || body === void 0 ? void 0 : body.incomeTaxTanClients) === null || _d === void 0 ? void 0 : _d.length) || 0;
        if (tanClientCredentialCount + clientsToEnable > organizationIncomeTaxLimit) {
            throw new common_1.BadRequestException(`Cannot enable clients. You can only enable up to ${organizationIncomeTaxLimit} Income Tax TAN clients in Atom Pro.`);
        }
        for (let i of body === null || body === void 0 ? void 0 : body.incomeTaxTanClients) {
            const incomeTaxClient = await tan_client_credentials_entity_1.default.findOne({
                where: { id: i === null || i === void 0 ? void 0 : i.id },
            });
            if (incomeTaxClient) {
                incomeTaxClient.status = aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE;
                await incomeTaxClient.save();
            }
        }
    }
    async getDeletedIncomeTaxClients(userId, query) {
        var _a;
        const { limit, offset } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            let deletedClients = (0, typeorm_1.createQueryBuilder)(tan_client_credentials_entity_1.default, 'clientCredentials')
                .leftJoinAndSelect('clientCredentials.client', 'client')
                .where('clientCredentials.organizationId = :organizationId', {
                organizationId: organizationId,
            })
                .andWhere('client.status != :status', { status: client_group_entity_1.UserStatus.DELETED })
                .andWhere('clientCredentials.status = :disStatus', { disStatus: aut_client_credentials_entity_1.IncomeTaxStatus.DISABLE });
            if (query.search) {
                deletedClients.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('clientCredentials.tanNumber LIKE :tanNumber', {
                        tanNumber: `%${query.search}%`,
                    });
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                    qb.orWhere('client.clientId LIKE :namesearch', {
                        namesearch: `%${query.search}%`,
                    });
                }));
            }
            const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    displayName: 'client.displayName',
                    tanNumber: 'clientCredentials.tanNumber',
                };
                const column = columnMap[sort.column] || sort.column;
                deletedClients.orderBy(column, sort.direction.toUpperCase());
            }
            else {
                deletedClients.orderBy('clientCredentials.createdAt', 'DESC');
            }
            ;
            if (offset >= 0) {
                deletedClients.skip(offset);
            }
            if (limit) {
                deletedClients.take(limit);
            }
            let result = await deletedClients.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
    }
    async exportIncomeTaxTandeletedClients(userId, body) {
        const newQuery = Object.assign(Object.assign({}, body), { offset: 0, limit: ********* });
        let clients = await this.getDeletedIncomeTaxClients(userId, newQuery);
        if (!clients.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Deleted Income Tax Clients');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client ID', key: 'clientId' },
            { header: 'Client #', key: 'clientNumber' },
            { header: 'TAN', key: 'tan' },
            { header: 'Category', key: 'category' },
            { header: 'Sub Category', key: 'subCategory' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Status Updated At', key: 'statusUpdatedAt' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        clients.result.forEach((client) => {
            var _a, _b, _c, _d, _e, _f, _g;
            const rowData = {
                serialNo: serialCounter++,
                clientId: (_a = client === null || client === void 0 ? void 0 : client.client) === null || _a === void 0 ? void 0 : _a.clientId,
                clientNumber: (_b = client === null || client === void 0 ? void 0 : client.client) === null || _b === void 0 ? void 0 : _b.clientNumber,
                category: ((_c = client === null || client === void 0 ? void 0 : client.client) === null || _c === void 0 ? void 0 : _c.category) ? (0, utils_1.getTitle)((_d = client === null || client === void 0 ? void 0 : client.client) === null || _d === void 0 ? void 0 : _d.category) : "",
                subCategory: ((_e = client === null || client === void 0 ? void 0 : client.client) === null || _e === void 0 ? void 0 : _e.subCategory) ? (0, utils_1.getTitle)((_f = client === null || client === void 0 ? void 0 : client.client) === null || _f === void 0 ? void 0 : _f.subCategory) : "",
                clientName: (_g = client === null || client === void 0 ? void 0 : client.client) === null || _g === void 0 ? void 0 : _g.displayName,
                tan: client === null || client === void 0 ? void 0 : client.tanNumber,
                password: client === null || client === void 0 ? void 0 : client.password,
                statusUpdatedAt: moment(client === null || client === void 0 ? void 0 : client.updatedAt).format("DD-MM-YYYY h:mm a")
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getTanBulkSyncStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let data = '';
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/tan`,
                headers: {},
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occure while getting into getBulkSyncStatus for TAN', error.message);
        }
    }
    async updateTanEnableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/enable/tan`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus for TAN', error);
        }
    }
    async updateTanDisableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/disable/tan`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus for TAN', error);
        }
    }
    async organizationTanScheduling(userId, body) {
        const { periodicity, day, hour, minute, weekDay } = body;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizarionId = user.organization.id;
            const amPm = hour >= 12 ? 'pm' : 'am';
            const adjustedHour = hour > 12 ? hour - 12 : hour;
            const originalBody = [
                {
                    periodicity: periodicity || 'DAILY',
                    days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
                    daysInstance: {},
                    hour: adjustedHour,
                    minute: minute,
                    seconds: 0,
                    amPm: amPm,
                    dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0,
                    month: 0,
                    intervalMinutes: 0,
                },
            ];
            let data = JSON.stringify({
                modules: ['P', 'F', 'EC', 'EP', 'OD'],
                orgId: organizarionId,
                type: 'TAN',
                schedules: originalBody,
            });
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occur while organizationScheduling for TAN', error);
        }
    }
    async getTraceBulkSyncStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let data = '';
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/traces`,
                headers: {},
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occure while getting into getBulkSyncStatus for TAN', error.message);
        }
    }
    async updateTraceEnableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/enable/traces`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus for TAN', error);
        }
    }
    async updateTraceDisableStatus(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let config = {
                method: 'put',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${(_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id}/disable/traces`,
                headers: {},
                data: '',
            };
            const response = await axios_1.default.request(config);
        }
        catch (error) {
            console.log('error while updateEnableStatus for TAN', error);
        }
    }
    async organizationTraceScheduling(userId, body) {
        const { periodicity, day, hour, minute, weekDay } = body;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizarionId = user.organization.id;
            const amPm = hour >= 12 ? 'pm' : 'am';
            const adjustedHour = hour > 12 ? hour - 12 : hour;
            const originalBody = [
                {
                    periodicity: periodicity || 'DAILY',
                    days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
                    daysInstance: {},
                    hour: adjustedHour,
                    minute: minute,
                    seconds: 0,
                    amPm: amPm,
                    dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0,
                    month: 0,
                    intervalMinutes: 0,
                },
            ];
            let data = JSON.stringify({
                modules: ['CI', 'F', 'OD'],
                orgId: organizarionId,
                type: 'TRACES',
                schedules: originalBody,
            });
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: data,
            };
            const response = await axios_1.default.request(config);
            return JSON.stringify(response === null || response === void 0 ? void 0 : response.data);
        }
        catch (error) {
            console.log('error occur while organizationScheduling for TAN', error);
        }
    }
};
TanSyncService = __decorate([
    (0, common_1.Injectable)()
], TanSyncService);
exports.TanSyncService = TanSyncService;
//# sourceMappingURL=tan-sync.service.js.map