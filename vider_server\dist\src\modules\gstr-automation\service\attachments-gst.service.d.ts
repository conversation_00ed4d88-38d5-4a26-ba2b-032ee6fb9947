/// <reference types="multer" />
import { StorageSystem } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import Storage from 'src/modules/storage/storage.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import { StorageService } from 'src/modules/storage/storage.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
export declare class AttachmentGstService {
    private uploadService;
    private bharahServce;
    private storageService;
    private oneDriveStorageService;
    private googleDriveStorageService;
    constructor(uploadService: AwsService, bharahServce: BharathStorageService, storageService: StorageService, oneDriveStorageService: OneDriveStorageService, googleDriveStorageService: GoogleDriveStorageService);
    saveAttachment(noticeId: number, files: Express.Multer.File[], userId: any, type: any): Promise<{
        errors: string[];
        success?: undefined;
    } | {
        success: boolean;
        errors?: undefined;
    }>;
    addAttachment(noticeId: number, files: Express.Multer.File[], userId: number, type: any): Promise<{
        errors: string[];
        success?: undefined;
    } | {
        success: boolean;
        errors?: undefined;
    }>;
    getFY(year: any): Promise<string>;
    convertFYString(fyString: any): Promise<string>;
    capitalizeFirstLetterOfEachWord(str: any): Promise<any>;
    existingGstrStorage(notice: any, user: User, type: string, storageType?: StorageSystem): Promise<Storage>;
    addOneDriveAttachments(noticeId: number, files: Express.Multer.File[], userId: number, type: any): Promise<{
        errors: string[];
        success?: undefined;
    } | {
        success: boolean;
        errors?: undefined;
    }>;
    addGoogleAttachments(noticeId: number, files: Express.Multer.File[], userId: number, type: any): Promise<{
        errors: string[];
        success?: undefined;
    } | {
        success: boolean;
        errors?: undefined;
    }>;
    deleteStorageFile(storageId: number, userId: number): Promise<void>;
}
