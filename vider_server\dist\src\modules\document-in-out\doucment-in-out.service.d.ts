/// <reference types="multer" />
import DocumentInOut from './entity/document-in-out.entity';
import { StorageService } from '../storage/storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
export declare class DocumentInOutService {
    private storageService;
    private oneDriveService;
    private bharathService;
    private attachementService;
    constructor(storageService: StorageService, oneDriveService: OneDriveStorageService, bharathService: BharathStorageService, attachementService: AttachmentsService);
    create(userId: number, data: any): Promise<number>;
    createDocumentItem(userId: number, data: any): Promise<number>;
    deleteDocumentItem(userId: number, data: any): Promise<void>;
    createAndSave(userId: number, data: any): Promise<void>;
    get(userId: number, query: any): Promise<[DocumentInOut[], number]>;
    findOne(id: number, userId: any): Promise<DocumentInOut>;
    update(id: any, body: any, userId: number): Promise<void>;
    delete(id: number, userId: any, query: any): Promise<{
        success: boolean;
    }>;
    saveAttachments(taskId: number, docId: number, files: Express.Multer.File[], userId: number): Promise<any>;
}
