"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelPartnerController = void 0;
const common_1 = require("@nestjs/common");
const channel_partner_service_1 = require("../services/channel-partner.service");
let ChannelPartnerController = class ChannelPartnerController {
    constructor(channelPartnerService) {
        this.channelPartnerService = channelPartnerService;
    }
    getAllPartners() {
        return this.channelPartnerService.getAllPartners();
    }
    getActivePartners() {
        return this.channelPartnerService.getActivePartners();
    }
    createPartner(dto) {
        return this.channelPartnerService.createPartner(dto);
    }
    updatePartner(id, dto) {
        return this.channelPartnerService.updatePartner(id, dto);
    }
    updatePartnerToogle(id, dto) {
        return this.channelPartnerService.updatePartnerToogle(id, dto);
    }
    getAllCoupons() {
        return this.channelPartnerService.getAllCoupons();
    }
    createCoupon(dto) {
        return this.channelPartnerService.createCoupon(dto);
    }
    updateCoupon(id, dto) {
        return this.channelPartnerService.updateCoupon(id, dto);
    }
    validateCoupon(dto) {
        return this.channelPartnerService.validateCoupon(dto);
    }
    getSignUps(query) {
        return this.channelPartnerService.getSignUps(query);
    }
    updateSignUpStatus(id, data) {
        return this.channelPartnerService.updateSignUpStatus(id, data);
    }
    async getPartnerAnalytics(id) {
        return this.channelPartnerService.getPartnerAnalytics(id);
    }
};
__decorate([
    (0, common_1.Get)('channel-partners'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "getAllPartners", null);
__decorate([
    (0, common_1.Get)('active-channel-partners'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "getActivePartners", null);
__decorate([
    (0, common_1.Post)('channel-partners'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "createPartner", null);
__decorate([
    (0, common_1.Patch)('channel-partners/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "updatePartner", null);
__decorate([
    (0, common_1.Patch)('toggle/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "updatePartnerToogle", null);
__decorate([
    (0, common_1.Get)('coupons'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "getAllCoupons", null);
__decorate([
    (0, common_1.Post)('coupons'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "createCoupon", null);
__decorate([
    (0, common_1.Patch)('coupons/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "updateCoupon", null);
__decorate([
    (0, common_1.Post)('coupons/validate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "validateCoupon", null);
__decorate([
    (0, common_1.Get)('sign-ups'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "getSignUps", null);
__decorate([
    (0, common_1.Patch)('lead-status/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], ChannelPartnerController.prototype, "updateSignUpStatus", null);
__decorate([
    (0, common_1.Get)('analytics/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ChannelPartnerController.prototype, "getPartnerAnalytics", null);
ChannelPartnerController = __decorate([
    (0, common_1.Controller)('channel-partner'),
    __metadata("design:paramtypes", [channel_partner_service_1.ChannelPartnerService])
], ChannelPartnerController);
exports.ChannelPartnerController = ChannelPartnerController;
//# sourceMappingURL=channel-partner.controller.js.map