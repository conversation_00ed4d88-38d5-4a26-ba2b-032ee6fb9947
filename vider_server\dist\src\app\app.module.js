"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const mongoose_1 = require("@nestjs/mongoose");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const dsc_register_module_1 = require("../modules/dsc-register/dsc-register.module");
const expenditure_module_1 = require("../modules/expenditure/expenditure.module");
const leads_module_1 = require("../modules/leads/leads.module");
const log_hours_module_1 = require("../modules/log-hours/log-hours.module");
const onedrive_storage_module_1 = require("../modules/ondrive-storage/onedrive-storage.module");
const reports_module_1 = require("../modules/reports/reports.module");
const stats_module_1 = require("../modules/stats/stats.module");
const db_config_1 = require("../config/db_config");
const mongo_config_1 = require("../config/mongo_config");
const activity_module_1 = require("../modules/activity/activity.module");
const admin_module_1 = require("../modules/admin/admin.module");
const billing_module_1 = require("../modules/billing/billing.module");
const categories_module_1 = require("../modules/categories/categories.module");
const chats_module_1 = require("../modules/chats/chats.module");
const client_panel_module_1 = require("../modules/client-panel/client-panel.module");
const clients_module_1 = require("../modules/clients/clients.module");
const common_module_1 = require("../modules/common/common.module");
const events_module_1 = require("../modules/events/events.module");
const forms_module_1 = require("../modules/forms/forms.module");
const get_started_module_1 = require("../modules/get-started/get-started.module");
const label_module_1 = require("../modules/labels/label.module");
const organization_module_1 = require("../modules/organization/organization.module");
const recurring_module_1 = require("../modules/recurring/recurring.module");
const permissions_module_1 = require("../modules/roles/permissions.module");
const services_module_1 = require("../modules/services/services.module");
const states_module_1 = require("../modules/states/states.module");
const storage_module_1 = require("../modules/storage/storage.module");
const tasks_module_1 = require("../modules/tasks/tasks.module");
const users_module_1 = require("../modules/users/users.module");
const notifications_module_1 = require("../notifications/notifications.module");
const notifications_prreferences_module_1 = require("../modules/notification-settings/notifications-prreferences.module");
const strapi_module_1 = require("../modules/strapi/strapi.module");
const sandbox_module_1 = require("../modules/sandbox/sandbox.module");
const gstr_register_module_1 = require("../modules/gstr-register/gstr-register.module");
const webhook_module_1 = require("../modules/webhook/webhook.module");
const atom_super_admin_activity_module_1 = require("../modules/atom-super-admin-activity/atom_super_admin_activity.module");
const attendance_module_1 = require("../modules/attendance/attendance.module");
const organization_preferences_module_1 = require("../modules/organization-preferences/organization-preferences.module");
const wallet_module_1 = require("../modules/wallet/wallet.module");
const whatsapp_module_1 = require("../modules/whatsapp/whatsapp.module");
const qtm_super_admin_activity_module_1 = require("../modules/qtm-super-admin-activity/qtm-super-admin-activity.module");
const atm_qtm_approval_module_1 = require("../modules/atm-qtm-approval/atm-qtm-approval.module");
const collent_data_module_1 = require("../modules/collect-data/collent-data.module");
const kyb_module_1 = require("../modules/kyb/kyb.module");
const quantum_module_1 = require("../modules/quantum/quantum.module");
const budgeted_hours_module_1 = require("../modules/budgeted-hours/budgeted-hours.module");
const automation_module_1 = require("../modules/automation/automation.module");
const cron_activity_module_1 = require("../modules/cron-activity/cron-activity.module");
const client_group_module_1 = require("../modules/client-group/client-group.module");
const udin_task_module_1 = require("../modules/udin-task/udin-task.module");
const gstr_automation_module_1 = require("../modules/gstr-automation/gstr-automation.module");
const communication_module_1 = require("../modules/communication/communication.module");
const wellknown_module_1 = require("../modules/wellknown/wellknown.module");
const timeLogger_1 = require("./timeLogger");
const tan_automation_module_1 = require("../modules/tan-automation/tan-automation.module");
const poster_module_1 = require("../modules/poster/poster.module");
const document_category_module_1 = require("../modules/document-in-out/document-category.module");
const document_in_out_module_1 = require("../modules/document-in-out/document-in-out.module");
const viderAi_module_1 = require("../modules/vider-ai/viderAi.module");
const channel_partner_module_1 = require("../modules/channel-partners/channel-partner.module");
const email_throttle_module_1 = require("../modules/email-throttle/email-throttle.module");
config_1.ConfigModule.forRoot({
    load: [db_config_1.default, mongo_config_1.default],
    expandVariables: true,
});
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(timeLogger_1.RequestTimeMiddleware).forRoutes('*');
    }
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRoot((0, db_config_1.default)()),
            mongoose_1.MongooseModule.forRoot((0, mongo_config_1.default)().MONGO_URI),
            event_emitter_1.EventEmitterModule.forRoot(),
            schedule_1.ScheduleModule.forRoot(),
            organization_module_1.OrganizationsModule,
            get_started_module_1.GetStartedModule,
            users_module_1.UsersModule,
            permissions_module_1.PermissionsModule,
            categories_module_1.CategoriesModule,
            services_module_1.ServicesModule,
            common_module_1.CommonModule,
            clients_module_1.ClientModule,
            leads_module_1.LeadsModule,
            storage_module_1.StorageModule,
            label_module_1.LabelModule,
            tasks_module_1.TasksModule,
            recurring_module_1.RecurringModule,
            events_module_1.EventsModule,
            states_module_1.StatesModule,
            activity_module_1.ActivityModule,
            notifications_module_1.NotificationsModule,
            forms_module_1.FormsModule,
            chats_module_1.ChatsModule,
            admin_module_1.AdminModule,
            client_panel_module_1.ClientPanelModule,
            billing_module_1.BillingModule,
            reports_module_1.ReportsModule,
            log_hours_module_1.LogHoursModule,
            expenditure_module_1.ExpenditureModule,
            onedrive_storage_module_1.OneDriveStorageModule,
            dsc_register_module_1.DscRegisterModule,
            stats_module_1.StatsModule,
            notifications_prreferences_module_1.PushNotificationModule,
            gstr_register_module_1.GstrRegisterModule,
            strapi_module_1.StrapiModule,
            sandbox_module_1.SandboxModule,
            webhook_module_1.WebhookModule,
            whatsapp_module_1.WhatsappModule,
            atom_super_admin_activity_module_1.AtomSuperAdminActivityModule,
            attendance_module_1.AttendanceModule,
            organization_preferences_module_1.OrganizationPreferencesModule,
            wallet_module_1.WalletModule,
            qtm_super_admin_activity_module_1.QtmSuperAdminActivityModule,
            atm_qtm_approval_module_1.AtmQtmApprovalModule,
            collent_data_module_1.CollectDataModule,
            kyb_module_1.KybModule,
            quantum_module_1.QuantumModule,
            budgeted_hours_module_1.BudgetedHoursModule,
            automation_module_1.AutomationModule,
            cron_activity_module_1.CronActivityModule,
            client_group_module_1.ClientGroupModule,
            udin_task_module_1.UdinTaskModule,
            gstr_automation_module_1.GstrAutomationModule,
            communication_module_1.CommunicationModule,
            wellknown_module_1.WellknownModule,
            tan_automation_module_1.TanAutomationModule,
            poster_module_1.PosterModule,
            document_category_module_1.DocumentCategoryModule,
            document_in_out_module_1.DocumentInOutModule,
            viderAi_module_1.ViderAiModule,
            storage_module_1.StorageModule,
            channel_partner_module_1.ChannelPartnerModule,
            email_throttle_module_1.EmailThrottleModule,
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map