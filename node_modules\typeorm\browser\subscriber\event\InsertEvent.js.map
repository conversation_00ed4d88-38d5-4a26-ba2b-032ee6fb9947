{"version": 3, "sources": ["../browser/src/subscriber/event/InsertEvent.ts"], "names": [], "mappings": "", "file": "InsertEvent.js", "sourcesContent": ["import { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\n\n/**\n * InsertEvent is an object that broadcaster sends to the entity subscriber when entity is inserted to the database.\n */\nexport interface InsertEvent<Entity> {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n\n    /**\n     * Inserting event.\n     */\n    entity: Entity\n\n    /**\n     * Id or ids of the entity being inserted.\n     */\n    entityId?: ObjectLiteral\n\n    /**\n     * Metadata of the entity.\n     */\n    metadata: EntityMetadata\n}\n"], "sourceRoot": "../.."}