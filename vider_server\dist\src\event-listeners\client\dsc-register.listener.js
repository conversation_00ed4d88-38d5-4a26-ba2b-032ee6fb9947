"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DscRegisterListener = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const client_entity_1 = require("../../modules/clients/entity/client.entity");
const notify_1 = require("../../notifications/notify");
const user_entity_1 = require("../../modules/users/entities/user.entity");
const actions_1 = require("../actions");
const dsc_activity_entity_1 = require("../../modules/dsc-register/entity/dsc-activity.entity");
const client_group_entity_1 = require("../../modules/client-group/client-group.entity");
let DscRegisterListener = class DscRegisterListener {
    async handleDscRegisterIssue(event) {
        try {
            const { userId, clientId, issuedTo, clientGroupId } = event;
            let user = await user_entity_1.User.findOne({ where: { id: userId } });
            let client = await client_entity_1.default.findOne({
                where: { id: clientId },
                relations: ['clientManager'],
            });
            let clientGroup = await client_group_entity_1.default.findOne({ where: { id: clientGroupId } });
            if (!client.clientManager)
                return;
            await notify_1.default.DscRegisterIssue({
                userName: user.fullName,
                clientName: client.displayName,
                clientGroupName: clientGroup.displayName,
                issuedTo,
                userIds: [client.clientManager.id],
            });
        }
        catch (err) {
            console.log(err);
        }
    }
    async handleDscRegisterReceive(event) {
        var _a;
        try {
            const { clientId, receivedBy, dscRegisterId } = event;
            let dscActivity = await dsc_activity_entity_1.default.find({
                where: {
                    dscRegister: { id: dscRegisterId },
                    type: dsc_activity_entity_1.DscActivityTypeEnum.ISSUE,
                },
                order: { date: 'DESC' },
                take: 1,
            });
            if (!dscActivity.length)
                return;
            let client = await client_entity_1.default.findOne({
                where: { id: clientId },
                relations: ['clientManager'],
            });
            if (!client.clientManager)
                return;
            await notify_1.default.DscRegisterReceive({
                clientName: client.displayName,
                receivedBy,
                userIds: [client.clientManager.id],
                receivedFrom: (_a = dscActivity[0]) === null || _a === void 0 ? void 0 : _a.personName,
            });
        }
        catch (err) {
            console.log(err);
        }
    }
};
__decorate([
    (0, event_emitter_1.OnEvent)(actions_1.Event_Actions.DSC_REGISTER_ISSUED, { async: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DscRegisterListener.prototype, "handleDscRegisterIssue", null);
__decorate([
    (0, event_emitter_1.OnEvent)(actions_1.Event_Actions.DSC_REGISTER_RECEIVED, { async: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DscRegisterListener.prototype, "handleDscRegisterReceive", null);
DscRegisterListener = __decorate([
    (0, common_1.Injectable)()
], DscRegisterListener);
exports.DscRegisterListener = DscRegisterListener;
//# sourceMappingURL=dsc-register.listener.js.map