"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailThrottleController = void 0;
const common_1 = require("@nestjs/common");
const email_throttle_service_1 = require("./email-throttle.service");
let EmailThrottleController = class EmailThrottleController {
    constructor(emailThrottleService) {
        this.emailThrottleService = emailThrottleService;
    }
    async queueEmails(body) {
        await this.emailThrottleService.enqueueEmail(body.to, body.subject, body.body);
        return { message: `Queued email to ${body.to}` };
    }
    async queueBulkEmails(body) {
        for (const e of body.emails) {
            await this.emailThrottleService.enqueueEmail(e.to, e.subject, e.body);
        }
        return { message: `${body.emails.length} emails queued.` };
    }
};
__decorate([
    (0, common_1.Post)('queue'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "queueEmails", null);
__decorate([
    (0, common_1.Post)('bulk-queue'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "queueBulkEmails", null);
EmailThrottleController = __decorate([
    (0, common_1.Controller)('email-throttle'),
    __metadata("design:paramtypes", [email_throttle_service_1.EmailThrottleService])
], EmailThrottleController);
exports.EmailThrottleController = EmailThrottleController;
//# sourceMappingURL=email-throttle.controller.js.map