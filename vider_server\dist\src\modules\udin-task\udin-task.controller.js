"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UdinTaskController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../users/jwt/jwt-auth.guard");
const udin_task_service_1 = require("./udin-task.service");
let UdinTaskController = class UdinTaskController {
    constructor(service) {
        this.service = service;
    }
    getUdinTasks(req, query) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        return this.service.getUdinTasks(query, userId);
    }
    async exportUdinTasksPageReport(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportUdinTasksPageReport(userId, query);
    }
    getUdinTask(req, query, taskId) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        return this.service.getUdinTask(query, userId);
    }
    updateUdinTaskDetails(body, req) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        return this.service.update(body, userId);
    }
    createUdinTask(body, req) {
        const { userId } = req.user;
        return this.service.createUdinTask(userId, body);
    }
    updateUdinTask(body, req, id) {
        const { userId } = req.user;
        return this.service.updateUdinTask(userId, id, body);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UdinTaskController.prototype, "getUdinTasks", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/udin-task/udin-tasksexport'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UdinTaskController.prototype, "exportUdinTasksPageReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("/:id"),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Number]),
    __metadata("design:returntype", void 0)
], UdinTaskController.prototype, "getUdinTask", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UdinTaskController.prototype, "updateUdinTaskDetails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UdinTaskController.prototype, "createUdinTask", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('/:id'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Number]),
    __metadata("design:returntype", void 0)
], UdinTaskController.prototype, "updateUdinTask", null);
UdinTaskController = __decorate([
    (0, common_1.Controller)('udin-task'),
    __metadata("design:paramtypes", [udin_task_service_1.UdinTaskService])
], UdinTaskController);
exports.UdinTaskController = UdinTaskController;
//# sourceMappingURL=udin-task.controller.js.map