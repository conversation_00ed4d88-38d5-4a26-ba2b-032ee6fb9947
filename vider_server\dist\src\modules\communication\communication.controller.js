"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CommunicationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunicationController = void 0;
const communication_service_1 = require("./communication.service");
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const event_emitter_1 = require("@nestjs/event-emitter");
const jwt_auth_guard_1 = require("../users/jwt/jwt-auth.guard");
let CommunicationController = CommunicationController_1 = class CommunicationController {
    constructor(eventEmitter, service) {
        this.eventEmitter = eventEmitter;
        this.service = service;
    }
    static withEventEmitter(eventEmitter) {
        return new CommunicationController_1(eventEmitter, null);
    }
    static withService(service) {
        return new CommunicationController_1(null, service);
    }
    async create(req, body) {
        const { userId } = req.user;
        return await this.service.createClientGroup(body, userId);
    }
    async getClientGroup(req, query) {
        const { userId } = req.user;
        return this.service.findClientGroup(userId, query);
    }
    async deleteClientGroup(id, req) {
        const { userId } = req.user;
        return this.service.deleteClientGroup(id, userId);
    }
    async update(id, body, req) {
        const { userId } = req.user;
        return this.service.updateClientGroup(userId, id, body);
    }
    async addClientstoClientGroup(id, body, req) {
        const { userId } = req.user;
        return this.service.addClientstoClientGroup(userId, id, body);
    }
    async removeClientsFromClientGroup(id, body, req) {
        const { userId } = req.user;
        return this.service.removeClientsFromClientGroup(userId, id, body);
    }
    async getOneClientGroup(id, req, query) {
        const { userId } = req.user;
        return this.service.findOneClientGroup(userId, id, query);
    }
    async createEmailTemplate(req, body) {
        const { userId } = req.user;
        return await this.service.createEmailTemplate(body, userId);
    }
    async sendEmailTemplatetoclients(body) {
        return this.service.sendEmailTemplatetoclients(body);
    }
    async getEmailTemplates(req, query) {
        const { userId } = req.user;
        return this.service.getEmailTemplates(userId, query);
    }
    async getOneEmailTemplate(id, req) {
        const { userId } = req.user;
        return this.service.getOneEmailTemplate(userId, id);
    }
    async updateEmailTemplate(id, body, req) {
        const { userId } = req.user;
        return this.service.updateEmailTemplate(userId, id, body);
    }
    async deleteEmailTemplate(id, req) {
        const { userId } = req.user;
        return this.service.deleteEmailTemplate(id, userId);
    }
    async createBroadActivity(req, body) {
        const { userId } = req.user;
        return await this.service.createBroadcastActivity(body, userId);
    }
    async getBroadcastActivity(req, query) {
        const { userId } = req.user;
        return this.service.getBroadcastActivity(userId, query);
    }
    async getBroadcastActivityDetails(id, req, query) {
        const { userId } = req.user;
        return this.service.getBroadcastActivityDetails(userId, id, query);
    }
    get(req, id, query) {
        const { userId } = req.user;
        return this.service.filteredClients(userId, id, query);
    }
    uploadFile(file, body, req) {
        const { userId } = req.user;
        return this.service.upload(file.buffer, file.originalname, file.mimetype);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/create-client-group'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "create", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-client-group'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getClientGroup", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('/delete-client-group/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "deleteClientGroup", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('update-client-group/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "update", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('add-clients-to-clientgroup/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "addClientstoClientGroup", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('remove-clients-from-clientgroup/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "removeClientsFromClientGroup", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-client-groupdetails/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getOneClientGroup", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/create-email-template'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "createEmailTemplate", null);
__decorate([
    (0, common_1.Post)('sendmail'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "sendEmailTemplatetoclients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-email-template'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getEmailTemplates", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-one-emailTemplate/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getOneEmailTemplate", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('update-email-temlate/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "updateEmailTemplate", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('/delete-email-template/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "deleteEmailTemplate", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/create-broadcast-activity'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "createBroadActivity", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-brocastactivity'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getBroadcastActivity", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-broadcastactivity/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CommunicationController.prototype, "getBroadcastActivityDetails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/get-filtered-clients/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], CommunicationController.prototype, "get", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/upload-files'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", void 0)
], CommunicationController.prototype, "uploadFile", null);
CommunicationController = CommunicationController_1 = __decorate([
    (0, common_1.Controller)('communication'),
    __metadata("design:paramtypes", [event_emitter_1.EventEmitter2, communication_service_1.CommunicationService])
], CommunicationController);
exports.CommunicationController = CommunicationController;
//# sourceMappingURL=communication.controller.js.map