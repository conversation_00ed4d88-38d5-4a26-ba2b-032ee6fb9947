"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrConfigController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
const config_services_1 = require("../service/config.services");
let GstrConfigController = class GstrConfigController {
    constructor(service) {
        this.service = service;
    }
    async gstAtomClient(req, id) {
        const { userId } = req.user;
        return this.service.gstAtomClient(userId, id);
    }
    disableAtomProGstrClient(body, req) {
        const { userId } = req.user;
        return this.service.disableAtomProGstrClient(userId, body);
    }
    disableGstrSingleClient(req, id) {
        const { userId } = req.user;
        return this.service.disableGstrSingleClient(id, userId);
    }
    getDeletedGstrClients(req, query) {
        const { userId } = req.user;
        return this.service.getDeletedGstrClients(userId, query);
    }
    async exportdeletedGstClient(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportdeletedGstClient(userId, query);
    }
    enableGstrClient(req, id) {
        const { userId } = req.user;
        return this.service.enableGstrClient(id, userId);
    }
    enableBulkGstrClient(req, body) {
        const { userId } = req.user;
        return this.service.enableBulkGstrClient(userId, body);
    }
    getBulkSyncStatus(req) {
        const { userId } = req.user;
        return this.service.getBulkSyncStatus(userId);
    }
    enableStatus(req) {
        const { userId } = req.user;
        return this.service.updateEnableStatus(userId);
    }
    updateDisableStatus(req) {
        const { userId } = req.user;
        return this.service.updateDisableStatus(userId);
    }
    async organizationScheduling(req, body) {
        const { userId } = req.user;
        return this.service.organizationGstrScheduling(userId, body);
    }
    createNoticeAndOrderItem(req, body) {
        const { userId } = req.user;
        return this.service.createNoticeAndOrderItem(userId, body);
    }
    updateNoticeAndOrder(req, body) {
        const { userId } = req.user;
        return this.service.updateNoticeAndOrder(userId, body);
    }
    deleteNoticeAndOrder(req, id) {
        const { userId } = req.user;
        return this.service.deleteNoticeAndOrder(userId, id);
    }
    createAdditionalNotice(req, body) {
        const { userId } = req.user;
        return this.service.createAdditionalNotice(userId, body);
    }
    updateAdditionalNotice(req, body) {
        const { userId } = req.user;
        return this.service.updateAdditionalNotice(userId, body);
    }
    deleteAdditionalNotice(req, id) {
        const { userId } = req.user;
        return this.service.deleteAdditionalNotice(userId, id);
    }
    completeTaskGstrOne(req, body) {
        const { userId } = req.user;
        return this.service.completeTasksWithAtomProSync(userId, body);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('atom-client/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], GstrConfigController.prototype, "gstAtomClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableGstrClients'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "disableAtomProGstrClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableGstr/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "disableGstrSingleClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('deletedGstrClients'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "getDeletedGstrClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/export-deletedGstrClients'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrConfigController.prototype, "exportdeletedGstClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('enableGstr/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "enableGstrClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('enableGstr/bulk-enable'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "enableBulkGstrClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('bulkSyncStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "getBulkSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('enableStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "enableStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "updateDisableStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('scheduling'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrConfigController.prototype, "organizationScheduling", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('noticeAndOrder'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "createNoticeAndOrderItem", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('noticeAndOrder'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "updateNoticeAndOrder", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('noticeAndOrder/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "deleteNoticeAndOrder", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('additionalNotice'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "createAdditionalNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('additionalNotice'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "updateAdditionalNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('additionalNotice/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "deleteAdditionalNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('completeTaskGstrOne'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrConfigController.prototype, "completeTaskGstrOne", null);
GstrConfigController = __decorate([
    (0, common_1.Controller)('gstr-config'),
    __metadata("design:paramtypes", [config_services_1.GstrConfigService])
], GstrConfigController);
exports.GstrConfigController = GstrConfigController;
//# sourceMappingURL=config.controller.js.map