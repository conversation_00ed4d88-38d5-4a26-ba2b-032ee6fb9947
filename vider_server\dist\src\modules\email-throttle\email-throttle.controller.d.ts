import { EmailThrottleService } from './email-throttle.service';
export declare class EmailThrottleController {
    private readonly emailThrottleService;
    constructor(emailThrottleService: EmailThrottleService);
    queueEmails(body: {
        to: string;
        subject: string;
        body: string;
    }): Promise<{
        message: string;
    }>;
    queueBulkEmails(body: {
        emails: {
            to: string;
            subject: string;
            body: string;
        }[];
    }): Promise<{
        message: string;
    }>;
}
