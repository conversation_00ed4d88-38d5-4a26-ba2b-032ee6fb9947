interface IData {
    data: any;
    email: string;
    filePath: string;
    subject: string;
    key: string;
    id: number;
    clientMail?: string;
    invoiceId?: number;
    type?: string;
}
export declare function sendMailViaAny(data: any): Promise<any>;
export declare function sendnewMail({ data, email, filePath, subject, key, id, clientMail, invoiceId, type, }: IData): Promise<unknown>;
export declare function sendnewMailToBusinessTeam({ data, email, filePath, subject, key, id, clientMail, }: IData): Promise<unknown>;
export {};
