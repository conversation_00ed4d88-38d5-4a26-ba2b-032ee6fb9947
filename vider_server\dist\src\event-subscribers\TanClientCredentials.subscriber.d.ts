import { Connection, EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
export declare class TanClientCredentialsSubscriber implements EntitySubscriberInterface<TanClientCredentials> {
    private readonly connection;
    constructor(connection: Connection);
    listenTo(): typeof TanClientCredentials;
    beforeUpdate(event: UpdateEvent<TanClientCredentials>): Promise<void>;
    afterInsert(event: InsertEvent<TanClientCredentials>): Promise<void>;
    afterUpdate(event: UpdateEvent<TanClientCredentials>): Promise<void>;
}
