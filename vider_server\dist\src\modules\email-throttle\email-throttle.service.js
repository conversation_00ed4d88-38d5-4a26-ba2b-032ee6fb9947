"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EmailThrottleService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailThrottleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const email_queue_entity_1 = require("./email-queue.entity");
const nodemailer = require("nodemailer");
let transporter = nodemailer.createTransport({
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
});
let EmailThrottleService = EmailThrottleService_1 = class EmailThrottleService {
    constructor(queueRepo) {
        this.queueRepo = queueRepo;
        this.logger = new common_1.Logger(EmailThrottleService_1.name);
        this.sending = false;
    }
    async enqueueEmail(to, subject, body) {
        const email = this.queueRepo.create({ to, subject, body });
        await this.queueRepo.save(email);
        this.logger.log(`Email queued: ${to}`);
    }
    async handleEmailQueue() {
        if (this.sending)
            return;
        this.sending = true;
        try {
            const batch = await this.queueRepo.find({
                order: { createdAt: 'ASC' },
                take: 5,
            });
            console.log(batch, 'batch');
            for (const email of batch) {
                try {
                    await this.sendEmail(email);
                    await this.queueRepo.remove(email);
                }
                catch (err) {
                    email.retryCount++;
                    if (email.retryCount < 3) {
                        await this.queueRepo.save(email);
                    }
                    else {
                        await this.queueRepo.remove(email);
                    }
                }
            }
        }
        finally {
            this.sending = false;
        }
    }
    async sendEmail(email) {
        let mailOptions = {
            from: {
                name: 'Vider',
                address: process.env.FROM_EMAIL,
            },
            to: email.to,
            subject: email.subject,
            html: email.body,
        };
        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                console.log(error);
            }
            else {
            }
        });
        this.logger.log(`Sending email to ${email.to}`);
        await new Promise((r) => setTimeout(r, 50));
    }
};
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_SECOND),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailThrottleService.prototype, "handleEmailQueue", null);
EmailThrottleService = EmailThrottleService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(email_queue_entity_1.EmailQueue, 'sqliteConnection')),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EmailThrottleService);
exports.EmailThrottleService = EmailThrottleService;
//# sourceMappingURL=email-throttle.service.js.map