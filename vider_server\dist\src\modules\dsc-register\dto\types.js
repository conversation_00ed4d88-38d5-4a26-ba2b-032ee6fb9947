"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubCategoryEnum = exports.CategoryEnum = void 0;
var CategoryEnum;
(function (CategoryEnum) {
    CategoryEnum["INDIVIDUAL"] = "individual";
    CategoryEnum["HUF"] = "huf";
    CategoryEnum["PARTNERSHIP_FIRM"] = "partnership_firm";
    CategoryEnum["LLP"] = "llp";
    CategoryEnum["COMPNAY"] = "company";
    CategoryEnum["TRUST"] = "trust";
    CategoryEnum["SOCIETY"] = "society";
    CategoryEnum["AOP"] = "aop";
    CategoryEnum["BOI"] = "boi";
    CategoryEnum["COROPORATIONS"] = "corporations";
    CategoryEnum["GOVERNMENT"] = "govermanment";
    CategoryEnum["ARTIFICIAL_JUDICIAL_PERSON"] = "artificial_judicial_person";
})(CategoryEnum = exports.CategoryEnum || (exports.CategoryEnum = {}));
var SubCategoryEnum;
(function (SubCategoryEnum) {
    SubCategoryEnum["INDIAN"] = "indian";
    SubCategoryEnum["FOREIGN"] = "foreign";
    SubCategoryEnum["PRIVATE"] = "private";
    SubCategoryEnum["PUBLIC"] = "public";
    SubCategoryEnum["GOVERNMENT"] = "government";
    SubCategoryEnum["OPC"] = "opc";
    SubCategoryEnum["SEC_8"] = "sec_8";
    SubCategoryEnum["TRUST"] = "trust";
    SubCategoryEnum["PRIVATE_DISCRETIONARY_TRUST"] = "private_discretionary_trust";
    SubCategoryEnum["SOCIETY"] = "society";
    SubCategoryEnum["COOPERATIVE_SOCIETY"] = "cooperative_society";
    SubCategoryEnum["STATE"] = "state";
    SubCategoryEnum["CENTRAL"] = "central";
})(SubCategoryEnum = exports.SubCategoryEnum || (exports.SubCategoryEnum = {}));
//# sourceMappingURL=types.js.map