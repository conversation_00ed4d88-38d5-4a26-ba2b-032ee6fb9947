"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanDashboardController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
const tan_dashboard_service_1 = require("../service/tan-dashboard-service");
let TanDashboardController = class TanDashboardController {
    constructor(service) {
        this.service = service;
    }
    getFormsUdinAnalytics(req, query) {
        const { userId } = req.user;
        return this.service.getFormsUdinAnalytics(userId, query.assessmentYear);
    }
    incometaxClientCheck(req, query) {
        const { userId } = req.user;
        return this.service.incometaxClientCheck(userId, query);
    }
    async exportTanInvalid(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportTanInvalid(userId, query);
    }
    getIncometaxConfigStatus(req, query) {
        const { userId } = req.user;
        return this.service.getIncometaxConfigStatus(userId, query.startDates);
    }
    getFormsAnalytics(req, query) {
        const { userId } = req.user;
        return this.service.getFormsAnalytics(userId, query.assessmentYear);
    }
    getFormsNavigateAnalytics(req, query) {
        const { userId } = req.user;
        return this.service.getFormsNavigateAnalytics(userId, query);
    }
    getFormsCorrectionAnalytics(req, query) {
        const { userId } = req.user;
        return this.service.getFormsCorrectionAnalytics(userId, query.assessmentYear);
    }
    eExcelProccedidingFyiNotice(req, query) {
        const { userId } = req.user;
        return this.service.getExcelCombinedNoticesCount(userId, query);
    }
    tracesNotice(req, query) {
        const { userId } = req.user;
        return this.service.tracesNotice(userId, query);
    }
    getExcelFyaEvents(req, query) {
        const { userId } = req.user;
        return this.service.getExcelFyaEvents(userId, query.startDates);
    }
    getExcelResponseDueEvents(req, query) {
        const { userId } = req.user;
        return this.service.getExcelResponseDueEvents(userId, query.startDates);
    }
    getTracesEvents(req, query) {
        const { userId } = req.user;
        return this.service.getTracesEvents(userId, query.startDates);
    }
    incometaxTanClientCheck(req, query) {
        const { userId } = req.user;
        return this.service.incometaxTanClientCheck(userId, query);
    }
    tracesClientCheck(req, query) {
        const { userId } = req.user;
        return this.service.tracesClientCheck(userId, query);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/forms-udin'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getFormsUdinAnalytics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('verification'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "incometaxClientCheck", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/exportTanInvalid'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanDashboardController.prototype, "exportTanInvalid", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/config-status'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getIncometaxConfigStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/forms-analytics'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getFormsAnalytics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/tds-analytics'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getFormsNavigateAnalytics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/forms-correction-analytics'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getFormsCorrectionAnalytics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('eProceedingNotices-excel'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "eExcelProccedidingFyiNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('tracesNotices'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "tracesNotice", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/fya-events-excel'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getExcelFyaEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/response-due-events-excel'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getExcelResponseDueEvents", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('/trace-issue-event'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "getTracesEvents", null);
__decorate([
    (0, common_1.Get)('verification-tan'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "incometaxTanClientCheck", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('verification-traces'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanDashboardController.prototype, "tracesClientCheck", null);
TanDashboardController = __decorate([
    (0, common_1.Controller)('incometaxtan-dashboard'),
    __metadata("design:paramtypes", [tan_dashboard_service_1.TanDashboardService])
], TanDashboardController);
exports.TanDashboardController = TanDashboardController;
//# sourceMappingURL=tan-dashboard.controller.js.map