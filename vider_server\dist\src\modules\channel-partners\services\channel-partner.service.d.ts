import { ChannelPartner } from '../entity/channel-partner.entity';
import { CouponCode } from '../entity/coupon-code.entity';
import { ChannelPartnerSignup } from '../entity/channel-partner-signup.entity';
export declare class ChannelPartnerService {
    getAllPartners(): Promise<ChannelPartner[]>;
    getActivePartners(): Promise<ChannelPartner[]>;
    createPartner(dto: any): Promise<ChannelPartner[]>;
    updatePartner(id: number, dto: any): Promise<ChannelPartner>;
    updatePartnerToogle(id: number, dto: any): Promise<ChannelPartner>;
    getAllCoupons(): Promise<CouponCode[]>;
    createCoupon(dto: any): Promise<CouponCode[]>;
    updateCoupon(id: number, dto: any): Promise<CouponCode>;
    validateCoupon(dto: any): Promise<{
        message: string;
        couponId: number;
        partnerId: number;
    }>;
    getSignUps(query: any): Promise<{
        count: number;
        result: ChannelPartnerSignup[];
    }>;
    updateSignUpStatus(id: number, data: any): Promise<void>;
    getPartnerAnalytics(id: number): Promise<{
        partner: {
            name: string;
            status: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
        coupons: {
            active: number;
            expired: number;
        };
        users: number;
        leadStatusCounts: any[];
    }>;
}
