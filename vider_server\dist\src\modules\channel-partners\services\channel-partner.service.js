"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelPartnerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const channel_partner_entity_1 = require("../entity/channel-partner.entity");
const coupon_code_entity_1 = require("../entity/coupon-code.entity");
const channel_partner_signup_entity_1 = require("../entity/channel-partner-signup.entity");
let ChannelPartnerService = class ChannelPartnerService {
    async getAllPartners() {
        const channelPartners = await channel_partner_entity_1.ChannelPartner.find({ order: { createdAt: 'DESC' } });
        return channelPartners;
    }
    async getActivePartners() {
        const channelPartners = await channel_partner_entity_1.ChannelPartner.find({
            where: { isActive: true },
            order: { createdAt: 'DESC' },
        });
        return channelPartners;
    }
    async createPartner(dto) {
        const partner = channel_partner_entity_1.ChannelPartner.create(dto);
        return await channel_partner_entity_1.ChannelPartner.save(partner);
    }
    async updatePartner(id, dto) {
        const partner = await channel_partner_entity_1.ChannelPartner.findOne({ id });
        if (!partner)
            throw new common_1.NotFoundException('Channel partner not found');
        Object.assign(partner, dto);
        return await channel_partner_entity_1.ChannelPartner.save(partner);
    }
    async updatePartnerToogle(id, dto) {
        const partner = await channel_partner_entity_1.ChannelPartner.findOne({ id });
        if (!partner)
            throw new common_1.NotFoundException('Channel partner not found');
        partner.isActive = dto.isActive;
        return await channel_partner_entity_1.ChannelPartner.save(partner);
    }
    async getAllCoupons() {
        const couponCodes = await coupon_code_entity_1.CouponCode.find({
            relations: ['channelPartner'],
            order: { createdAt: 'DESC' },
        });
        return couponCodes;
    }
    async createCoupon(dto) {
        try {
            const partner = await channel_partner_entity_1.ChannelPartner.findOne({
                id: dto.channelPartnerId,
            });
            if (!partner)
                throw new common_1.BadRequestException('Invalid partner ID');
            const coupon = coupon_code_entity_1.CouponCode.create(Object.assign(Object.assign({}, dto), { channelPartner: partner }));
            return await coupon_code_entity_1.CouponCode.save(coupon);
        }
        catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                throw new common_1.BadRequestException("Can't have duplicate coupon code");
            }
            throw new common_1.InternalServerErrorException('Failed to create coupon');
        }
    }
    async updateCoupon(id, dto) {
        try {
            const coupon = await coupon_code_entity_1.CouponCode.findOne({
                where: { id },
                relations: ['channelPartner'],
            });
            if (!coupon)
                throw new common_1.NotFoundException('Coupon not found');
            if (dto.channelPartnerId) {
                const partner = await channel_partner_entity_1.ChannelPartner.findOne({
                    id: dto.channelPartnerId,
                });
                if (!partner)
                    throw new common_1.BadRequestException('Invalid partner ID');
                coupon.channelPartner = partner;
            }
            Object.assign(coupon, dto);
            return coupon_code_entity_1.CouponCode.save(coupon);
        }
        catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                throw new common_1.BadRequestException("Can't have duplicate coupon code");
            }
            throw new common_1.InternalServerErrorException('Failed to create coupon');
        }
    }
    async validateCoupon(dto) {
        var _a, _b, _c;
        const { couponCode, channelPartnerId } = dto;
        const now = new Date();
        const coupon = await coupon_code_entity_1.CouponCode.findOne({
            where: { code: couponCode },
            relations: ['channelPartner'],
        });
        if (!coupon) {
            throw new common_1.BadRequestException('Invalid coupon code');
        }
        if (channelPartnerId && ((_a = coupon.channelPartner) === null || _a === void 0 ? void 0 : _a.id) !== channelPartnerId) {
            throw new common_1.BadRequestException(`Coupon belongs to partner "${(_b = coupon.channelPartner) === null || _b === void 0 ? void 0 : _b.name}"`);
        }
        if (new Date(coupon.validFrom) > now || new Date(coupon.validTo) < now) {
            throw new common_1.BadRequestException('Coupon is expired or not yet valid');
        }
        return {
            message: 'Coupon is valid',
            couponId: coupon.id,
            partnerId: (_c = coupon.channelPartner) === null || _c === void 0 ? void 0 : _c.id,
        };
    }
    async getSignUps(query) {
        const { limit, offset, search, status } = query;
        const signUpRecords = (0, typeorm_1.createQueryBuilder)(channel_partner_signup_entity_1.ChannelPartnerSignup, 'signUps')
            .leftJoinAndSelect('signUps.channelPartner', 'channelPartner')
            .leftJoinAndSelect('signUps.coupon', 'coupon')
            .leftJoinAndSelect('signUps.user', 'user')
            .leftJoinAndSelect('user.organization', 'organization');
        if (search) {
            signUpRecords.where(new typeorm_1.Brackets((qb) => {
                qb.where('user.fullName LIKE :search1', {
                    search1: `%${query.search}%`,
                });
                qb.orWhere('coupon.code LIKE :search2', {
                    search2: `%${query.search}%`,
                });
                qb.orWhere('channelPartner.name LIKE :search3', {
                    search3: `%${query.search}%`,
                });
            }));
        }
        if (status) {
            signUpRecords.andWhere('signUps.leadStatus = :leadStatus', { leadStatus: status });
        }
        if (offset >= 0) {
            signUpRecords.skip(offset);
        }
        if (limit) {
            signUpRecords.take(limit);
        }
        const result = await signUpRecords.getManyAndCount();
        return {
            count: result[1],
            result: result[0],
        };
    }
    async updateSignUpStatus(id, data) {
        const { status } = data;
        const signUpRecord = await channel_partner_signup_entity_1.ChannelPartnerSignup.findOne({ id });
        if (signUpRecord) {
            signUpRecord.leadStatus = channel_partner_signup_entity_1.leadStatusEnum[status];
            await signUpRecord.save();
        }
        return;
    }
    async getPartnerAnalytics(id) {
        const partner = await channel_partner_entity_1.ChannelPartner.findOne({ where: { id } });
        const today = new Date();
        const [activeCoupons, expiredCoupons] = await Promise.all([
            coupon_code_entity_1.CouponCode.count({
                where: {
                    channelPartner: { id },
                    validFrom: (0, typeorm_1.LessThan)(today),
                    validTo: (0, typeorm_1.MoreThan)(today),
                },
            }),
            coupon_code_entity_1.CouponCode.count({
                where: {
                    channelPartner: { id },
                    validTo: (0, typeorm_1.LessThan)(today),
                },
            }),
        ]);
        const totalUsers = await channel_partner_signup_entity_1.ChannelPartnerSignup.count({ where: { channelPartner: { id } } });
        const leadCounts = await channel_partner_signup_entity_1.ChannelPartnerSignup.createQueryBuilder('signup')
            .select('signup.leadStatus', 'leadStatus')
            .addSelect('COUNT(*)', 'count')
            .where('signup.channelPartner.id = :id', { id })
            .groupBy('signup.leadStatus')
            .getRawMany();
        return {
            partner: {
                name: partner.name,
                status: partner.isActive,
                createdAt: partner.createdAt,
                updatedAt: partner.updatedAt,
            },
            coupons: {
                active: activeCoupons,
                expired: expiredCoupons,
            },
            users: totalUsers,
            leadStatusCounts: leadCounts,
        };
    }
};
ChannelPartnerService = __decorate([
    (0, common_1.Injectable)()
], ChannelPartnerService);
exports.ChannelPartnerService = ChannelPartnerService;
//# sourceMappingURL=channel-partner.service.js.map