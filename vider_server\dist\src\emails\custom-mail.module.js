"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomMailModule = void 0;
const common_1 = require("@nestjs/common");
const customSendMail_1 = require("./customSendMail");
const email_throttle_module_1 = require("../modules/email-throttle/email-throttle.module");
let CustomMailModule = class CustomMailModule {
};
CustomMailModule = __decorate([
    (0, common_1.Module)({
        imports: [email_throttle_module_1.EmailThrottleModule],
        providers: [customSendMail_1.CustomMailService],
        exports: [customSendMail_1.CustomMailService],
    })
], CustomMailModule);
exports.CustomMailModule = CustomMailModule;
//# sourceMappingURL=custom-mail.module.js.map