import { Connection, EntitySubscriberInterface, InsertEvent } from "typeorm";
import ViderMarketingWebhook from "src/modules/webhook-marketing/entity/vider-marketing-webhook.entity";
export declare class ViderMarketingWebhookSubscriber implements EntitySubscriberInterface<ViderMarketingWebhook> {
    private readonly connection;
    constructor(connection: Connection);
    listenTo(): typeof ViderMarketingWebhook;
    beforeInsert(event: InsertEvent<ViderMarketingWebhook>): void | Promise<any>;
    afterInsert(event: InsertEvent<ViderMarketingWebhook>): Promise<void>;
    handleMedia(mediaId: string, mobileNumber: string, document: any): Promise<void>;
}
