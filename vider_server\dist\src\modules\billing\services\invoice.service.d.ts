/// <reference types="node" />
/// <reference types="node" />
import Task from 'src/modules/tasks/entity/task.entity';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { FindInvoicesDto, NextInvoiceNumberDto } from '../dto/find-invoices.dto';
import { GetUnbilledTasksDto } from '../dto/get-unbilled.dto';
import { Invoice } from '../entitities/invoice.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FindClientBillingInvoices } from '../dto/find-client-billing-invoices.dto';
import { ReceiptsService } from './receipts.service';
import Activity from 'src/modules/activity/activity.entity';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { AwsService } from 'src/modules/storage/upload.service';
import * as ExcelJS from 'exceljs';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
export declare class InvoiceService {
    private eventEmitter;
    private receiptsService;
    private oneDriveService;
    private googleDriveService;
    private awsService;
    constructor(eventEmitter: EventEmitter2, receiptsService: ReceiptsService, oneDriveService: OneDriveStorageService, googleDriveService: GoogleDriveStorageService, awsService: AwsService);
    createInvoice(userId: number, body: CreateInvoiceDto): Promise<Buffer | Invoice>;
    updateInvoice(invoiceid: number, body: any, userId: any): Promise<Buffer | Invoice>;
    getInvoices(userId: number, query: FindInvoicesDto): Promise<{
        totalCount: number;
        result: Invoice[];
    }>;
    exportTdsBilling(userId: number, body: any): Promise<ExcelJS.Buffer>;
    exportGstBilling(userId: number, body: any): Promise<ExcelJS.Buffer>;
    getClientPortalInvoices(clientId: number, query: FindInvoicesDto): Promise<{
        totalCount: number;
        result: Invoice[];
    }>;
    getClientInvoices(clientid: number): Promise<any>;
    getClientBillingInvoices(query: FindClientBillingInvoices): Promise<{
        totalCount: any;
        invoices: any;
    }>;
    getClientGroupBillingInvoices(query: FindClientBillingInvoices): Promise<{
        totalCount: any;
        invoices: any;
    }>;
    getTasks(query: GetUnbilledTasksDto): Promise<{
        totalCount: number;
        result: Task[];
    }>;
    getInvoice(estimateId: number, query: any): Promise<Invoice | "Un-Authorized">;
    cancelInvoice(estimateId: number, userId: any): Promise<Invoice>;
    downloadInvoice(invoiceId: number, body: any): Promise<Buffer>;
    downloadInvoicewithoutEmittor(invoiceId: number): Promise<Buffer>;
    exportLineItem(userId: number, body: FindInvoicesDto): Promise<{
        file: ExcelJS.Buffer;
        type: string;
    }>;
    exportTaxRate(userId: number, body: FindInvoicesDto): Promise<{
        file: ExcelJS.Buffer;
        type: string;
    }>;
    submitForApproval(invoiceId: number): Promise<Invoice>;
    getNextInvoiceNumber(userId: number, query: NextInvoiceNumberDto): Promise<{
        prifix: string;
        number: string;
    }>;
    getBillingActivity(userId: any, id: any, query: any): Promise<Activity[]>;
}
