"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanAutomationController = void 0;
const common_1 = require("@nestjs/common");
const tan_automation_service_1 = require("../service/tan-automation.service");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
let TanAutomationController = class TanAutomationController {
    constructor(service) {
        this.service = service;
    }
    findAll(req, query) {
        const { userId } = req.user;
        return this.service.findAll(userId, query);
    }
    async exportIncomeTaxTanClients(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportIncomeTaxTanClients(userId, query);
    }
    addClientTanCredentials(body, req) {
        const { userId } = req.user;
        return this.service.addClientTanCredentials(userId, body);
    }
    getAllClients(req, query) {
        const { userId } = req.user;
        return this.service.getAllClients(userId, query);
    }
    getIncomeTaxProfile(id, req) {
        const { userId } = req.user;
        return this.service.getIncomeTaxProfile(userId, id);
    }
    clientEChallan(id, query, req) {
        const { userId } = req.user;
        return this.service.clientEChallan(userId, query, id);
    }
    async exportTanClientChallan(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanClientChallan(userId, query);
    }
    findEchallan(req, id) {
        const { userId } = req.user;
        return this.service.findEchallan(userId, id);
    }
    findEchallans(req, query) {
        const { userId } = req.user;
        return this.service.findEchallans(userId, query);
    }
    async exportTanIncomeTaxChallans(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanIncomeTaxChallans(userId, query);
    }
    getClientform(id, query, req) {
        const { userId } = req.user;
        return this.service.getClientform(id, query, userId);
    }
    async exportTanClientForm(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanClientForm(userId, query);
    }
    findForm(req, id) {
        const { userId } = req.user;
        return this.service.findForm(userId, id);
    }
    findAllForms(req, query) {
        const { userId } = req.user;
        return this.service.findAllForms(userId, query);
    }
    async exportTanIncomeTaxForms(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanIncomeTaxForms(userId, query);
    }
    getActivityLogData(req, id, query) {
        const { userId } = req.user;
        return this.service.getActivityLogData(id, query, userId);
    }
    getclientReport(req, query) {
        const { userId } = req.user;
        return this.service.getclientReport(userId, query);
    }
    async exportTanSyncStatus(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanSyncStatus(userId, query);
    }
    async exportTanIncomeTaxReports(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanIncomeTaxReports(userId, query);
    }
    getclientAutoStatus(id, req) {
        const { userId } = req.user;
        return this.service.getclientAutoStatus(id, userId);
    }
    findMycas(req, query) {
        const { userId } = req.user;
        return this.service.findMycas(userId, query);
    }
    async exportTanIncomeTaxMycas(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanIncomeTaxMycas(userId, query);
    }
    getMycaFormTypes(req) {
        const { userId } = req.user;
        return this.service.getMycaFormTypes(userId);
    }
    clientMycas(id, query, req) {
        const { userId } = req.user;
        return this.service.clientMycas(userId, query, id);
    }
    async exportTanClientMyCas(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanClientMyCas(userId, query);
    }
    update(id, body, req) {
        const { userId } = req.user;
        return this.service.updateClientTanCredentials(id, body, userId);
    }
    getClientTraceCommunications(id, query, req) {
        const { userId } = req.user;
        return this.service.getClientTraceCommunications(id, query, userId);
    }
    async exportClientTanTracesInbox(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportClientTanTracesInbox(userId, query);
    }
    findAllTraceCommunication(req, query) {
        const { userId } = req.user;
        return this.service.findAllTraceCommunication(userId, query);
    }
    async exportClientTracesInbox(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportClientTracesInbox(userId, query);
    }
    getActivityLogTracesData(req, id, query) {
        const { userId } = req.user;
        return this.service.getActivityLogTracesData(id, query, userId);
    }
    getclientAutoTracesStatus(id, req) {
        const { userId } = req.user;
        return this.service.getclientAutoTracesStatus(id, userId);
    }
    getTraceReport(req, query) {
        const { userId } = req.user;
        return this.service.getTraceReport(userId, query);
    }
    async exportTanTraceSyncStatus(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportTanTraceSyncStatus(userId, query);
    }
    findFyaTempNotices(req, query) {
        const { userId } = req.user;
        return this.service.findFyaTempNotices(userId, query);
    }
    findFyiTempNotices(req, query) {
        const { userId } = req.user;
        return this.service.findFyiTempNotices(userId, query);
    }
    getExcelFyaSections(req) {
        const { userId } = req.user;
        return this.service.getExcelFyaSections(userId);
    }
    getExcelFyiSections(req) {
        const { userId } = req.user;
        return this.service.getExcelFyiSections(userId);
    }
    getClientExcelProceedingFyi(id, query, req) {
        const { userId } = req.user;
        return this.service.getClientExcelProceedingFyi(id, query, userId);
    }
    getClientExcelProceedingFya(id, query, req) {
        const { userId } = req.user;
        return this.service.getClientExcelProceedingFya(id, query, userId);
    }
    getExcelFyiNotices(req, query) {
        const { userId } = req.user;
        return this.service.getExcelCombinedNotices(userId, query);
    }
    findAllDemands(req, query) {
        const { userId } = req.user;
        return this.service.findAllDemands(userId, query);
    }
    findDeamnd(req, id) {
        const { userId } = req.user;
        return this.service.findDemand(userId, id);
    }
    getClientDemand(id, query, req) {
        const { userId } = req.user;
        return this.service.getClientDemand(id, query, userId);
    }
    async exportDemands(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportDemands(userId, query);
    }
    async exportClientDemand(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportClientDemand(userId, query);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('credentials'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findAll", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/client-credentialsexport'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportIncomeTaxTanClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('credentials'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "addClientTanCredentials", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clients'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getAllClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('profile/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getIncomeTaxProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientechallan/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "clientEChallan", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/tanClientChallan-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanClientChallan", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('e-challan/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findEchallan", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('e-challans'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findEchallans", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/e-challans-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanIncomeTaxChallans", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientform/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getClientform", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/tanClientForm-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanClientForm", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('form/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findForm", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('forms'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findAllForms", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/forms-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanIncomeTaxForms", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('activity/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getActivityLogData", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('reports'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getclientReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/exportTanSyncStatus-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/lastSync-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanIncomeTaxReports", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientAutoStatus/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getclientAutoStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('my-cas'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findMycas", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/myCas-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanIncomeTaxMycas", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('form-types'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getMycaFormTypes", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('mycas/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "clientMycas", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/tanMyCas-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanClientMyCas", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('credentials/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "update", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('traces-communication/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getClientTraceCommunications", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/clientTanTraces-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportClientTanTracesInbox", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('traces-communications'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findAllTraceCommunication", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/clientTraces-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportClientTracesInbox", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('activity-traces/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getActivityLogTracesData", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientAutoStatus-traces/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getclientAutoTracesStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('traces-reports'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getTraceReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/exportTanTraceSyncStatus-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportTanTraceSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('temp-notice-fya'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findFyaTempNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('temp-notice-fyi'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findFyiTempNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('sections-fya-excel'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getExcelFyaSections", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('sections-fyi-excel'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getExcelFyiSections", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientproceedingfyi-excel/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getClientExcelProceedingFyi", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientproceedingfya-excel/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getClientExcelProceedingFya", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('eproceeding-notice-excel'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getExcelFyiNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('demands'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findAllDemands", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('demand/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "findDeamnd", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clientDemand/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanAutomationController.prototype, "getClientDemand", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/demands-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportDemands", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/export-clientdemand'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanAutomationController.prototype, "exportClientDemand", null);
TanAutomationController = __decorate([
    (0, common_1.Controller)('incometaxtan'),
    __metadata("design:paramtypes", [tan_automation_service_1.TanAutomationService])
], TanAutomationController);
exports.TanAutomationController = TanAutomationController;
//# sourceMappingURL=tan-automation.controller.js.map