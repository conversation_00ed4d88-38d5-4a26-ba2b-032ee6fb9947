import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';
import { UpdateTaskApprovalsDto } from 'src/modules/atm-qtm-approval/dto/update-approvals.dto';
export declare class ApprovalLevelService {
    find(userId: number): Promise<ApprovalProcedures[]>;
    findTasks(data: any): Promise<any[] | {
        result: any;
        taskStatus: TaskStatusEnum;
    }>;
    getApprovals(id: string, status: string): Promise<any>;
    getNextApprovalDetails(id: string, level: string): Promise<any>;
    updateTaskApprovals(body: UpdateTaskApprovalsDto, userId: number): Promise<string>;
    approvalProcess(id: string, approvalData: ApprovalProcedures): Promise<void>;
}
