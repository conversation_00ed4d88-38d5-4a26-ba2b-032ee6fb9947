"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrClientService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../../users/entities/user.entity");
const gstrCredentials_entity_1 = require("../entity/gstrCredentials.entity");
const typeorm_1 = require("typeorm");
const axios_1 = require("axios");
const automation_machines_entity_1 = require("../../automation/entities/automation_machines.entity");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
const client_entity_1 = require("../../clients/entity/client.entity");
const password_entity_1 = require("../../clients/entity/password.entity");
const atomProReUse_1 = require("../../../utils/atomProReUse");
const whatsapp_controller_1 = require("../../whatsapp/whatsapp.controller");
const ExcelJS = require("exceljs");
const categoryLabels = {
    individual: 'Individual',
    huf: 'Hindu Undivided Family',
    partnership_firm: 'Partnership Firm',
    llp: 'Limited Liability Partnership',
    company: 'Company',
    opc: 'OPC',
    public: 'Public Limited',
    government: 'Government',
    sec_8: 'Section-8',
    foreign: 'Foreign',
    aop: 'Association of Persons',
    boi: 'Body of Individuals',
    trust: 'Trust',
    public_trust: 'Public Trust',
    private_discretionary_trust: 'Private Discretionary Trust',
    state: 'State',
    central: 'Central',
    local_authority: 'Local Authority',
    artificial_judicial_person: 'Artificial Juridical Person',
};
const datesFormation_1 = require("../../../utils/datesFormation");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
const automation_machines_archive_entity_1 = require("../../automation/entities/automation_machines_archive.entity");
const permission_1 = require("../../tasks/permission");
let GstrClientService = class GstrClientService {
    async getGstrClients(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        let clients = (0, typeorm_1.getConnection)()
            .createQueryBuilder(gstrCredentials_entity_1.default, 'gstrCredentials')
            .select([
            'client.displayName',
            'gstrCredentials.id',
            'client.clientId',
            'client.category',
            'gstrCredentials.userName',
            'gstrCredentials.password',
            'profile.gstin',
            'clientManagers.id',
        ])
            .leftJoin('gstrCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('gstrCredentials.profile', 'profile')
            .leftJoin('client.organization', 'organization')
            .where('organization.id=:organization', { organization: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status IS NULL').orWhere('gstrCredentials.status = :enabledStatus', { enabledStatus: gstrCredentials_entity_1.GstrStatus.ENABLE });
        }));
        const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                category: 'client.category',
                clientId: 'client.clientId',
                displayName: 'client.displayName',
            };
            const column = columnMap[sort.column] || sort.column;
            clients.orderBy(column, sort.direction.toUpperCase());
        }
        if (query.search) {
            clients.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('client.displayName LIKE :search', {
                    search: `%${query.search}%`,
                });
                qb.orWhere('gstrCredentials.userName LIKE :search', {
                    search: `%${query.search}%`,
                });
                qb.orWhere('profile.gstIn LIKE :search', {
                    search: `%${query.search}%`,
                });
            }));
        }
        if (ViewAssigned && !ViewAll) {
            clients.andWhere('clientManagers.id = :userId', { userId });
        }
        if (query.offset >= 0) {
            clients.skip(query.offset);
        }
        if (query.limit) {
            clients.take(query.limit);
        }
        let result = await clients.getManyAndCount();
        return {
            count: result[1],
            result: result[0],
        };
    }
    async exportGstrClient(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let gstrClients = await this.getGstrClients(userId, newQuery);
        if (!gstrClients.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Clients');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client ID', key: 'clientId' },
            { header: 'Category', key: 'category' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'GST Number', key: 'gst' },
            { header: 'User Name', key: 'userName' },
            { header: 'Password', key: 'password' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        gstrClients.result.forEach((gstrclient) => {
            var _a, _b, _c, _d;
            const rowData = {
                serialNo: serialCounter++,
                clientId: (_a = gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.client) === null || _a === void 0 ? void 0 : _a.clientId,
                category: categoryLabels[(_b = gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.client) === null || _b === void 0 ? void 0 : _b.category] || '-',
                clientName: (_c = gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.client) === null || _c === void 0 ? void 0 : _c.displayName,
                gst: (_d = gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.profile) === null || _d === void 0 ? void 0 : _d.gstin,
                userName: gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.userName,
                password: gstrclient === null || gstrclient === void 0 ? void 0 : gstrclient.password,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getAtomClients(userId, data) {
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const entityManager = (0, typeorm_1.getManager)();
        let query = `
  SELECT id, display_name as displayName, status
  FROM client 
  WHERE organization_id = ${user === null || user === void 0 ? void 0 : user.organization.id} 
    AND status != 'DELETED'
    AND id NOT IN (
      SELECT client_id 
      FROM gstr_credentials
      WHERE organization_id = ${user === null || user === void 0 ? void 0 : user.organization.id}
        AND client_id IS NOT NULL
    )
`;
        if (data === null || data === void 0 ? void 0 : data.search) {
            query += ` AND display_name LIKE '%${data === null || data === void 0 ? void 0 : data.search}%'`;
        }
        if (data === null || data === void 0 ? void 0 : data.limit) {
            query += ` LIMIT ${data === null || data === void 0 ? void 0 : data.limit}`;
            if (data === null || data === void 0 ? void 0 : data.page) {
                query += ` OFFSET ${data === null || data === void 0 ? void 0 : data.page}`;
            }
        }
        let clients = await entityManager.query(query);
        return clients;
    }
    async addGstrCredentials(userId, body) {
        var _a, _b, _c;
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });
            const credential = await gstrCredentials_entity_1.default.findOne({
                where: { organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id, userName: body === null || body === void 0 ? void 0 : body.userName },
            });
            const organizationPreferences = await organization_preferences_entity_1.default.findOne({
                where: { organization: user.organization },
            });
            if (organizationPreferences) {
                const organizationLimit = (_b = organizationPreferences === null || organizationPreferences === void 0 ? void 0 : organizationPreferences.automationConfig) === null || _b === void 0 ? void 0 : _b.gstrLimit;
                if (organizationLimit) {
                    const gstrCredentialsCount = await gstrCredentials_entity_1.default.count({
                        where: { organizationId: user.organization.id, status: gstrCredentials_entity_1.GstrStatus.ENABLE },
                    });
                    if (organizationLimit >= gstrCredentialsCount) {
                        if (credential) {
                            throw new common_1.BadRequestException('Specified pannumber Utilize your organization already');
                        }
                        else {
                            const client = await client_entity_1.default.findOne({ where: { id: body.clientId } });
                            const details = {
                                website: 'GST | e-Filing',
                                websiteUrl: 'https://services.gst.gov.in/services/login',
                                loginId: body === null || body === void 0 ? void 0 : body.userName,
                                password: body === null || body === void 0 ? void 0 : body.password,
                                client: client,
                                isaddAtomPro: password_entity_1.IsExistingAtomPro.YES,
                                userId,
                            };
                            let password = null;
                            const passwordCheck = await password_entity_1.default.findOne({ where: { client, website: details.website, loginId: details.loginId }, order: { createdAt: 'DESC' } });
                            if (passwordCheck) {
                                password = await (0, atomProReUse_1.updateClientCredentials)(details, passwordCheck === null || passwordCheck === void 0 ? void 0 : passwordCheck.id);
                            }
                            else {
                                password = await (0, atomProReUse_1.createClientCredentials)(details);
                            }
                            const gstrCredentials = new gstrCredentials_entity_1.default();
                            gstrCredentials.userName = body === null || body === void 0 ? void 0 : body.userName.trim();
                            gstrCredentials.password = body === null || body === void 0 ? void 0 : body.password.trim();
                            gstrCredentials.userId = userId;
                            gstrCredentials.clientId = body.clientId;
                            gstrCredentials.organizationId = (_c = user === null || user === void 0 ? void 0 : user.organization) === null || _c === void 0 ? void 0 : _c.id;
                            gstrCredentials.status = gstrCredentials_entity_1.GstrStatus.ENABLE;
                            gstrCredentials.passwordId = password === null || password === void 0 ? void 0 : password.id;
                            gstrCredentials.save();
                        }
                    }
                    else {
                        throw new common_1.BadRequestException('Maximum Gstr Client Count Reached');
                    }
                }
            }
        }
        catch (error) {
            console.log('Error occur while add the incomeTax client credentials', error);
            throw new common_1.InternalServerErrorException(error);
        }
    }
    async updateGstrCredentials(id, body, userId) {
        try {
            const clientCredential = await gstrCredentials_entity_1.default.findOne({
                where: { id },
                relations: ['client'],
            });
            if (clientCredential.passwordId) {
                const password = await password_entity_1.default.findOne({
                    where: { id: clientCredential.passwordId },
                    relations: ['client'],
                });
                password.password = body.password;
                password['userId'] = userId;
                await password.save();
            }
            if (clientCredential) {
                clientCredential.password = body.password;
                await clientCredential.save();
                return clientCredential;
            }
        }
        catch (error) {
            console.log('Error occur while update the gstr client credentials', error);
        }
    }
    async sendSingleGstrCamundaRequest(userId, data) {
        try {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(data),
            };
            const response = await (0, axios_1.default)(config);
            const responseData = response === null || response === void 0 ? void 0 : response.data;
            return responseData[JSON.stringify(data[0].gstrCredentialsId)];
            if (responseData[JSON.stringify(data[0].autoCredentialsId)] ===
                'There is already an active request present') {
                return 'There is already an active request present';
            }
            else {
                return true;
            }
        }
        catch (error) {
            console.log('error in sendSingleIncometaxAutomationRequestToCamunda', error);
        }
    }
    async createGsrRequest(userId, id, body) {
        let data1 = [
            {
                modules: body === null || body === void 0 ? void 0 : body.requests,
                type: 'GSTR',
                gstrCredentialsId: id,
            },
        ];
        return this.sendSingleGstrCamundaRequest(userId, data1);
    }
    async sendIncometaxAutomationRequestToCamunda(userId, data) {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
            headers: {
                'X-USER-ID': userId,
                'Content-Type': 'application/json',
            },
            data: JSON.stringify(data),
        };
        axios_1.default
            .request(config)
            .then((response) => { })
            .catch((error) => {
            console.log(error);
        });
    }
    async bulkGstrSync(userId, data) {
        if (data === null || data === void 0 ? void 0 : data.selectedIds) {
            let requestClientList = [];
            for (let gstr of data === null || data === void 0 ? void 0 : data.selectedIds) {
                requestClientList.push({
                    modules: data === null || data === void 0 ? void 0 : data.requests,
                    gstrCredentialsId: gstr === null || gstr === void 0 ? void 0 : gstr.id,
                    type: automation_machines_entity_1.TypeEnum.GSTR,
                });
            }
            this.sendIncometaxAutomationRequestToCamunda(userId, requestClientList);
        }
    }
    async getActivityLog(id, query, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredential = await gstrCredentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const autActivity = await (0, typeorm_1.createQueryBuilder)(automation_machines_entity_1.default, 'autActivity')
                    .leftJoinAndSelect('autActivity.user', 'user')
                    .where('autActivity.gstrCredentials =:id', { id: id });
                if (query.fromDate && query.toDate) {
                    const { startTime, endTime } = (0, datesFormation_1.dateFormation)(query.fromDate, query.toDate);
                    autActivity
                        .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
                        .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
                }
                autActivity.orderBy('autActivity.id', 'DESC');
                let result = await autActivity.getMany();
                return { result, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.error('Error fetching activity log data:', error);
            return null;
        }
    }
    async getActivityArchiveLog(id, query, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredential = await gstrCredentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if (clientCredential) {
                const autActivity = await (0, typeorm_1.createQueryBuilder)(automation_machines_archive_entity_1.default, 'autActivity')
                    .leftJoinAndSelect('autActivity.user', 'user')
                    .where('autActivity.gstrCredentials =:id', { id: id });
                if (query.fromDate && query.toDate) {
                    const { startTime, endTime } = (0, datesFormation_1.dateFormation)(query.fromDate, query.toDate);
                    autActivity
                        .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
                        .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
                }
                autActivity.orderBy('autActivity.id', 'DESC');
                let result = await autActivity.getMany();
                return { result, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.error('Error fetching activity log data:', error);
            return null;
        }
    }
    async getclientSyncStatus(id, userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const clientCredentials = await gstrCredentials_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
            });
            if (clientCredentials) {
                const lastCompletedMachine = await automation_machines_entity_1.default.findOne({
                    where: { gstrCredentials: id },
                    order: {
                        id: 'DESC',
                    },
                    relations: ['gstrCredentials', 'gstrCredentials.client'],
                });
                let totalInqueueCount = 0;
                if ((lastCompletedMachine === null || lastCompletedMachine === void 0 ? void 0 : lastCompletedMachine.status) === 'INQUEUE') {
                    totalInqueueCount = await automation_machines_entity_1.default.count({
                        where: { status: 'INQUEUE', id: (0, typeorm_1.LessThan)(lastCompletedMachine.id), type: 'GSTR' },
                    });
                }
                return { lastCompletedMachine, totalInqueueCount, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error occur while getting getclientAutoStatus', error);
        }
    }
    async getCaseIdBasedClientNotices(id, userId, query) {
        var _a, _b;
        const { offset, limit } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const checkGstrCredentials = await gstrCredentials_entity_1.default.findOne({
            where: { id: id, organizationId: user.organization.id },
        });
        const totalCountQuery = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditionalNoticeOrders')
            .select('COUNT(DISTINCT gstrAdditionalNoticeOrders.arn)', 'count')
            .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
            .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
            orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .getRawOne();
        const total = Number(totalCountQuery.count);
        if (checkGstrCredentials) {
            const caseIdUniqueRecords = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditionalNoticeOrders')
                .select([
                'gstrAdditionalNoticeOrders.fy',
                'gstrAdditionalNoticeOrders.arn',
                'gstrAdditionalNoticeOrders.gstIn',
                'gstrAdditionalNoticeOrders.caseTypeName',
                'gstrAdditionalNoticeOrders.id',
                'gstrAdditionalNoticeOrders.gstrCredentialsId',
                'gstrAdditionalNoticeOrders.caseStatus',
                'gstrAdditionalNoticeOrders.caseId',
                'gstrAdditionalNoticeOrders.section',
                'gstrAdditionalNoticeOrders.nm',
                'gstrAdditionalNoticeOrders.designation',
                'gstrAdditionalNoticeOrders.refId',
                'gstrAdditionalNoticeOrders.categoryDate',
                'gstrAdditionalNoticeOrders.caseTypeId'
            ])
                .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
                .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
                orgId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
            })
                .orderBy('gstrAdditionalNoticeOrders.fy', 'DESC')
                .groupBy('gstrAdditionalNoticeOrders.arn');
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    fy: 'gstrAdditionalNoticeOrders.fy',
                    caseType: 'gstrAdditionalNoticeOrders.caseTypeName',
                };
                const column = columnMap[sort.column] || sort.column;
                caseIdUniqueRecords.orderBy(column, sort.direction.toUpperCase());
            }
            if (offset >= 0) {
                caseIdUniqueRecords.skip(offset);
            }
            if (limit) {
                caseIdUniqueRecords.take(limit);
            }
            const result = await caseIdUniqueRecords.getManyAndCount();
            return {
                count: total,
                result: result[0],
                accessDenied: true,
            };
        }
    }
    async exportCaseBasedNotices(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const gstrid = query === null || query === void 0 ? void 0 : query.gstrid;
        let gstNoticeorders = await this.getCaseIdBasedClientNotices(gstrid, userId, exportQuery);
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Add.Notice & Orders (Case ID)');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'GST', key: 'gst' },
            { header: 'Case Type', key: 'caseType' },
            { header: 'Case ID', key: 'caseId' },
            { header: 'Case Status', key: 'caseStatus' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        gstNoticeorders.result.forEach((noticeorder) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.fy,
                gst: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.gstIn,
                caseType: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseTypeName,
                caseId: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.arn,
                caseStatus: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseStatus,
            };
            const row = worksheet.addRow(rowData);
            const caseStatusCell = row.getCell('caseStatus');
            if (rowData.caseStatus === 'OPEN') {
                caseStatusCell.font = {
                    color: { argb: 'FF800000' },
                    bold: true,
                };
                caseStatusCell.value = 'Open';
            }
            else if (rowData.caseStatus === 'CLOSED') {
                caseStatusCell.font = {
                    color: { argb: 'FF008000' },
                    bold: true,
                };
                caseStatusCell.value = 'Closed';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getCaseIdBasedOrgNotices(userId, query) {
        var _a, _b;
        const { offset, limit, search, type, financialYear, caseType } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const totalCountQuery = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditionalNoticeOrders')
            .leftJoinAndSelect('gstrAdditionalNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
            .select('COUNT(DISTINCT gstrAdditionalNoticeOrders.arn)', 'count')
            .where('gstrAdditionalNoticeOrders.organizationId = :orgId', {
            orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere('gstrCredentials.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE });
        if (ViewAssigned && !ViewAll) {
            totalCountQuery.andWhere('clientManagers.id = :userId', { userId });
        }
        if (search) {
            totalCountQuery.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('client.displayName LIKE :search', { search: `%${search}%` });
                qb.orWhere('gstrAdditionalNoticeOrders.gstIn LIKE :search', { search: `%${search}%` });
            }));
        }
        if (type) {
            totalCountQuery.andWhere('gstrAdditionalNoticeOrders.caseTypeName LIKE :typeName', {
                typeName: `%${type}%`,
            });
        }
        if (caseType) {
            totalCountQuery.andWhere('gstrAdditionalNoticeOrders.caseStatus LIKE :caseTypeStatus', {
                caseTypeStatus: caseType,
            });
        }
        if (financialYear) {
            if (financialYear === 'NA') {
                totalCountQuery.andWhere('gstrAdditionalNoticeOrders.fy is null');
            }
            else {
                totalCountQuery.andWhere('gstrAdditionalNoticeOrders.fy = :finy', { finy: financialYear });
            }
        }
        const total = Number((await totalCountQuery.getRawOne()).count);
        const caseIdUniqueRecords = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditionalNoticeOrders')
            .leftJoinAndSelect('gstrAdditionalNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
            .select([
            'gstrAdditionalNoticeOrders.fy',
            'gstrAdditionalNoticeOrders.arn',
            'client.displayName',
            'gstrAdditionalNoticeOrders.gstIn',
            'gstrAdditionalNoticeOrders.caseTypeName',
            'gstrAdditionalNoticeOrders.id',
            'gstrAdditionalNoticeOrders.gstrCredentialsId',
            'gstrAdditionalNoticeOrders.caseStatus',
        ])
            .where('gstrAdditionalNoticeOrders.organizationId = :orgId', {
            orgId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere('gstrCredentials.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE })
            .orderBy('gstrAdditionalNoticeOrders.fy', 'DESC')
            .groupBy('gstrAdditionalNoticeOrders.arn');
        const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                name: 'client.displayName',
                fy: 'gstrAdditionalNoticeOrders.fy',
                type: 'gstrAdditionalNoticeOrders.caseTypeName',
            };
            const column = columnMap[sort.column] || sort.column;
            caseIdUniqueRecords.orderBy(column, sort.direction.toUpperCase());
        }
        if (search) {
            caseIdUniqueRecords.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('client.displayName LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('gstrAdditionalNoticeOrders.gstIn LIKE :search', {
                    search: `%${search}%`,
                });
            }));
        }
        if (ViewAssigned && !ViewAll) {
            caseIdUniqueRecords.andWhere('clientManagers.id = :userId', { userId });
        }
        if (type) {
            caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.caseTypeName LIKE :typeName', {
                typeName: `%${type}%`,
            });
        }
        if (caseType) {
            caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.caseStatus LIKE :caseTypeStatus', {
                caseTypeStatus: caseType,
            });
        }
        if (financialYear) {
            if (financialYear === 'NA') {
                caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.fy is null');
            }
            else {
                caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.fy = :finy', {
                    finy: financialYear,
                });
            }
        }
        if (offset >= 0) {
            caseIdUniqueRecords.skip(offset);
        }
        if (limit) {
            caseIdUniqueRecords.take(limit);
        }
        const results = await caseIdUniqueRecords.getManyAndCount();
        return {
            count: total,
            result: results[0],
        };
    }
    async exportCasebasedOrgNotices(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let caseorders = await this.getCaseIdBasedOrgNotices(userId, exportQuery);
        if (!caseorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Add. Notice & Orders');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Case Type', key: 'caseType' },
            { header: 'Case ID', key: 'caseId' },
            { header: 'Case Status', key: 'caseStatus' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!caseorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        caseorders.result.forEach((caseorder) => {
            const { client: { displayName: clientName }, } = caseorder;
            const rowData = {
                serialNo: serialCounter++,
                fy: caseorder === null || caseorder === void 0 ? void 0 : caseorder.fy,
                clientName: clientName,
                gstIn: caseorder === null || caseorder === void 0 ? void 0 : caseorder.gstIn,
                caseType: caseorder === null || caseorder === void 0 ? void 0 : caseorder.caseTypeName,
                caseId: caseorder === null || caseorder === void 0 ? void 0 : caseorder.arn,
                caseStatus: caseorder === null || caseorder === void 0 ? void 0 : caseorder.caseStatus,
            };
            const row = worksheet.addRow(rowData);
            const caseStatusCell = row.getCell('caseStatus');
            if (rowData.caseStatus === 'OPEN') {
                caseStatusCell.font = {
                    color: { argb: 'FF800000' },
                    bold: true,
                };
                caseStatusCell.value = 'Open';
            }
            else if (rowData.caseStatus === 'CLOSED') {
                caseStatusCell.font = {
                    color: { argb: 'FF008000' },
                    bold: true,
                };
                caseStatusCell.value = 'Closed';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
};
GstrClientService = __decorate([
    (0, common_1.Injectable)()
], GstrClientService);
exports.GstrClientService = GstrClientService;
//# sourceMappingURL=gstr-client.service.js.map