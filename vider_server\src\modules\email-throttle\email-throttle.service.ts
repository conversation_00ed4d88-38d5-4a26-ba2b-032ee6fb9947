import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { EmailQueue } from './email-queue.entity';
import * as nodemailer from 'nodemailer';

// Default SMTP configuration (same as customSendMail.ts)
const defaultsmtp: any = {
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
};

@Injectable()
export class EmailThrottleService {
    private readonly logger = new Logger(EmailThrottleService.name);
    private sending = false;
    private readonly batchSize: number;
    private readonly maxRetries: number;
    private readonly processingDelay: number;


    constructor(
        @InjectRepository(EmailQueue, 'sqliteConnection')
        private readonly queueRepo: Repository<EmailQueue>,
    ) {
        // Configure throttling parameters from environment variables
        this.batchSize = parseInt(process.env.EMAIL_BATCH_SIZE) || 5;
        this.maxRetries = parseInt(process.env.EMAIL_MAX_RETRIES) || 3;
        this.processingDelay = parseInt(process.env.EMAIL_PROCESSING_DELAY) || 50;
    }

    async enqueueEmail(
        to: string,
        subject: string,
        body: any,
        organizationId?: number,
        smtpConfig?: any,
        attachments?: any[]
    ) {
        const email = this.queueRepo.create({
            to,
            subject,
            body,
            organizationId,
            smtpConfig: smtpConfig ? JSON.stringify(smtpConfig) : null,
            attachments: attachments ? JSON.stringify(attachments) : null
        });
        await this.queueRepo.save(email);
        this.logger.log(`Email queued: ${to}`);
    }

    @Cron(CronExpression.EVERY_SECOND)
    async handleEmailQueue() {
        if (this.sending) return;
        this.sending = true;

        try {
            const batch = await this.queueRepo.find({
                order: { createdAt: 'ASC' },
                take: this.batchSize, // Configurable batch size
            });
            console.log(batch, 'batch');
            for (const email of batch) {
                try {
                    await this.sendEmail(email);
                    await this.queueRepo.remove(email);
                    this.logger.log(`Email processed and removed from queue: ${email.to}`);
                } catch (err) {
                    email.retryCount++;
                    this.logger.warn(`Email failed for ${email.to}, retry count: ${email.retryCount}, error: ${err.message}`);

                    if (email.retryCount < this.maxRetries) {
                        await this.queueRepo.save(email);
                        this.logger.log(`Email queued for retry: ${email.to}`);
                    } else {
                        await this.queueRepo.remove(email);
                        this.logger.error(`Email permanently failed after ${this.maxRetries} retries, removed from queue: ${email.to}`);
                    }
                }
            }
        } finally {
            this.sending = false;
        }
    }

    private async sendEmail(email: EmailQueue): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                // Parse SMTP configuration if available
                const smtpConfig = email.smtpConfig ? JSON.parse(email.smtpConfig) : null;
                const attachments = email.attachments ? JSON.parse(email.attachments) : null;

                // Prepare mail options similar to customsendmail
                const mailOptions = {
                    from: smtpConfig?.name
                        ? `"${smtpConfig.name}" <${smtpConfig.auth?.user}>`
                        : smtpConfig?.auth?.user || process.env.FROM_EMAIL || "<EMAIL>",
                    to: email.to,
                    subject: email.subject,
                    html: email.body,
                    attachments: attachments
                };

                // Clean up SMTP config similar to customsendmail
                if (smtpConfig) {
                    delete smtpConfig?.name;
                    if (smtpConfig?.service === "outlook" || smtpConfig?.service === "yahoo") {
                        delete smtpConfig?.service;
                    }
                }

                // Use organization SMTP or default
                const smtp = smtpConfig || defaultsmtp;
                const customTransporter = nodemailer.createTransport(smtp);

                customTransporter.sendMail(mailOptions, (error: any, info: any) => {
                    if (error) {
                        this.logger.error(`Failed to send email to ${email.to}: ${error.message}`);
                        reject(error);
                    } else {
                        this.logger.log(`Email sent successfully to ${email.to}`);
                        resolve();
                    }
                });
            } catch (error) {
                this.logger.error(`Error preparing email for ${email.to}: ${error.message}`);
                reject(error);
            }
        });
    }

    async getQueueStatus() {
        const totalEmails = await this.queueRepo.count();
        const pendingEmails = await this.queueRepo.count({ where: { retryCount: 0 } });
        const retryingEmails = await this.queueRepo.count({
            where: { retryCount: 1 }
        });
        const failingEmails = await this.queueRepo.count({
            where: { retryCount: this.maxRetries - 1 }
        });

        return {
            totalEmails,
            pendingEmails,
            retryingEmails,
            failingEmails,
            isProcessing: this.sending
        };
    }

    async getFailedEmails() {
        return await this.queueRepo.find({
            where: { retryCount: this.maxRetries - 1 },
            order: { createdAt: 'DESC' },
            take: 50
        });
    }

    async getConfiguration() {
        return {
            batchSize: this.batchSize,
            maxRetries: this.maxRetries,
            processingDelay: this.processingDelay,
            cronExpression: 'EVERY_SECOND',
            description: 'Email throttling service configuration'
        };
    }
}
