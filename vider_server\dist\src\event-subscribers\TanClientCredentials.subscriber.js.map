{"version": 3, "file": "TanClientCredentials.subscriber.js", "sourceRoot": "", "sources": ["../../../src/event-subscribers/TanClientCredentials.subscriber.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAMiB;AAEjB,gIAAkH;AAClH,kHAAmG;AACnG,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B,IAAI,gBAAsC,CAAC;AAEpC,IAAM,8BAA8B,GAApC,MAAM,8BAA8B;IAGzC,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QACjD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,QAAQ;QACN,OAAO,uCAAoB,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAwC;QACzD,gBAAgB,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,cAAc,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAwC;QACxD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;YACxC,MAAM,uBAAuB,GAAG,MAAM,yCAAuB,CAAC,kBAAkB,CAAC,SAAS,CAAC;iBACxF,KAAK,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,CAAC;iBACnE,QAAQ,CAAC,0DAA0D,CAAC;iBACpE,MAAM,EAAE,CAAC;YACZ,IAAI,uBAAuB,EAAE;gBAC3B,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,MAAM,GAAG;oBACX,MAAM,EAAE,KAAK;oBACb,aAAa,EAAE,QAAQ;oBACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,2CAA2C,cAAc,MAAM;oBAC9F,OAAO,EAAE,EAAE;oBACX,IAAI,EAAE,IAAI;iBACX,CAAC;gBAEF,KAAK;qBACF,OAAO,CAAC,MAAM,CAAC;qBACf,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;;oBACjB,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,EAAE;wBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,QAAQ,CAAC,CAAC;wBACtD,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;4BACzB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;4BACzB,KAAK,EAAE,cAAc;4BACrB,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,QAAQ;yBACpB,CAAC,CAAC;wBAEH,IAAI,OAAO,GAAG;4BACZ,MAAM,EAAE,MAAM;4BACd,aAAa,EAAE,QAAQ;4BACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,yCAAyC;4BACxE,OAAO,EAAE;gCACP,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;gCACjC,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,KAAK;yBACZ,CAAC;wBAEF,KAAK;6BACF,OAAO,CAAC,OAAO,CAAC;6BAChB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;wBACnB,CAAC,CAAC;6BACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;4BACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;wBAC/E,CAAC,CAAC,CAAC;qBACN;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC/E,CAAC,CAAC,CAAC;aACN;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;SACrE;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAwC;QACxD,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;YAChD,IAAI,gBAAgB,CAAC,MAAM,KAAK,MAAM,EAAE;gBACtC,MAAM,uBAAuB,GAAG,MAAM,yCAAuB,CAAC,kBAAkB,CAAC,SAAS,CAAC;qBACxF,KAAK,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,CAAC;qBACnE,QAAQ,CAAC,0DAA0D,CAAC;qBACpE,MAAM,EAAE,CAAC;gBACZ,IAAI,uBAAuB,EAAE;oBAC3B,IAAI,IAAI,GAAG,EAAE,CAAC;oBACd,IAAI,MAAM,GAAG;wBACX,MAAM,EAAE,KAAK;wBACb,aAAa,EAAE,QAAQ;wBACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,2CAA2C,cAAc,MAAM;wBAC9F,OAAO,EAAE,EAAE;wBACX,IAAI,EAAE,IAAI;qBACX,CAAC;oBAEF,KAAK;yBACF,OAAO,CAAC,MAAM,CAAC;yBACf,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;;wBACjB,IAAI,QAAQ,CAAC,IAAI,EAAE;4BACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,QAAQ,CAAC,CAAC;4BACtD,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;gCACzB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;gCACzB,KAAK,EAAE,cAAc;gCACrB,IAAI,EAAE,KAAK;gCACX,SAAS,EAAE,QAAQ;6BACpB,CAAC,CAAC;4BAEH,IAAI,OAAO,GAAG;gCACZ,MAAM,EAAE,MAAM;gCACd,aAAa,EAAE,QAAQ;gCACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,yCAAyC;gCACxE,OAAO,EAAE;oCACP,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;oCACjC,cAAc,EAAE,kBAAkB;iCACnC;gCACD,IAAI,EAAE,KAAK;6BACZ,CAAC;4BAEF,KAAK;iCACF,OAAO,CAAC,OAAO,CAAC;iCAChB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;4BACnB,CAAC,CAAC;iCACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gCACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;4BAC/E,CAAC,CAAC,CAAC;yBACN;oBACH,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBACf,OAAO,CAAC,KAAK,CACX,+DAA+D,EAC/D,KAAK,CAAC,OAAO,CACd,CAAC;oBACJ,CAAC,CAAC,CAAC;iBACN;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;SACrE;IACH,CAAC;CACF,CAAA;AAvIY,8BAA8B;IAD1C,IAAA,yBAAe,GAAE;qCAIyB,oBAAU;GAHxC,8BAA8B,CAuI1C;AAvIY,wEAA8B"}