import * as nodemailer from 'nodemailer';

let defaultsmtp: any = {
  host: 'email-smtp.ap-south-1.amazonaws.com',
  port: 587,
  auth: {
    user: 'AKIA5GHOVJDTRJ3PAQ6E',
    pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
  },
};

export async function customsendmail({ to, org, data, }: any) {
  return new Promise(async (resolve, reject) => {
    const mailOptions = {
      from: org?.smtp ? org?.smtp?.name ? `"${org?.smtp?.name}" <${org?.smtp?.auth?.user}>` : org?.smtp?.auth?.user : "<EMAIL>",
      to: to,
      subject: data.subject,
      html: data.html,
      attachments: data.attachments
    };

    if (org?.smtp) {
      delete org?.smtp?.name;
    }

    if (org?.smtp?.service === "outlook" || org?.smtp?.service === "yahoo") {
      delete org?.smtp?.service;
    }

    const smtp = org?.smtp ? org?.smtp : defaultsmtp;
    const customtransporter = nodemailer.createTransport(smtp);
    await this.emailThrottleService.enqueueEmail(mailOptions.to, mailOptions.subject, mailOptions.html, organization.id);

    // customtransporter.sendMail(mailOptions, function (error: any, info: any) {
    //   if (error) {
    //     console.log(error.message);
    //     reject(error.message);
    //   } else {
    //     // console.log(info.response);
    //     resolve(info.response);
    //   }
    // });
  });
}