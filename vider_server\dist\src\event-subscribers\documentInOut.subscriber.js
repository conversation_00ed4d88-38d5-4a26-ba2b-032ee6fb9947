"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentInOutSubscriber = void 0;
const typeorm_1 = require("typeorm");
const document_in_out_entity_1 = require("../modules/document-in-out/entity/document-in-out.entity");
const moment = require("moment");
let DocumentInOutSubscriber = class DocumentInOutSubscriber {
    constructor(connection) {
        this.connection = connection;
        this.beforePriority = '';
        this.beforeStatus = '';
        connection.subscribers.push(this);
    }
    listenTo() {
        return document_in_out_entity_1.default;
    }
    async beforeInsert(event) {
        const yearLastTwoDigits = moment().format('YY');
        const { documentType, useType, organization } = event.entity;
        const string = yearLastTwoDigits + (documentType === "in" ? "DI" : "DO") + (useType === "kyb" ? "K" : useType === "task" ? "T" : "G");
        const count = await (0, typeorm_1.createQueryBuilder)(document_in_out_entity_1.default, 'documentInOut')
            .leftJoin('documentInOut.organization', 'organization')
            .where('organization.id = :organizationId', { organizationId: organization.id })
            .andWhere("documentInOut.documentId LIKE :documentId", { documentId: `${string}%` })
            .getCount();
        function generateDOCId(id) {
            if (id < 10000) {
                return string + id.toString().padStart(4, '0');
            }
            return string + id;
        }
        event.entity.documentId = generateDOCId(count + 1);
    }
    async afterInsert(event) {
    }
};
DocumentInOutSubscriber = __decorate([
    (0, typeorm_1.EventSubscriber)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], DocumentInOutSubscriber);
exports.DocumentInOutSubscriber = DocumentInOutSubscriber;
//# sourceMappingURL=documentInOut.subscriber.js.map