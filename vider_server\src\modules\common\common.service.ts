import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import {
  Between,
  Brackets,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  get<PERSON>anager,
  In,
  LessThan<PERSON>rEqual,
  Not,
} from 'typeorm';
import { Notification } from 'src/notifications/notification.entity';
import NotificationPreferences from '../notification-settings/notifications-preferences.entity';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import {
  sendDocumentInvoiceData,
  sendDocumentReceiptData,
  sendDocumentTextMessage,
  sendWhatsAppTemplateMessage,
  sendWhatsAppTemplateMessageUS,
  sendWhatsAppTextMessage,
} from '../whatsapp/whatsapp.service';
import { getAdminIDsBasedOnOrganizationId, getUserDetails } from 'src/utils/re-use';
import Event from '../events/event.entity';
import { Organization } from '../organization/entities/organization.entity';
import Task from '../tasks/entity/task.entity';
import DscRegister from '../dsc-register/entity/dsc-register.entity';
import * as ejs from 'ejs';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import { User, UserStatus, UserType } from '../users/entities/user.entity';
import puppeteer from 'puppeteer';
import * as xlsx from 'xlsx';
import Client from '../clients/entity/client.entity';
import Receipt, { ReceiptStatus, ReceiptType } from '../billing/entitities/receipt.entity';
import { formatDate, getTitle } from 'src/utils';
import CronActivity from '../cron-activity/cron-activity.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { sendnewMail } from 'src/emails/newemails';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import {
  clientgroupinvoicebilled,
  clientgroupinvoicebilling,
  clientgroupinvoicereceipts,
  clientGroupinvoiceunbilled,
  clientsgrouplistinvoice,
} from 'src/utils/sqlqueries';
import TaskStatus from '../tasks/entity/task-status.entity';
import { Invoice, InvoiceStatus } from '../billing/entitities/invoice.entity';
import { ProformaInvoice } from '../billing/entitities/proforma-invoice.entity';
import GstrNoticeOrders from '../gstr-automation/entity/noticeOrders.entity';
import GstrAdditionalNoticeOrders from '../gstr-automation/entity/gstrAdditionalOrdersAndNotices.entity';
import AutFyaNotice from '../automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutFyiNotice from '../automation/entities/aut_income_tax_eproceedings_fyi_notice.entity';
import Expenditure from '../expenditure/expenditure.entity';
import ViderWhatsappSessions from '../whatsapp/entity/viderWhatsappSessions';
import * as AWS from 'aws-sdk';
import LogHour from '../log-hours/log-hour.entity';
import Attendance from '../attendance/attendance.entity';
import AutomationMachines, { TypeEnum } from '../automation/entities/automation_machines.entity';
import GstrCredentials, { GstrStatus } from '../gstr-automation/entity/gstrCredentials.entity';
import AutClientCredentials, {
  IncomeTaxStatus,
} from '../automation/entities/aut_client_credentials.entity';
import { TaskRecurringStatus, TaskStatusEnum } from '../tasks/dto/types';
import { CommentsController } from '../tasks/controllers/comments.controller';
import { EmailThrottleService } from '../email-throttle/email-throttle.service';

let transporter: any = nodemailer.createTransport({
  host: 'email-smtp.ap-south-1.amazonaws.com',
  port: 587,
  auth: {
    user: 'AKIA5GHOVJDTRJ3PAQ6E',
    pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
  },
});
const s3 = new AWS.S3();

@Injectable()
export class CommonService {

  constructor(
    public emailThrottleService: EmailThrottleService,
  ) { }

  async getclientinvoicebilled(clientId, payload: any) {
    let sql = `SELECT 
    t.id, 
    t.name, 
    t.task_number AS tasknumber, 
    t.status AS status, 
    DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
    DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
    SUM(i.grand_total) AS amount,
    (SELECT COUNT(t2.id) 
     FROM task t2 
     LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
     WHERE t2.client_id = '${payload.clientId}'
         AND t2.status != 'deleted' 
         AND t2.status != 'terminated' 
         AND t2.payment_status = 'BILLED'
         AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
         AND t2.parent_task_id IS NULL
    ) AS total_count
  FROM task t 
  LEFT JOIN invoice i ON i.id = t.invoice_id
  WHERE t.client_id = '${payload.clientId}'
    AND t.status != 'deleted' 
    AND t.status != 'terminated' 
    AND t.payment_status = 'BILLED'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
    AND t.parent_task_id IS NULL `;

    sql += ` group by t.id having t.id is not null 
    limit ${payload?.offset || 0}, ${payload?.limit || 1000}`;
    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  async exportClientBilledTasks(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let tasks = await this.getclientinvoicebilled(clientId, newQuery);

    if (!tasks?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Billed Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Task ID', key: 'tasknumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Task Status', key: 'status' },
      { header: 'Invoice Date', key: 'invoice_date' },
      { header: 'Due Date', key: 'invoice_due_date' },
      { header: 'Invoice Value (₹)', key: 'amount' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks?.result?.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++,
        tasknumber: task?.tasknumber,
        name: task?.name,
        status: getTitle(task?.status),
        // 'Invoice Date': formatDate(task?.invoice_date),
        // 'Due Date': formatDate(task?.invoice_due_date),
        invoice_date: task?.invoice_date,
        invoice_due_date: task?.invoice_due_date,
        amount: 1 * task?.amount,
      };

      const row = worksheet.addRow(rowData);
      // Access the 'status' column cell
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'todo':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'on hold':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'under review':
            statusCell.font = { color: { argb: '653BBA' }, bold: true };
            break;
          case 'completed':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientGroupBilledTasks(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let sql = await clientgroupinvoicebilled(payload);
    let result = await getManager().query(sql);

    if (!result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Billed Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Task ID', key: 'tasknumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Task Status', key: 'status' },
      { header: 'Pure Agent ₹', key: 'pureagent' },
      { header: 'Additional Amount ₹', key: 'additional' },
      { header: 'Service Fee ₹', key: 'serviceFee' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    result?.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++,
        tasknumber: task?.tasknumber,
        name: task?.name,
        status: getTitle(task?.status),
        pureagent: 1 * task?.pureagent || 0,
        additional: 1 * task?.additional || 0,
        serviceFee: 1 * task?.fee_amount || 0,
      };

      const row = worksheet.addRow(rowData);
      // Access the 'status' column cell
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'todo':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'on hold':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'under review':
            statusCell.font = { color: { argb: '653BBA' }, bold: true };
            break;
          case 'completed':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getclientinvoiceexport(clientId, payload: any) {
    let sql = `select * from invoice where client_id = ${payload.clientId}
    AND invoice_number LIKE '%${payload.search}%'; `;

    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  async exportClientInvoiceReport(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let invoices = await this.getclientinvoiceexport(clientId, newQuery);

    if (!invoices?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Invoice');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Invoice Number', key: 'invoiceNumber' },
      { header: 'Amount', key: 'amount' },
      { header: 'Status', key: 'status' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    invoices?.result?.forEach((invoice) => {
      const rowData = {
        serialNo: serialCounter++,
        invoiceDate: formatDate(invoice?.created_at),
        invoiceNumber: invoice?.invoice_number,
        amount: 1 * invoice?.grand_total,
        status: invoice?.status == 'APPROVAL_PENDING' ? 'INVOICED' : getTitle(invoice?.status),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientGroupInvoiceReport(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let sql = await clientgroupinvoicebilling(payload);
    let result = await getManager().query(sql, newQuery);

    if (!result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Invoice');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Invoice Number', key: 'invoiceNumber' },
      { header: 'Amount', key: 'amount' },
      { header: 'Status', key: 'status' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    result?.forEach((invoice) => {
      const rowData = {
        serialNo: serialCounter++,
        invoiceDate: formatDate(invoice?.created_at),
        invoiceNumber: invoice?.invoice_number,
        amount: 1 * invoice?.grand_total,
        status: invoice?.status == 'APPROVAL_PENDING' ? 'INVOICED' : getTitle(invoice?.status),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getclientinvoiceUnbilled(clientId, payload: any) {
    let sql = `SELECT t.id,t.task_number as tasknumber,t.name, t.status, t.fee_amount, SUM(e.amount) AS total_expenditure,
    SUM( CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END) AS pureagent,
    SUM( CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END) AS additional,
    (
        SELECT COUNT(t2.id) 
        FROM task t2 
        LEFT JOIN expenditure e2 ON e2.task_id = t2.id 
        WHERE t2.client_id = '${payload.clientId}'
        AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%')
        AND (t2.recurring_status != 'pending' OR t2.recurring_status IS NULL)
        AND t2.status != 'deleted' AND t2.status != 'terminated'
        AND t2.payment_status = 'UNBILLED'
        AND t2.billable is true
        AND t2.parent_task_id IS NULL
    ) AS total_count
    FROM task t 
    LEFT JOIN expenditure e ON e.task_id = t.id 
    WHERE t.client_id = '${payload.clientId}'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%')
    AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL)
    AND t.status != 'deleted' and t.status !='terminated'
    AND t.billable is true
    AND t.payment_status = 'UNBILLED'
    AND t.billable is true
    AND t.parent_task_id IS NULL
    GROUP BY t.id, t.name, t.status, t.fee_amount`;

    // sql += ` group by t.id having t.id is not null
    // limit ${payload?.offset || 0}, ${payload?.limit || 1000}`;
    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  async exportUnClientBilledTasks(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let tasks = await this.getclientinvoiceUnbilled(clientId, newQuery);

    if (!tasks?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Un-Billed Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Task ID', key: 'tasknumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Task Status', key: 'status' },
      { header: 'Pure Agent ₹', key: 'pureagent' },
      { header: 'Additional Amount ₹', key: 'additional' },
      { header: 'Service Fee ₹', key: 'serviceFee' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks?.result?.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++,
        tasknumber: task?.tasknumber,
        name: task?.name,
        status: getTitle(task?.status),
        pureagent: 1 * task?.pureagent || 0,
        additional: 1 * task?.additional || 0,
        serviceFee: 1 * task?.fee_amount || 0,
      };

      const row = worksheet.addRow(rowData);
      // Access the 'status' column cell
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'todo':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'on hold':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'under review':
            statusCell.font = { color: { argb: '653BBA' }, bold: true };
            break;
          case 'completed':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportUnClientGroupUnBilledTasks(clientId: any, payload: any) {
    let sql = await clientGroupinvoiceunbilled(payload);
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let result = await getManager().query(sql, newQuery);

    if (!result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Un-Billed Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Task ID', key: 'tasknumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Task Status', key: 'status' },
      { header: 'Pure Agent ₹', key: 'pureagent' },
      { header: 'Additional Amount ₹', key: 'additional' },
      { header: 'Service Fee ₹', key: 'serviceFee' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    result?.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++,
        tasknumber: task?.tasknumber,
        name: task?.name,
        status: getTitle(task?.status),
        pureagent: 1 * task?.pureagent || 0,
        additional: 1 * task?.additional || 0,
        serviceFee: 1 * task?.fee_amount || 0,
      };

      const row = worksheet.addRow(rowData);
      // Access the 'status' column cell
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'todo':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'on hold':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'under review':
            statusCell.font = { color: { argb: '653BBA' }, bold: true };
            break;
          case 'completed':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getclientinvoiceOverView(orgId, payload: any, ViewAll, ViewAssigned, user) {
    let sql = `SELECT 
    c.id,
    c.display_name,
    c.category,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.billable=true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable=true THEN t.id END) AS billedtasks,
    CASE 
    WHEN ABS(
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${ReceiptStatus.CREATED}' 
                      AND r.type = '${ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    ) < 0.0000001 THEN 0
    ELSE (
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${ReceiptStatus.CREATED}' 
                      AND r.type = '${ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    )
END AS dueamount,
IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ) as tdsAmount,


    (
        SELECT COUNT(DISTINCT c2.id)
        FROM client c2
        LEFT JOIN task t2 ON c2.id = t2.client_id 
        AND (t2.recurring_status != 'pending' OR t2.recurring_status IS NULL) 
        AND t2.status != 'terminated' 
        AND t2.status != 'deleted' 
        AND t2.parent_task_id IS NULL
        LEFT JOIN client_client_managers_user ccmu ON c2.id = ccmu.client_id
        LEFT JOIN user u ON ccmu.user_id = u.id
        WHERE c2.organization_id = '${payload.organizationid}' 
        AND c2.status != 'deleted' 
        AND c2.display_name LIKE '%${payload.search}%'
        ${ViewAssigned && !ViewAll ? `AND u.id = ${user.id}` : ' '}
    ) AS total_count
FROM 
    client c 
    LEFT JOIN task t ON c.id = t.client_id 
    AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL) 
    AND t.status != 'terminated' 
    AND t.status != 'deleted' 
    AND t.parent_task_id IS NULL
    LEFT JOIN client_client_managers_user ON c.id = client_client_managers_user.client_id
    LEFT JOIN user ON client_client_managers_user.user_id = user.id
WHERE 
    c.organization_id = '${payload.organizationid}' 
     AND (t.billable is true OR t.id is null OR t.billable is false)
    AND c.status != 'deleted' 
    ${ViewAssigned && !ViewAll ? `AND user.id = ${user.id}` : ' '}
AND c.display_name LIKE '%${payload.search}%' group by c.id;`;

    // sql += ` group by t.id having t.id is not null
    // limit ${payload?.offset || 0}, ${payload?.limit || 1000}`;
    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  async exportClientOverviewlist(orgId: any, payload: any, ViewAll, ViewAssigned, user) {
    const newQuery = { ...payload, offset: 0, limit: 100000 };
    let clients = await this.getclientinvoiceOverView(orgId, newQuery, ViewAll, ViewAssigned, user);

    if (!clients?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Client Billing');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Category', key: 'clientCategory' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Billable Task', key: 'billableTask' },
      { header: 'Unbilled Task', key: 'unbilledTask' },
      { header: 'Billed Tasks', key: 'billedTasks' },
      { header: 'Amount Due', key: 'amountDue' },
      { header: 'TDS Amount', key: 'tdsAmount' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients?.result?.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        clientCategory: getTitle(client.category),
        clientName: client?.display_name,
        billableTask: 1 * client?.billabletasks,
        unbilledTask: 1 * client?.unbilledtasks,
        billedTasks: 1 * client?.billedtasks,
        amountDue: 1 * client?.dueamount,
        tdsAmount: 1 * client?.tdsAmount,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientGroupOverviewlist(orgId: any, payload: any, user, ViewAll, ViewAssigned) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let sql = await clientsgrouplistinvoice(payload, user, ViewAll, ViewAssigned);
    let result = await getManager().query(sql, newQuery);

    if (!result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Group Billing');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Group', key: 'clientName' },
      { header: 'Billable Task', key: 'billableTask' },
      { header: 'Unbilled Task', key: 'unbilledTask' },
      { header: 'Billed Tasks', key: 'billedTasks' },
      { header: 'Amount Due', key: 'amountDue' },
      { header: 'TDS Amount', key: 'tdsAmount' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    result?.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        // clientCategory:getTitle(client.category),
        clientName: client?.display_name,
        billableTask: 1 * client?.billabletasks,
        unbilledTask: 1 * client?.unbilledtasks,
        billedTasks: 1 * client?.billedtasks,
        amountDue: 1 * client?.dueamount,
        tdsAmount: 1 * client?.tdsAmount,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getClientReceiptsReport(clientId, payload: any) {
    let sql = `select receipt_number,
    status,
    DATE_FORMAT(receipt_date, '%d-%m-%y')receipt_date,
    payment_mode,amount,
    DATE_FORMAT(r.created_at, '%d-%m-%y') created_at,
    (
      SELECT COUNT(*)
      FROM receipt r
      WHERE r.client_id = '${payload.clientId}'
      AND r.receipt_number LIKE '%${payload.search}%'
  ) AS total_count,
  r.type,
  be.trade_name
  from receipt r LEFT JOIN billing_entity be ON r.billing_entity_id = be.id
  where client_id='${payload.clientId}'
    AND receipt_number LIKE '%${payload.search}%'
    order by r.created_at desc;`;
    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  async exportClientReceipts(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let receipts = await this.getClientReceiptsReport(clientId, newQuery);

    if (!receipts?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Receipts');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Receipt Type', key: 'type' },
      { header: 'Receipt #', key: 'receipt_number' },
      { header: 'Receipt Date', key: 'created_at' },
      { header: 'Billing Entity', key: 'trade_name' },
      { header: 'Amount Received (₹)', key: 'amount' },
      { header: 'Status', key: 'status' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    receipts?.result?.forEach((receipt) => {
      const rowData = {
        serialNo: serialCounter++,
        type: receipt?.type,
        receipt_number: receipt?.receipt_number,
        created_at: receipt?.created_at,
        trade_name: receipt?.trade_name,
        amount: 1 * receipt?.amount,
        status: getTitle(receipt?.status),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'trade_name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientGroupReceipts(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let sql = await clientgroupinvoicereceipts(payload);
    let result = await getManager().query(sql, newQuery);

    if (!result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Receipts');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Receipt Type', key: 'type' },
      { header: 'Receipt #', key: 'receipt_number' },
      { header: 'Receipt Date', key: 'created_at' },
      { header: 'Billing Entity', key: 'trade_name' },
      { header: 'Amount Received (₹)', key: 'amount' },
      { header: 'Status', key: 'status' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    result?.forEach((receipt) => {
      const rowData = {
        serialNo: serialCounter++,
        type: receipt?.type,
        receipt_number: receipt?.receipt_number,
        created_at: receipt?.created_at,
        trade_name: receipt?.trade_name,
        amount: 1 * receipt?.amount,
        status: getTitle(receipt?.status),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'trade_name') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async upload(file: Express.Multer.File) {
    try {
      const { originalname } = file;
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const upload = await this.uploadS3(file.buffer, bucketS3, originalname, file.mimetype);
      return upload;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async uploadInvoice(data) {
    try {
      const { invoiceData, invoiceBuffer } = data;
      const {
        organization,
        client,
        invoiceNumber,
        invoiceDueDate,
        grandTotal,
        id,
        invoiceDate,
        clientGroup,
        userId,
      } = invoiceData;
      const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
      const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const uploadData: any = await this.uploadS3(
        Buffer.from(invoiceBuffer),
        bucketS3,
        `invoice/${id}.pdf`,
        'application/pdf',
      );
      const { Location } = uploadData;
      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;
        const clientName =
          client?.displayName?.length > 0
            ? client?.displayName
            : client?.fullName || clientGroup?.displayName;
        const title = 'Invoice Created';
        if (invoiceData.whatsappCheck === true) {
          const whatsappOptions = {
            title: 'invoice-client-template',
            userId: userId,
            orgId: organization?.id,
            // to: `91${client?.mobileNumber}`,
            to: fullMobileNumberWithCountry(
              client?.mobileNumber || clientGroup?.mobileNumber,
              client?.countryCode || clientGroup?.countryCode,
            ),
            name: 'invoice_client',
            header: [
              {
                type: 'document',
                link: Location,
              },
            ],

            body: [clientName, invoiceNumber, invoiceDateFormat, grandTotal, invoiceDueDateFormat],

            fileName: `Invoice-${invoiceNumber}`,
            key: 'INVOICE_CREATION_WHATSAPP',
          };
          await sendWhatsAppTemplateMessageUS(whatsappOptions);
          // sendDocumentInvoiceData;
        }
        if (invoiceData.emailCheck === true) {
          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;
          const keyy = 'INVOICE_CREATION_MAIL';
          if (clientPreferences && clientPreferences[keyy]) {
            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';

            const address = addressParts.join(', ') + pincode;

            const mailOptions = {
              id: userId,
              key: 'INVOICE_CREATION_MAIL',
              email: client?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                serviceName: '',
                legalName: organization?.tradeName || organization?.legalName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDateFormat: invoiceDueDateFormat,
                invoiceTotalAmount: grandTotal,
                clientName: clientName,
                address: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                userId: userId,
              },

              filePath: 'client-invoice-created',
              subject: 'Invoice for Services Rendered',
              invoiceId: id,
            };
            await sendnewMail(mailOptions);
          }
        }


        const whatsappOptions = {
          to: `91${mobileNumber}`,
          name: 'invoice',
          header: [
            {
              type: 'document',
              link: Location,
            },
          ],
          body: [fullName, invoiceNumber, clientName, invoiceDueDate, grandTotal],
          fileName: `Invoice-${invoiceNumber}`,
        };
        const key = 'INVOICES_CREATED_WHATSAPP';
        // await sendWhatsAppTemplateMessage(whatsappOptions);
        const whatsappMessageBody = `
Hi *${fullName}*
A new Invoice has been generated in ATOM:
*Invoice number:* ${invoiceNumber}
*Client name:* ${clientName}
*Due date:* ${invoiceDueDate}
*Total amount:* ${grandTotal}
        
 We hope this helps!
        `;
      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async uploadReceipt(data) {
    try {
      const { receiptData, receiptBuffer } = data;
      const {
        organization,
        client,
        receiptNumber,
        receiptDate,
        amount,
        id,
        clientGroup,
        emailCheck,
        paymentMode,
        address,
        logInUser,
      } = receiptData;

      const receiptDateFormat = moment(receiptDate).format("DD-MM-YYYY");
      const bucketS3 = process.env.AWS_BUCKET_NAME;

      // ✅ Upload receipt PDF to S3
      const uploadData: any = await this.uploadS3(
        Buffer.from(receiptBuffer),
        bucketS3,
        `receipt/${id}.pdf`,
        "application/pdf"
      );
      const { Location } = uploadData;

      console.log("organization", organization?.id);

      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      console.log("admins", admins);

      const clientName =
        client?.displayName?.length > 0
          ? client?.displayName
          : client?.fullName || clientGroup?.displayName;

      // ✅ WhatsApp notifications
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;

        if (receiptData.whatsappCheck === true) {
          const whatsappOptions = {
            title: "receipt-client-template",
            userId: userId,
            orgId: organization?.id,
            to: fullMobileNumberWithCountry(
              client?.mobileNumber || clientGroup?.mobileNumber,
              client?.countryCode || clientGroup?.countryCode
            ),
            name: "receipt_client",
            header: [
              {
                type: "document",
                link: Location,
              },
            ],
            body: [fullName, clientName, receiptNumber, receiptDateFormat, amount],
            fileName: `Receipt-${receiptNumber}`,
            key: "RECEIPT_CREATION_WHATSAPP",
          };

          await sendWhatsAppTemplateMessageUS(whatsappOptions);

          const key = "RECEIPT_CREATED_WHATSAPP";
          const whatsappMessageBody = `
Hi *${fullName}*
A new receipt has been generated in ATOM:
*Receipt number:* ${receiptNumber}
*Client name:* ${clientName}
*Receipt date:* ${receiptDate}
*Total amount:* ${amount}
        
We hope this helps!
        `;
          const whatsappTitle = "Receipt Created";

          // Optional text/document messages
          // await sendWhatsAppTextMessage(...);
          // await sendDocumentReceiptData(...);
        }
      }

      // ✅ Email notification (inline afterInsert logic)
      try {
        const key = "RECEIPT_CREATION_MAIL";
        if (emailCheck && key === "RECEIPT_CREATION_MAIL" && client) {
          const mailOptions = {
            id: logInUser,
            key: "RECEIPT_CREATION_MAIL",
            email: client?.email,
            clientMail: "ORGANIZATION_CLIENT_EMAIL",
            data: {
              legalName: organization?.tradeName || organization?.legalName,
              receiptDate: receiptDate,
              Amountreceived: amount,
              clientName: clientName,
              address: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              paymentMode: paymentMode,
              userId: receiptData?.userId,
            },
            filePath: "client-receipt-created",
            subject: "Confirmation of Payment Received for Service Rendered",
          };

          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization?.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;

          if (clientPreferences && clientPreferences[key]) {
            await sendnewMail(mailOptions);
          }
        }
      } catch (mailErr) {
        console.error("Failed to send receipt creation email:", mailErr);
      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async uploadReceiptForEdit(data) {
    try {
      const { receiptData, receiptBuffer } = data;
      const {
        organization,
        client,
        receiptNumber,
        receiptDate,
        amount,
        id,
        clientGroup,
        emailCheck,
        paymentMode,
        address,
        logInUser,
      } = receiptData;

      const receiptDateFormat = moment(receiptDate).format("DD-MM-YYYY");
      const bucketS3 = process.env.AWS_BUCKET_NAME;

      // ✅ Upload receipt PDF to S3
      const uploadData: any = await this.uploadS3(
        Buffer.from(receiptBuffer),
        bucketS3,
        `Receipt_${id}.pdf`,
        // `Invoice_${id}.pdf`,
        "application/pdf"
      );
      const { Location } = uploadData;

      console.log("organization", organization?.id);

      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      console.log("admins", admins);

      const clientName =
        client?.displayName?.length > 0
          ? client?.displayName
          : client?.fullName || clientGroup?.displayName;

      // ✅ WhatsApp notifications
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;

        if (receiptData.whatsappCheck === true) {
          const whatsappOptions = {
            title: "receipt-client-template",
            userId: userId,
            orgId: organization?.id,
            to: fullMobileNumberWithCountry(
              client?.mobileNumber || clientGroup?.mobileNumber,
              client?.countryCode || clientGroup?.countryCode
            ),
            name: "receipt_updated_client",
            header: [
              {
                type: "document",
                link: Location,
              },
            ],
            // body: [fullName, clientName, receiptNumber, receiptDateFormat, amount],
            fileName: `Receipt-${receiptNumber}`,
            key: "RECEIPT_EDITED_WHATSAPP",
          };

          await sendWhatsAppTemplateMessageUS(whatsappOptions);

          const key = "RECEIPT_EDITED_WHATSAPP";
          const whatsappMessageBody = `
Hi *${fullName}*
A new receipt has been generated in ATOM:
*Receipt number:* ${receiptNumber}
*Client name:* ${clientName}
*Receipt date:* ${receiptDate}
*Total amount:* ${amount}
        
We hope this helps!
        `;
          const whatsappTitle = "Receipt Created";

          // Optional text/document messages
          // await sendWhatsAppTextMessage(...);
          // await sendDocumentReceiptData(...);
        }
      }

      // ✅ Email notification (inline afterInsert logic)
      try {
        const key = "RECEIPT_EDITED_MAIL";
        if (emailCheck && key === "RECEIPT_EDITED_MAIL" && client) {
          const mailOptions = {
            id: logInUser,
            key: "RECEIPT_EDITED_MAIL",
            email: client?.email,
            clientMail: "ORGANIZATION_CLIENT_EMAIL",
            data: {
              legalName: organization?.tradeName || organization?.legalName,
              receiptDate: receiptDate,
              Amountreceived: amount,
              clientName: clientName,
              address: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              paymentMode: paymentMode,
              userId: receiptData?.userId,
            },
            filePath: "client-receipt-edited",
            subject: "Confirmation of Updated Payment Received for Service Rendered",
          };

          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization?.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;

          if (clientPreferences && clientPreferences[key]) {
            await sendnewMail(mailOptions);
          }
        }
      } catch (mailErr) {
        console.error("Failed to send receipt creation email:", mailErr);
      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }


  async uploadProformaInvoice(data) {
    try {
      const { invoiceData, invoiceBuffer } = data;
      const {
        organization,
        client,
        invoiceNumber,
        invoiceDueDate,
        grandTotal,
        id,
        invoiceDate,
        clientGroup,
      } = invoiceData;
      const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
      const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const uploadData: any = await this.uploadS3(
        Buffer.from(invoiceBuffer),
        bucketS3,
        `invoice/${id}.pdf`,
        'application/pdf',
      );
      const { Location } = uploadData;
      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;
        const clientName =
          client?.displayName.length > 0
            ? client?.displayName
            : client?.fullName || clientGroup?.displayName;
        const clientNumber = client?.mobileNumber || clientGroup?.mobileNumber;
        const title = 'Invoice Proforma Created';
        if (invoiceData.whatsappCheck === true) {

          const whatsappOptions = {
            title: 'proforma-invoice-client-template',
            userId: userId,
            orgId: organization?.id,
            to: `91${clientNumber}`,
            name: 'invoiceproforma',
            header: [
              {
                type: 'document',
                link: Location,
              },
            ],

            body: [clientName, invoiceNumber, invoiceDateFormat, grandTotal, invoiceDueDateFormat],

            fileName: `Proforma-Invoice-${invoiceNumber}`,
            key: 'INVOICE_PROFORMA_CREATION_WHATSAPP',
          };
          await sendWhatsAppTemplateMessageUS(whatsappOptions);
        }
        if (invoiceData.emailCheck === true) {

          const keyy = 'INVOICE_PROFORMA_CREATION_MAIL';
          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;
          if (clientPreferences?.['INVOICE_PROFORMA_CREATION_MAIL']) {
            const address = `${organization.buildingNo || ' ' ? organization.buildingNo || ' ' + ', ' : ''
              }${organization.floorNumber || ' ' ? organization.floorNumber || ' ' + ', ' : ''}${organization.buildingName || ' ' ? organization.buildingName + ', ' : ''
              }${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''
              }${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''
              }${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''
              }`;

            const mailOptions = {
              id: userId,
              key: 'INVOICE_PROFORMA_CREATION_MAIL',
              email: client?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                serviceName: '',
                legalName: organization?.tradeName || organization?.legalName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDateFormat: invoiceDueDateFormat,
                invoiceTotalAmount: grandTotal,
                clientName: clientName,
                address: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                userId: userId,
              },

              filePath: 'client-invoice-created',
              subject: 'Proforma Invoice for Services Rendered',
              invoiceId: id,
              type: 'PROFORMA',
            };
            await sendnewMail(mailOptions);
          }
        }


      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }


  async uploadInvoiceForEdit(data) {
    try {
      const { invoiceData, invoiceBuffer } = data;
      const {
        organization,
        client,
        invoiceNumber,
        invoiceDueDate,
        grandTotal,
        id,
        invoiceDate,
        clientGroup,
        userId,
      } = invoiceData;
      const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
      const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const uploadData: any = await this.uploadS3(
        Buffer.from(invoiceBuffer),
        bucketS3,
        // `invoice/${id}.pdf`,
        `Invoice_${id}.pdf`,
        'application/pdf',
      );
      const { Location } = uploadData;
      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;
        const clientName =
          client?.displayName?.length > 0
            ? client?.displayName
            : client?.fullName || clientGroup?.displayName;
        const title = 'Invoice Edited';
        if (invoiceData.whatsappCheck === true) {
          const whatsappOptions = {
            title: 'client-invoice-edited',
            userId: userId,
            orgId: organization?.id,
            // to: `91${client?.mobileNumber}`,
            to: fullMobileNumberWithCountry(
              client?.mobileNumber || clientGroup?.mobileNumber,
              client?.countryCode || clientGroup?.countryCode,
            ),
            name: 'invoiceupdated_clientt',
            header: [
              {
                type: 'document',
                link: Location,
              },
            ],
            fileName: `Invoice-${invoiceNumber}`,
            key: 'INVOICE_EDITED_WHATSAPP',
          };
          await sendWhatsAppTemplateMessageUS(whatsappOptions);
          // sendDocumentInvoiceData;
        }
        if (invoiceData.emailCheck === true) {
          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;
          const keyy = 'INVOICE_EDITED_MAIL';
          if (clientPreferences && clientPreferences[keyy]) {
            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';

            const address = addressParts.join(', ') + pincode;

            const mailOptions = {
              id: userId,
              key: 'INVOICE_EDITED_MAIL',
              email: client?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                serviceName: '',
                legalName: organization?.tradeName || organization?.legalName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDateFormat: invoiceDueDateFormat,
                invoiceTotalAmount: grandTotal,
                clientName: clientName,
                address: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                userId: userId,
              },

              filePath: 'client-invoice-edited',
              subject: 'Updated Invoice for Services Rendered',
              invoiceId: id,
            };
            await sendnewMail(mailOptions);
          }
        }


        const whatsappOptions = {
          to: `91${mobileNumber}`,
          name: 'invoice',
          header: [
            {
              type: 'document',
              link: Location,
            },
          ],
          body: [fullName, invoiceNumber, clientName, invoiceDueDate, grandTotal],
          fileName: `Invoice-${invoiceNumber}`,
        };
        const key = 'INVOICES_EDITED_WHATSAPP';
        // await sendWhatsAppTemplateMessage(whatsappOptions);
        const whatsappMessageBody = `
Hi *${fullName}*
An existing invoice has been updated in ATOM:
*Invoice number:* ${invoiceNumber}
*Client name:* ${clientName}
*Due date:* ${invoiceDueDate}
*Total amount:* ${grandTotal}
        
 We hope this helps!
        `;
      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async uploadProformaInvoiceForEdit(data) {
    try {
      const { invoiceData, invoiceBuffer } = data;
      const {
        organization,
        client,
        invoiceNumber,
        invoiceDueDate,
        grandTotal,
        id,
        invoiceDate,
        clientGroup,
      } = invoiceData;
      const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
      const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const uploadData: any = await this.uploadS3(
        Buffer.from(invoiceBuffer),
        bucketS3,
        // `invoice/${id}.pdf`,
        `Invoice_${id}.pdf`,
        'application/pdf',
      );
      const { Location } = uploadData;
      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const {
          full_name: fullName,
          mobile_number: mobileNumber,
          id: userId,
          organization_id: orgId,
        } = userDetails;
        const clientName =
          client?.displayName.length > 0
            ? client?.displayName
            : client?.fullName || clientGroup?.displayName;
        const clientNumber = client?.mobileNumber || clientGroup?.mobileNumber;
        const title = 'Invoice Proforma Edited';
        if (invoiceData.whatsappCheck === true) {

          const whatsappOptions = {
            title: 'proforma-invoice-client-template',
            userId: userId,
            orgId: organization?.id,
            to: `91${clientNumber}`,
            name: 'proforma_updated_client',
            header: [
              {
                type: 'document',
                link: Location,
              },
            ],
            fileName: `Proforma-Invoice-${invoiceNumber}`,
            key: 'INVOICE_PROFORMA_EDITED_WHATSAPP',
          };
          await sendWhatsAppTemplateMessageUS(whatsappOptions);
        }
        if (invoiceData.emailCheck === true) {

          const keyy = 'INVOICE_PROFORMA_EDITED_MAIL';
          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization.id },
          });
          const clientPreferences = orgPreferences?.clientPreferences?.email;
          if (clientPreferences?.['INVOICE_PROFORMA_EDITED_MAIL']) {
            const address = `${organization.buildingNo || ' ' ? organization.buildingNo || ' ' + ', ' : ''
              }${organization.floorNumber || ' ' ? organization.floorNumber || ' ' + ', ' : ''}${organization.buildingName || ' ' ? organization.buildingName + ', ' : ''
              }${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''
              }${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''
              }${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''
              }`;

            const mailOptions = {
              id: userId,
              key: 'INVOICE_PROFORMA_EDITED_MAIL',
              email: client?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                serviceName: '',
                legalName: organization?.tradeName || organization?.legalName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDateFormat: invoiceDueDateFormat,
                invoiceTotalAmount: grandTotal,
                clientName: clientName,
                address: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                userId: userId,
              },

              filePath: 'client-invoice-edited',
              subject: 'Updated Proforma Invoice for Services Rendered',
              invoiceId: id,
              type: 'PROFORMA',
            };
            await sendnewMail(mailOptions);
          }
        }


      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_3PM)
  // @Cron(CronExpression.EVERY_MINUTE)
  // @Cron(CronExpression.EVERY_5_MINUTES)
  async summaryReport() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'Summary Report';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      try {
        const allOrganizations = await Organization.find();
        for (const organization of allOrganizations) {
          const organizationPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: organization?.id },
          });
          const whatsappCheck = organizationPreferences?.notificationConfig?.whatsappPreferences;
          if (whatsappCheck) {
            const userDetails = await User.find({
              where: {
                organization: organization?.id,
                status: UserStatus.ACTIVE,
                type: UserType.ORGANIZATION,
              },
            });
            const startOfDay = new Date();
            startOfDay.setHours(0, 0, 0, 0); // Start of the current day
            const endOfDay = new Date();
            endOfDay.setHours(23, 59, 59, 999); // End of the current day
            for (const user of userDetails) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: user?.id, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const { fullName, mobileNumber, id } = user;

                //   const tasks = await TaskStatus.find({
                //     where: {
                //         user: id,
                //         status: 'completed',
                //         createdAt: Between(startOfDay, endOfDay),
                //     },
                //     relations:['task','task.client','task.clientGroup','task.service','task.category','task.user', 'task.organization']
                // });
                const tasks = await TaskStatus.createQueryBuilder('taskStatus')
                  .leftJoinAndSelect('taskStatus.task', 'task')
                  .leftJoinAndSelect('taskStatus.user', 'user') // Alias for taskStatus's user
                  .leftJoinAndSelect('task.client', 'client')
                  .leftJoinAndSelect('task.clientGroup', 'clientGroup')
                  .leftJoinAndSelect('task.service', 'service')
                  .leftJoinAndSelect('task.category', 'category')
                  .leftJoinAndSelect('task.organization', 'organization')
                  .andWhere('taskStatus.status = :status', { status: 'completed' })
                  .andWhere('taskStatus.createdAt BETWEEN :startOfDay AND :endOfDay', {
                    startOfDay,
                    endOfDay,
                  })
                  .andWhere('organization.id = :organizationId', {
                    organizationId: user?.organization?.id,
                  })
                  .getMany();

                let completedTasks = [];
                if (tasks?.length > 0) {
                  completedTasks = tasks.map((taskStatus) => ({
                    orgId: organization?.id,
                    clientName:
                      taskStatus.task?.client?.displayName ||
                      taskStatus.task?.clientGroup?.displayName,
                    serviceCategory: taskStatus.task?.category?.name,
                    taskId: taskStatus.task?.taskNumber,
                    taskName: taskStatus.task?.name,
                    priority: taskStatus.task?.priority,
                    status: taskStatus.task?.status,
                    taskDueDate: taskStatus.task?.dueDate,
                    userName: taskStatus?.user?.fullName,
                  }));
                }

                const completedTaskCount = tasks.length;
                const clients = await Client.find({
                  where: {
                    organization: user?.organization?.id,
                    createdAt: Between(startOfDay, endOfDay),
                  },
                });

                let clientsOnBoardedToday = [];
                if (clients?.length > 0) {
                  clientsOnBoardedToday = clients.map((client) => ({
                    clientId: client?.clientId,
                    clientCategory: client?.category,
                    clientSubCategory: client?.subCategory || '-',
                    clientNumber: client?.clientNumber,
                    clientName: client?.displayName,
                    clientTradeName: client?.tradeName || '-',
                    mobileNumber: client?.mobileNumber,
                    email: client?.email,
                    status: client?.status,
                  }));
                }
                const clientsCreated = clients?.length;

                const proformaInvoice = await ProformaInvoice.find({
                  where: {
                    organization: user?.organization?.id,
                    createdAt: Between(startOfDay, endOfDay),
                  },
                  relations: ['billingEntity', 'client', 'clientGroup'],
                });
                let proformaInvoicesToday = [];
                if (proformaInvoice?.length > 0) {
                  proformaInvoicesToday = proformaInvoice.map((proforma) => ({
                    proformaInvoiceNumber: proforma?.invoiceNumber,
                    proformaInvoiceDate: proforma?.invoiceDate,
                    proformaBillingEntity: proforma?.billingEntity?.tradeName,
                    proformaClientName:
                      proforma?.client?.displayName || proforma?.clientGroup?.displayName,
                    proformaInvoiceAmount: proforma?.grandTotal,
                    proformaDueDate: proforma?.invoiceDueDate,
                    proformaStatus: proforma?.status,
                  }));
                }

                const proformaInvoiceCreated = proformaInvoice?.length;
                const invoice = await Invoice.find({
                  where: {
                    organization: user?.organization?.id,
                    createdAt: Between(startOfDay, endOfDay),
                  },
                  relations: ['billingEntity', 'client', 'clientGroup'],
                });
                let invoicesToday = [];
                if (invoice?.length > 0) {
                  invoicesToday = invoice.map((inv) => ({
                    invoiceNumber: inv?.invoiceNumber,
                    invoiceDate: inv?.invoiceDate,
                    billingEntity: inv?.billingEntity?.tradeName,
                    clientName: inv?.client?.displayName || inv?.clientGroup?.displayName,
                    invoiceAmount: inv?.grandTotal,
                    dueDate: inv?.invoiceDueDate,
                    status:
                      inv?.status === InvoiceStatus.APPROVAL_PENDING ||
                        inv?.status === InvoiceStatus.PARTIALLY_PAID
                        ? inv?.invoiceDueDate > moment().subtract(1, 'day').format('YYYY-MM-DD')
                          ? inv.status === InvoiceStatus.APPROVAL_PENDING
                            ? 'Created'
                            : 'Partially Paid'
                          : 'Overdue'
                        : inv?.status === InvoiceStatus.CANCELLED
                          ? 'Cancelled'
                          : 'Paid',
                  }));
                }
                const invoiceCreated = invoice?.length;
                const receipts = await Receipt.find({
                  where: {
                    organization: user?.organization?.id,
                    createdAt: Between(startOfDay, endOfDay),
                  },
                  relations: ['billingEntity', 'client', 'clientGroup'],
                });
                let receiptsToday = [];
                if (receipts?.length > 0) {
                  receiptsToday = receipts.map((receipt) => ({
                    receiptNumber: receipt?.receiptNumber,
                    receiptDate: receipt?.receiptDate,
                    billingEntity: receipt?.billingEntity?.tradeName,
                    receiptType: receipt?.type,
                    clientName: receipt?.client?.displayName || receipt?.clientGroup?.displayName,
                    receiptMode: receipt?.paymentMode,
                    receiptAmount: receipt?.amount,
                    status: receipt?.status,
                  }));
                }
                const startDate = moment(new Date()).format('YYYY-MM-DD');
                const endDate = moment(new Date()).format('YYYY-MM-DD');
                const activeUserss = await User.find({
                  where: {
                    organization: user?.organization?.id,
                    type: 'ORGANIZATION',
                  },
                  relations: ['role'], // Include role details
                });

                // Step 2: Fetch attendance details for the users
                const userIdss = activeUserss.map((user) => user.id);
                const receiptsCreated = receipts?.length;
                const expenditure = await Expenditure.createQueryBuilder('expenditure')
                  .leftJoinAndSelect('expenditure.task', 'task')
                  .leftJoinAndSelect('expenditure.client', 'client')
                  .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
                  .leftJoinAndSelect('task.organization', 'organization')
                  .leftJoinAndSelect('expenditure.user', 'user')
                  .andWhere('expenditure.date BETWEEN :startDate AND :endDate', {
                    startDate,
                    endDate,
                  })
                  .andWhere('user.id IN (:...users)', { users: userIdss })
                  .getMany();
                let expenditureCreatedToday = [];
                if (expenditure?.length > 0) {
                  expenditureCreatedToday = expenditure.map((exp) => ({
                    expenditureType: exp?.type,
                    client: exp?.client?.displayName || exp?.clientGroup?.displayName,
                    expenseType: exp?.taskExpenseType,
                    taskId: exp?.task?.taskNumber,
                    taskName: exp?.task?.name,
                    expenseTitle: exp?.particularName,
                    amount: exp?.amount,
                    userName: exp?.user?.fullName,
                  }));
                }

                const expenditureToday = expenditure?.length;

                let tasksDue = await createQueryBuilder(Task, 'task')
                  .leftJoin('task.organization', 'organization')
                  .leftJoin('task.members', 'taskMembers')
                  .leftJoinAndSelect('task.category', 'category')
                  .leftJoinAndSelect('task.members', 'members')
                  .leftJoinAndSelect('members.imageStorage', 'imageStorage')
                  .leftJoinAndSelect('task.client', 'client')
                  .leftJoinAndSelect('task.clientGroup', 'clientGroup')
                  .where('organization.id = :id', { id: user.organization.id })
                  .andWhere('task.status not in (:...status)', {
                    status: ['terminated', 'deleted', 'completed'],
                  })
                  .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
                  .andWhere('task.parentTask is null')
                  .andWhere('DATE(task.dueDate) = :dueDate', {
                    dueDate: moment().add(1, 'day').format('YYYY-MM-DD'),
                  })
                  .orderBy('task.dueDate', 'ASC')
                  .getMany();

                // let result = await tasks.getMany();

                let tasksDueTommorow = [];
                if (tasksDue?.length > 0) {
                  tasksDueTommorow = tasksDue.map((task) => ({
                    orgId: organization?.id,
                    clientName: task?.client?.displayName || task?.clientGroup?.displayName,
                    serviceCategory: task?.category?.name,
                    taskId: task?.taskNumber,
                    taskName: task?.name,
                    priority: task?.priority,
                    status: task?.status,
                    taskDueDate: task?.dueDate,
                    userName: task?.user?.fullName,
                  }));
                }

                const tasksDueTommorowCount = tasksDue?.length;

                // Count GSTR Additional Notices
                // const additionalGSTNoticeCount = await GstrAdditionalNoticeOrders.find({
                //   where: {
                //     organizationId: organization?.id,
                //     dueDate: Between(
                //       moment(startOfDay).format('DD/MM/YYYY'),
                //       moment(endOfDay).format('DD/MM/YYYY'),
                //     ),
                //   },
                //   relations:['client']

                // });
                const additionalNoticeOrders = await GstrAdditionalNoticeOrders.createQueryBuilder(
                  'additionalNotice',
                )
                  .where('additionalNotice.organizationId = :organizationId', {
                    organizationId: organization.id,
                  })
                  .andWhere(
                    new Brackets((qb) => {
                      qb.where(
                        'STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay,"%Y-%m-%d") AND STR_TO_DATE(:endOfDay,"%Y-%m-%d")',
                        { startOfDay, endOfDay },
                      ).orWhere(
                        'STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay, "%Y-%m-%d") AND STR_TO_DATE(:endOfDay, "%Y-%m-%d")',
                        { startOfDay, endOfDay },
                      );
                    }),
                  )
                  .leftJoinAndSelect('additionalNotice.client', 'client')
                  .getMany();
                let additionalNoticeOrdersArray = [];
                if (additionalNoticeOrders?.length > 0) {
                  additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => ({
                    orgId: organization?.id,
                    clientName: order?.client?.displayName,
                    issuanceDate: moment(order?.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                    dueDate:
                      order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                        ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                        : order?.dueDate,
                    financialYear: order?.fy || '-',
                    folder: order?.caseFolderTypeName,
                    referenceNumber: order?.refNum,
                    type: order?.caseTypeName,
                  }));
                }
                const additionalGSTNoticeCountToday = additionalNoticeOrders?.length;
                // Count GSTR Notices
                // const gstNoticeCount = await GstrNoticeOrders.find({
                //   where: {
                //     organizationId: organization?.id,
                //     dueDate: Between(
                //       moment(startOfDay).format('DD/MM/YYYY'),
                //       moment(endOfDay).format('DD/MM/YYYY'),
                //     ),
                //   },
                //   relations:['client']

                // });
                const noticeOrders = await GstrNoticeOrders.createQueryBuilder('noticeOrder')
                  .where('noticeOrder.organizationId = :organizationId', {
                    organizationId: organization.id,
                  })
                  .andWhere(
                    new Brackets((qb) => {
                      qb.where(
                        'STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay,"%Y-%m-%d") AND STR_TO_DATE(:endOfDay,"%Y-%m-%d")',
                        { startOfDay, endOfDay },
                      ).orWhere(
                        'STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay, "%Y-%m-%d") AND STR_TO_DATE(:endOfDay, "%Y-%m-%d")',
                        { startOfDay, endOfDay },
                      );
                    }),
                  )
                  .leftJoinAndSelect('noticeOrder.client', 'client')
                  .getMany();

                let noticeAndOrdersArray = [];
                if (noticeOrders?.length > 0) {
                  noticeAndOrdersArray = noticeOrders.map((order) => ({
                    orgId: organization?.id,
                    clientName: order?.client?.displayName,
                    orderNumber: order?.orderNumber,
                    issuanceDate: moment(order?.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                    dueDate:
                      order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                        ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                        : order?.dueDate,
                    type: order?.type,
                    amount: order?.amountOfDemand,
                  }));
                }
                const gstNoticeCountToday = noticeOrders.length;
                const totalGSTDueCount = additionalGSTNoticeCountToday + gstNoticeCountToday;
                // const fyaNoticeCount = await AutFyaNotice.find({
                //   where: {
                //     organizationId: organization?.id,
                //     responseDueDate: Between(
                //       startOfDay,
                //       endOfDay
                //     ),
                //   },
                //   relations:['client']

                // });
                const fyaRecords = await createQueryBuilder(AutFyaNotice, 'autFyaNotice')
                  .where('autFyaNotice.organizationId = :orgId', { orgId: organization?.id })
                  .andWhere(
                    new Brackets((qb) => {
                      qb.where('DATE(autFyaNotice.issuedOn) BETWEEN :startOfDay AND :endOfDay', {
                        startOfDay,
                        endOfDay,
                      }).orWhere(
                        'DATE(autFyaNotice.responseDueDate) BETWEEN :startOfDay AND :endOfDay',
                        {
                          startOfDay,
                          endOfDay,
                        },
                      );
                    }),
                  )
                  .leftJoinAndSelect('autFyaNotice.client', 'client')
                  .getMany();
                let fyaRecordsArray = [];
                if (fyaRecords?.length > 0) {
                  fyaRecordsArray = fyaRecords.map((item) => ({
                    orgId: organization?.id,
                    id: item.id,
                    clientName: item?.client?.displayName,
                    panNumber: item?.pan,
                    proceedingName: item?.proceedingName,
                    din: item?.documentIdentificationNumber || '-',
                    ay: item?.assesmentYear
                      ? `${item.assesmentYear}-${parseInt(item?.assesmentYear) + 1}`
                      : '-',
                    issuedOnDate:
                      item?.issuedOn &&
                        moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                        ? moment(item?.issuedOn).format('DD-MM-YYYY')
                        : '-',
                    responseDueDate:
                      item?.responseDueDate &&
                        moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                        ? moment(item?.responseDueDate).format('DD-MM-YYYY')
                        : '-',
                  }));
                }
                const fyaNoticeCountToday = fyaRecords.length;

                // const fyiNoticeCount = await AutFyiNotice.find({
                //   where: {
                //     organizationId: organization?.id,
                //     responseDueDate: Between(
                //       startOfDay,
                //       endOfDay,
                //     ),
                //   },
                //   relations:['client','eProceeding']

                // });
                const fyiRecords = await createQueryBuilder(AutFyiNotice, 'autFyiNotice')
                  .leftJoinAndSelect('autFyiNotice.eProceeding', 'eProceeding')
                  .where('autFyiNotice.organizationId = :orgId', { orgId: organization?.id })
                  .andWhere(
                    new Brackets((qb) => {
                      qb.where('DATE(autFyiNotice.issuedOn) BETWEEN :startOfDay AND :endOfDay', {
                        startOfDay,
                        endOfDay,
                      }).orWhere(
                        'DATE(autFyiNotice.responseDueDate) BETWEEN :startOfDay AND :endOfDay',
                        {
                          startOfDay,
                          endOfDay,
                        },
                      );
                    }),
                  )
                  .leftJoinAndSelect('autFyiNotice.client', 'client')
                  .getMany();
                let fyiRecordsArray = [];
                if (fyiRecords?.length > 0) {
                  fyiRecordsArray = fyiRecords.map((item) => ({
                    orgId: organization?.id,
                    id: item.id,
                    clientName: item?.client?.displayName,
                    panNumber: item?.pan,
                    proceedingName: item?.proceedingName,
                    din: item?.documentIdentificationNumber || '-',
                    ay: item?.eProceeding?.assessmentYear
                      ? `${item?.eProceeding?.assessmentYear}-${parseInt(item?.eProceeding?.assessmentYear) + 1
                      }`
                      : '-',
                    issuedOnDate:
                      item?.issuedOn &&
                        moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                        ? moment(item?.issuedOn).format('DD-MM-YYYY')
                        : '-',
                    responseDueDate:
                      item?.responseDueDate &&
                        moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                        ? moment(item?.responseDueDate).format('DD-MM-YYYY')
                        : '-',
                  }));
                }

                const orgId = organization?.id;

                // Step 1: Get all active users
                const activeUsers = await User.find({
                  where: {
                    organization: orgId,
                    status: 'ACTIVE',
                    type: 'ORGANIZATION',
                  },
                  relations: ['role'], // Include role details
                });

                // Step 2: Fetch attendance details for the users
                const userIds = activeUsers.map((user) => user.id);
                const attendanceDetails = await Attendance.find({
                  where: {
                    userId: In(userIds),
                    attendanceDate: Between(
                      moment(startOfDay).format('YYYY-MM-DD'),
                      moment(endOfDay).format('YYYY-MM-DD'),
                    ),
                  },
                });

                // Step 3: Fetch timesheet details for the users
                const logHours = await LogHour.find({
                  where: {
                    user: In(userIds),
                    completedDate: Between(startOfDay, endOfDay),
                  },
                  relations: ['user', 'client', 'clientGroup', 'task'], // Include user, client, and client group details
                });

                // Step 4: Map the data
                const mergedData = activeUsers.map((user) => {
                  const attendance = attendanceDetails.find((att) => att.userId === user.id);
                  const userLogHours = logHours.filter((log) => log.user.id === user.id);

                  return {
                    userId: user.id,
                    userName: user.fullName,
                    role: user.role?.name || '-', // Handle missing role gracefully
                    attendanceStatus: attendance?.type || '-', // Default to 'Absent' if no attendance
                    checkinTime: attendance?.checkin_time
                      ? moment(attendance.checkin_time).format('h:mm A')
                      : '-',
                    checkoutTime: attendance?.checkout_time
                      ? moment(attendance.checkout_time).format('h:mm A')
                      : '-',
                    totalLogHours: attendance?.hours_logged,
                    logHours: userLogHours.map((log) => ({
                      type: log?.type,
                      client: log.client?.displayName || log.clientGroup?.displayName,
                      taskName: log?.task?.name || log?.title,
                      taskId: log.task?.taskNumber || '-',
                      duration: moment.utc(+log?.duration).format('HH:mm') || '-',
                    })),
                  };
                });

                const today = new Date();
                const reportDate = `${today.getDate().toString().padStart(2, '0')}-${(
                  today.getMonth() + 1
                )
                  .toString()
                  .padStart(2, '0')}-${today.getFullYear()}`;
                // Outputs: '04-12-2024'
                // const reportDate = '04-12-2024'
                const fyiNoticeCountToday = fyiRecords.length;
                const totalITNoticeDue = fyiNoticeCountToday + fyaNoticeCountToday;
                const ejsData = {
                  reportDate,
                  completedTaskCount,
                  clientsCreated,
                  proformaInvoiceCreated,
                  invoiceCreated,
                  receiptsCreated,
                  expenditureToday,
                  tasksDueTommorowCount,
                  totalITNoticeDue,
                  totalGSTDueCount,
                  completedTasks,
                  clientsOnBoardedToday,
                  proformaInvoicesToday,
                  invoicesToday,
                  receiptsToday,
                  expenditureCreatedToday,
                  tasksDueTommorow,
                  noticeAndOrdersArray,
                  additionalNoticeOrdersArray,
                  fyaRecordsArray,
                  fyiRecordsArray,
                  mergedData,
                };
                const htmlContent = await ejs.renderFile(
                  'src/emails/templates/summary-report.ejs',
                  ejsData,
                );
                // const browser = await puppeteer.launch();
                const browser = await puppeteer.launch({
                  headless: true, // Run in headless mode
                  // executablePath: '/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.108/chrome-linux64/chrome', // Path to Chrome
                  executablePath: '/usr/bin/google-chrome',
                  args: ['--no-sandbox', '--disable-setuid-sandbox'], // Required for servers
                });
                const page = await browser.newPage();
                await page.setContent(htmlContent);
                const pdfBuffer = await page.pdf({
                  format: 'A4', // Standard page size
                  printBackground: true, // Include background colors and images
                  margin: {
                    top: '1cm',
                    bottom: '1cm',
                    left: '1cm',
                    right: '1cm',
                  },
                });

                await browser.close();
                // Upload PDF to S3
                const s3Params = {
                  Bucket: process.env.AWS_BUCKET_NAME,
                  Key: `summary-report-${organization.id}-${moment().format(
                    'YYYY-MM-DD HH:mm:ss',
                  )}.pdf`,
                  Body: pdfBuffer,
                  ContentType: 'application/pdf',
                };
                const uploadResult = await s3.upload(s3Params).promise();
                const pdfLink = uploadResult.Location;
                const title = 'Summary Report';
                const key = 'SUMMARY_REPORT_WHATSAPP';
                try {
                  const caption = 'Summary Report';
                  const filename = 'Summary-Report.pdf';

                  await sendDocumentTextMessage(
                    user?.mobileNumber,
                    pdfLink,
                    caption,
                    filename,
                    user?.organization?.id,
                    user?.id,
                    title,
                    key,
                  );
                } catch (error) {
                  console.error(
                    'Error sending document message for user:',
                    user.mobileNumber,
                    error,
                  );
                }
              }
            }
          }
        }
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
      } catch (error) {
        console.log(error);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = error?.message;
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
      }
    }
  }
  async uploadS3(file: Buffer, bucket: string, name: string, contentType = '') {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: name,
      Body: file,
      ContentType: contentType,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  getS3() {
    return new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID_VIDER,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_VIDER,
    });
  }

  async synccalender(data) {
    try {
      const headers = {
        'accept': 'application/json',
        'X-ACCESS-TOKEN': data.token,
        'X-USER-ID': data.email,
        'X-DB-USER-ID': data.userid,
        'X-DB-ORG-ID': data.orgid,
      };
      const url = `${process.env.QUANTUM_API_URL}/events`;
      const response = await axios.get(url, { headers });
      if (response?.status == 200) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error(error);
    }
  }

  async addTaskDataForReport(data) {
    // Collect all task ids
    const taskIds = data.map(d => d.id);

    // Fetch tasks with members and leaders
    const tasksWithRelations = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'taskMember')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .whereInIds(taskIds)
      .getMany();

    // Map for lookup
    const taskMap = new Map(
      tasksWithRelations.map(t => [
        t.id,
        {
          members: t.members.map((member: User) => ({
            id: member.id,
            fullName: member.fullName,
          })),
          taskLeader: t.taskLeader.map((leader: User) => ({
            id: leader.id,
            fullName: leader.fullName,
          })),
        },
      ]),
    );

    // Merge with original data
    const Data = data.map(d => ({
      ...d,
      members: taskMap.get(d.id)?.members || [],
      taskLeaders: taskMap.get(d.id)?.taskLeader || [],
    }));

    console.log("final:", JSON.stringify(Data, null, 2));
    return Data;
  }


  async synccalenderstatus(data) {
    try {
      const headers = {
        'accept': 'application/json',
        'X-ACCESS-TOKEN': data.token,
        'X-USER-ID': data.user,
      };
      const url = `${process.env.QUANTUM_API_URL}/sync/status/${data.user}`;
      const response = await axios.get(url, { headers });
      if (response?.status == 200) {
        return {
          message: 'Calendar sync is successfull',
          completed: true,
        };
      } else {
        return {
          message: 'Calendar sync is failed',
          completed: false,
        };
      }
    } catch (error) {
      console.error(error);
    }
  }

  async exportClientProformaInvoiceReport(clientId: any, payload: any) {
    const newQuery = { ...payload, offset: 0, limit: 100000000 };
    let invoices = await this.getclientproformainvoiceexport(clientId, newQuery);

    if (!invoices?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Proforma Invoice');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Invoice Number', key: 'invoiceNumber' },
      { header: 'Amount', key: 'amount' },
      { header: 'Status', key: 'status' },
    ];
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    invoices?.result?.forEach((invoice) => {
      const rowData = {
        serialNo: serialCounter++,
        invoiceDate: invoice?.created_at,
        invoiceNumber: invoice?.invoice_number,
        amount: 1 * invoice?.grand_total,
        status: invoice?.status == 'APPROVAL_PENDING' ? 'INVOICED' : getTitle(invoice?.status),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getclientproformainvoiceexport(clientId, payload: any) {
    const clientCondation = payload?.clientId
      ? `client_id=${payload.clientId}`
      : `client_group_id=${payload.clientGroupId}`;
    let sql = `select * from proforma_invoice where ${clientCondation}
    AND invoice_number LIKE '%${payload.search}%'; `;

    let result = await getManager().query(sql);

    return {
      result,
    };
  }

  @Cron(CronExpression.EVERY_DAY_AT_6AM)
  async sendTrailExpiredMessage() {
    if (process.env.Cron_Running === 'true') {
      const entityManager = getManager();
      const expiryOrgsql = `SELECT * FROM organization WHERE DATE_SUB(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')),
     INTERVAL 2 DAY) = CURDATE() - INTERVAL 1 DAY;`;
      const nextdayExpiryOrgs = await entityManager.query(expiryOrgsql);

      let notifications = [];
      for (let expiryOrg of nextdayExpiryOrgs) {
        const userbyorgIdsql = `SELECT * FROM user WHERE organization_id = ${expiryOrg.id} and type = 'ORGANIZATION';`;
        const usersbyorgId = await entityManager.query(userbyorgIdsql);

        if (Array.isArray(usersbyorgId) && usersbyorgId.length > 0) {
          for (let orguser of usersbyorgId) {
            if (
              expiryOrg &&
              expiryOrg.config &&
              expiryOrg.config.expirydate &&
              expiryOrg?.config?.demo == 'yes'
            ) {
              let newNotification = new Notification();
              newNotification.title = 'Your Trial Period expires Soon';
              newNotification.body = `Trail period end date is ${expiryOrg.config.expirydate}`;
              newNotification.user = orguser;
              notifications.push(newNotification);
            }
          }
        }
      }
      if (notifications.length > 0) {
        await Notification.save(notifications);
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_5AM)
  async sendSubscriptionMail() {
    if (process.env.Cron_Running === 'true') {
      const entityManager = getManager();
      const expiryOrgsql = `SELECT * FROM organization WHERE DATE_SUB(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')),
     INTERVAL 2 DAY) = CURDATE() - INTERVAL 1 DAY;`;
      const nextdayExpiryOrgs = await entityManager.query(expiryOrgsql);

      let notifications = [];
      for (let expiryOrg of nextdayExpiryOrgs) {
        if (
          expiryOrg?.config?.subscriptionmode == 'monthly' ||
          expiryOrg?.config?.subscriptionmode == 'yearly'
        ) {
          const userbyorgIdsql = `SELECT * FROM user WHERE organization_id = ${expiryOrg.id} and type = 'ORGANIZATION';`;
          const usersbyorgId = await entityManager.query(userbyorgIdsql);
          if (Array.isArray(usersbyorgId) && usersbyorgId.length > 0) {
            for (let orguser of usersbyorgId) {
              if (
                expiryOrg &&
                expiryOrg?.config &&
                expiryOrg?.config?.expirydate &&
                expiryOrg?.config?.demo == 'no'
              ) {
                let newNotification = new Notification();
                newNotification.title = 'Your subscription expires soon';
                newNotification.body = `subscription end date is ${expiryOrg.config.expirydate}`;
                newNotification.user = orguser;
                notifications.push(newNotification);
              }
            }
          }
        }
      }
      if (notifications.length > 0) {
        await Notification.save(notifications);
      }
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async GetReport() {
    // if (process.env.Cron_Running === 'true') {
      const currentTime = moment();
      const startTime = moment().set({ hour: 3, minute: 55, second: 0 });
      const endTime = moment().set({ hour: 4, minute: 5, second: 0 });
      // if (
      //   currentTime.isBetween(startTime, endTime) &&
      //   process.env.WEBSITE_URL === 'https://atom.vider.in'
      // ) {
        const cronData = new CronActivity();
        cronData.cronType = 'REMAINDER MAIL';
        cronData.cronDate = moment().toDate().toString();
        cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
        const cornActivityID = await cronData.save();
        let ErrorMails = [];
        try {
          const statutoryComplaianceEvents = await Event.find({
            where: { defaultOne: true, date: moment().format('YYYY-MM-DD') },
          });
          const organisationActiveList = await Organization.createQueryBuilder('organization')
            .select(['organization.id', 'user.id', 'user.fullName', 'user.email'])
            .leftJoin('organization.users', 'user')
            .where(
              "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
              { expirydate: moment().format('YYYY-MM-DD') },
            )
            .andWhere('user.status = :status', { status: 'active' })
            .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
            .getMany();

          for (let organization of organisationActiveList) {
            try {
              const getDSCExpiry = await createQueryBuilder(DscRegister, 'dscRegister')
                .leftJoin('dscRegister.organization', 'organization')
                .where('organization.id = :id', { id: organization.id })
                .andWhere('dscRegister.expiryDate = :date', { date: moment().format('YYYY-MM-DD') })
                .getMany();

              for (let user of organization.users) {
                try {
                  const getDueTasks = await Task.createQueryBuilder('task')
                    .select([
                      'task.id',
                      'task.name',
                      'task.taskNumber',
                      'task.recurringStatus',
                      'task.status',
                      'client.displayName',
                      'clientGroup.displayName',
                    ])
                    .leftJoin('task.members', 'user')
                    .leftJoin('task.client', 'client')
                    .leftJoin('task.clientGroup', 'clientGroup')
                    .where('user.id = :id', { id: user.id })
                    .andWhere('task.dueDate = :duedate', { duedate: moment().format('YYYY-MM-DD') })
                    .andWhere(
                      '(task.recurringStatus is null or task.recurringStatus = :recurringStatus)',
                      {
                        recurringStatus: TaskRecurringStatus.CREATED,
                      },
                    )
                    .andWhere('task.status IN (:...statuses)', {
                      statuses: [
                        TaskStatusEnum.TODO,
                        TaskStatusEnum.IN_PROGRESS,
                        TaskStatusEnum.ON_HOLD,
                        TaskStatusEnum.UNDER_REVIEW,
                      ],
                    })
                    .getMany();

                  let dueTasks = [];

                  for (let i of getDueTasks) {
                    i['client']['displayName'] = i?.['client']?.['displayName']
                      ? i?.['client']?.['displayName']
                      : i?.['clientGroup']?.['displayName'];
                    dueTasks.push(i);
                  }

                  const events = await createQueryBuilder(Event, 'event')
                    .select([
                      'event.title',
                      'event.location',
                      'event.startTime',
                      'event.endTime',
                      'client.displayName',
                      'clientGroup.displayName',
                      'task.name',
                    ])
                    .leftJoin('event.task', 'task')
                    .leftJoin('event.client', 'client')
                    .leftJoin('event.clientGroup', 'clientGroup')
                    .leftJoin('event.user', 'user')
                    .where('user.id = :id', { id: user.id })
                    .andWhere('event.date = :date', { date: moment().format('YYYY-MM-DD') })
                    .getMany();

                  let generalEvents = [];
                  let taskEvents = [];

                  for (let i of events) {
                    i['client']['displayName'] = i?.['client']?.['displayName']
                      ? i?.['client']?.['displayName']
                      : i?.['clientGroup']?.['displayName'];
                    if (i?.task) {
                      i['duration'] = `${moment(i?.startTime).format('hh:mm A')} - ${moment(
                        i?.endTime,
                      ).format('hh:mm A')}`;
                      taskEvents.push(i);
                    } else {
                      i['duration'] = `${moment(i?.startTime).format('hh:mm A')} - ${moment(
                        i?.endTime,
                      ).format('hh:mm A')}`;
                      generalEvents.push(i);
                    }
                  }

                  let mailData = {
                    events: statutoryComplaianceEvents?.length ? statutoryComplaianceEvents : null,
                    getDSCExpiry: getDSCExpiry?.length ? getDSCExpiry : null,
                    getDueTasks: dueTasks?.length ? dueTasks : null,
                    taskEvents: taskEvents?.length ? taskEvents : null,
                    generalEvents: generalEvents?.length ? generalEvents : null,
                    fullname: user?.fullName,
                    email: user?.email,
                  };

                  if (
                    statutoryComplaianceEvents?.length ||
                    getDSCExpiry?.length ||
                    getDueTasks?.length ||
                    taskEvents?.length ||
                    generalEvents?.length
                  ) {
                    try {
                      let data = mailData;
                      let subject = `Focused Goals for Today`;
                      let email = user?.email;
                      let filePath = 'remainder-mail';
                      let html = {};
                      if (filePath == '') {
                        html = data;
                      } else {
                        try {
                          let templatePath = `src/emails/templates/${filePath}.ejs`;
                          let templateStr = fs.readFileSync(templatePath, 'utf-8');
                          let template = ejs.compile(templateStr.toString());
                          html = filePath == '' ? data : template(data);
                        } catch (error) {
                          console.log('error', error);
                        }
                      }

                      let mailOptions = {
                        from: {
                          name: 'Vider',
                          address: process.env.FROM_EMAIL,
                        },
                        to: '<EMAIL>',
                        subject: subject,
                        html: html,
                      };

                      await this.emailThrottleService.enqueueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

                      // transporter.sendMail(mailOptions, function (error: any, info: any) {
                      //   if (error) {
                      //     ErrorMails.push(error?.message ? error.message : error);
                      //   } else {
                      //     console.log(info.response);
                      //   }
                      // });
                    } catch (error) {
                      ErrorMails.push(error?.message ? error.message : error);
                    }
                  }
                } catch (error) {
                  ErrorMails.push(error?.message ? error.message : error);
                }
              }
            } catch (error) {
              ErrorMails.push(error?.message ? error.message : error);
            }
          }
          const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
            .where('id = :id', { id: cornActivityID.id })
            .getOne();
          getcornActivityID.responseData = ErrorMails.length ? ErrorMails.join(',') : 'Success';
          getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
          await getcornActivityID.save();
        } catch (error) {
          const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
            .where('id = :id', { id: cornActivityID.id })
            .getOne();
          getcornActivityID.responseData = ErrorMails.length ? ErrorMails.join(',') : error;
          getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
          await getcornActivityID.save();
          console.log(error);
        }
      // }
    // }
  }

  // @Cron(CronExpression.EVERY_DAY_AT_1AM)
  // async fetchoraganizationdetails() {
  //   const cronData = new CronActivity();
  //   cronData.cronType = 'SMTP FILE UPDATION';
  //   cronData.cronDate = moment().toDate().toString();
  //   cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //   const cornActivityID = await cronData.save();
  //   try {
  //     const filedata = [];
  //     const organisationActiveList = await Organization.createQueryBuilder('organization')
  //       .select([
  //         'organization.id',
  //         'organization.email',
  //         'organization.smtp',
  //         'user.id',
  //         'user.fullName',
  //         'user.email',
  //         'user.type',
  //       ])
  //       .leftJoin('organization.users', 'user')
  //       .andWhere('user.status = :status', { status: 'active' })
  //       .andWhere('user.type = :userType', { userType: UserType.ORGANIZATION })
  //       .getMany();
  //     for (let organization of organisationActiveList) {
  //       if (organization['smtp']) {
  //         let mailOptions = {
  //           from: organization['smtp']['from'],
  //           to: '',
  //           organization: organization?.id,
  //           user: organization.users,
  //           smtp: organization['smtp'],
  //         };
  //         filedata.push(mailOptions);
  //       }
  //     }
  //     setTimeout(
  //       async (fd) => {
  //         await writeLocalOrgDetails(fd);
  //       },
  //       5000,
  //       filedata,
  //     );
  //   } catch (error) {
  //     console.error('Error retrieving organizations:', error.message);
  //     const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //       .where('id = :id', { id: cornActivityID.id })
  //       .getOne();
  //     getcornActivityID.responseData = error.message;
  //     getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //     await getcornActivityID.save();

  //     return [];
  //   }

  //   const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //     .where('id = :id', { id: cornActivityID.id })
  //     .getOne();
  //   getcornActivityID.responseData = 'success';
  //   getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //   await getcornActivityID.save();
  // }

  @Cron(CronExpression.EVERY_DAY_AT_7AM)
  async sendCronEmail() {
    if (process.env.Cron_Running === 'true') {
      const date = moment().format('YYYY-MM-DD'); // Format the date as YYYY-MM-DD
      const getCronData = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('DATE(cronActivity.cronDate) = :date', { date })
        .getMany();

      let mailData = {
        cronData: getCronData,
        fullname: 'Shashank',
        email: '<EMAIL>',
        environment: process.env.WEBSITE_URL,
      };

      try {
        let data = mailData;
        let subject = `Cron Data`;
        let email = '<EMAIL>';
        let filePath = 'cron-mail';
        let html = {};
        if (filePath == '') {
          html = data;
        } else {
          try {
            let templatePath = `src/emails/templates/${filePath}.ejs`;
            let templateStr = ejs.fileLoader(templatePath);
            let template = ejs.compile(templateStr.toString());
            html = filePath == '' ? data : template(data);
          } catch (error) {
            console.log('error', error);
          }
        }

        let mailOptions = {
          from: {
            name: 'Vider',
            address: process.env.FROM_EMAIL,
          },
          to: email,
          subject: subject,
          html: html,
        };

        transporter.sendMail(mailOptions, function (error: any, info: any) {
          if (error) {
            console.log(error);
          } else {
            console.log(info.response);
          }
        });
      } catch (error) {
        console.log(error);
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_7PM)
  async addSchedulingOrganization() {
    if (
      process.env.WEBSITE_URL === 'https://atom.vider.in' &&
      process.env.Cron_Running === 'true'
    ) {
      const organizationId = 514;
      const user = await User.findOne({ where: { organization: organizationId } });

      const autClients = await AutClientCredentials.find({
        where: { organizationId: organizationId, status: IncomeTaxStatus.ENABLE },
      });

      if (autClients) {
        for (let item of autClients) {
          const automationMachines = new AutomationMachines();
          automationMachines.autoCredentials = item;
          automationMachines.modules = ['OD', 'EP'];
          automationMachines.type = TypeEnum.INCOMETAX;
          automationMachines.status = 'INQUEUE';
          automationMachines.user = user;
          await automationMachines.save();
        }
      }

      const gstrClients = await GstrCredentials.find({
        where: { organizationId: organizationId, status: GstrStatus.ENABLE },
      });

      if (gstrClients) {
        for (let item of gstrClients) {
          const automationMachines = new AutomationMachines();
          automationMachines.gstrCredentials = item;
          automationMachines.modules = ['P', 'NAO', 'ANO'];
          automationMachines.type = TypeEnum.GSTR;
          automationMachines.status = 'INQUEUE';
          automationMachines.user = user;
          await automationMachines.save();
        }

      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async disableWhatsappForExpiredOrganizations() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'Disable Whatsapp To ExpiredOrgs';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const organisationExpiredList = await Organization.createQueryBuilder('organization')
        .select(['organization.id', 'user.id', 'user.fullName', 'user.email'])
        .leftJoin('organization.users', 'user')
        .where(
          "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') < :expirydate",
          { expirydate: moment().format('YYYY-MM-DD') },
        )
        .andWhere('user.status = :status', { status: 'active' })
        .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
        .getMany();

      for (const org of organisationExpiredList) {
        const organizationPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: org?.id },
        });
        const whatsappCheck = organizationPreferences?.notificationConfig?.whatsappPreferences;
        if (whatsappCheck) {
          // Update to false
          organizationPreferences.notificationConfig.whatsappPreferences = false;

          await OrganizationPreferences.save({
            ...organizationPreferences,
            notificationConfig: organizationPreferences.notificationConfig,
          });

        }
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = 'success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }

  async InvoiceReminder(data, userId) {
    try {
      const { invoiceData, invoiceBuffer } = data;
      const {
        organization,
        client,
        clients,
        clientGroup,
        invoiceNumber,
        invoiceDueDate,
        invoiceDate,
        grandTotal,
        id,
        status,
      } = invoiceData;
      const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
      const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
      let statusText = status;
      const entityManager = getManager();

      // Check if the invoice is overdue
      if (
        (status === 'APPROVAL_PENDING' || status === 'PARTIALLY_PAID') &&
        moment().format('YYYY-MM-DD') > moment(invoiceDueDate).format('YYYY-MM-DD')
      ) {
        statusText = 'Overdue';
      } else if (status === 'APPROVAL_PENDING') {
        statusText = 'Invoiced';
      } else {
        const statusMap = {
          PARTIALLY_PAID: 'Partially paid',
        };
        statusText = statusMap[status] || 'Unknown';
      }
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const uploadData: any = await this.uploadS3(
        Buffer.from(invoiceBuffer),
        bucketS3,
        `invoice/${id}.pdf`,
        'application/pdf',
      );
      const { Location } = uploadData;
      const admins = await getAdminIDsBasedOnOrganizationId(organization?.id);
      const client_id = client?.id;
      const orgQuery = `SELECT organization_id FROM client where id = ${client_id};`;
      const orgIdSql = await entityManager.query(orgQuery);
      const orgId = orgIdSql[0]?.organization_id;
      const orgdetails = await Organization.findOne({ id: orgId });

      const addressParts = [
        orgdetails.buildingNo || '',
        orgdetails.floorNumber || '',
        orgdetails.buildingName || '',
        orgdetails.street || '',
        orgdetails.location || '',
        orgdetails.city || '',
        orgdetails.district || '',
        orgdetails.state || '',
      ].filter((part) => part && part.trim() !== '');
      const pincode =
        orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

      const address: any = addressParts.join(', ') + pincode;
      const remindData = {
        clientName: client?.displayName,
        invoiceNumber: invoiceNumber,
        invoiceDate: invoiceDueDate,
        dueDate: invoiceDueDate,
        invoiceAmount: grandTotal,
        legalName: organization?.tradeName || organization?.legalName,
        userId: userId,
        address: address,
        phoneNumber: organization?.mobileNumber,
        mail: organization?.email,
      };
      let key = 'INVOICE_REMINDER_MAIL';
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: organization.id },
      });
      const clientPreferences = orgPreferences?.clientPreferences?.email;
      if (clientPreferences && clientPreferences[key]) {
        await sendnewMail({
          id: userId,
          key: 'INVOICE_REMINDER_MAIL',
          email: client.email,
          data: remindData,

          filePath: 'invoice-remind',
          subject: `Invoice Reminder`,
          clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        });
      }
      for (let admin of admins) {
        const userDetails = await getUserDetails(admin);
        const { full_name: fullName, mobile_number: mobileNumber, id: userId } = userDetails;
        const clientName = client
          ? client?.displayName.length > 0
            ? client?.displayName
            : client?.fullName
          : clientGroup?.displayName;
        const title = 'Invoice Created';

        const whatsappOptions: any = {
          title: 'invoice-reminder-client',
          userId: userId,
          orgId: organization?.id,
          // to: `91${client?.mobileNumber}`,
          to: fullMobileNumberWithCountry(
            client ? client?.mobileNumber : clientGroup?.mobileNumber,
            client ? client?.countryCode : clientGroup?.countryCode,
          ),
          // to: `91${client ? client?.mobileNumber : clientGroup?.mobileNumber}`,
          name: 'invoice_reminder_client',
          header: [
            {
              type: 'document',
              link: Location,
            },
          ],

          body: [
            clientName,
            clientName,
            invoiceNumber,
            invoiceDateFormat,
            grandTotal,
            invoiceDueDateFormat,
            statusText,
          ],
          fileName: `Invoice-${invoiceNumber}`,
          key: 'INVOICE_REMINDER_WHATSAPP',
        };

        await sendWhatsAppTemplateMessageUS(whatsappOptions);
      }
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  // @Cron(CronExpression.EVERY_HOUR)
  // async changeClientUserORganizationId() {
  //   const clientsUser = await createQueryBuilder(Client, 'client')
  //     .leftJoinAndSelect('client.organization', 'clientOrganization')
  //     .leftJoinAndSelect('client.user', 'user')
  //     .leftJoinAndSelect('user.organization', 'userOrganization')
  //     .select([
  //       'client.id',
  //       'client.email',
  //       'user.id',
  //       'clientOrganization.id',
  //       'userOrganization.id'
  //     ])
  //     .getMany();
  //   console.log(clientsUser.length);
  //   let count = [];
  //   for (let i of clientsUser) {
  //     if (i?.organization?.id) {
  //       if (i?.user && !i?.user?.organization) {
  //         console.log(i,i?.user,i?.user?.organization)
  //         console.log("Before", i?.user?.organization, i?.organization?.id);
  //         const user = await createQueryBuilder(User, 'user')
  //           .leftJoinAndSelect('user.organization', 'organization')
  //           .where('user.id = :id', { id: i?.user?.id })
  //           .getOne();
  //         const organization = await createQueryBuilder(Organization, 'organization')
  //         .where('organization.id = :id', { id: i?.organization?.id })
  //         .getOne();
  //         user.organization = organization;
  //         await user.save();
  //         console.log("after", user?.organization?.id, i?.organization?.id);
  //         count.push(i?.id);
  //       }
  //     }
  //   }
  //   console.log(count,count.length);
  // }
  async getOrganizationPreference(userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization },
    });
    return organizationPreferences;
  }

  // @Cron(CronExpression.EVERY_10_SECONDS)
  async sendOrganizationExpiryAlertMail() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const nextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
      const unexpiredOrganizations = await Organization.createQueryBuilder('organization')
        .leftJoinAndSelect('organization.users', 'user')
        .leftJoin('user.role', 'role')
        .where(
          "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') between :today AND :nextSeventhDay",
          { today, nextSeventhDay },
        )
        .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
        .andWhere('role.name = :role', { role: 'Admin' })
        .getMany();

      try {
        for (let organization of unexpiredOrganizations) {
          const {
            users: [user],
          } = organization;
          const config: any = organization?.config;
          // const orgExpiryDate = moment(config.expirydate, 'YYYY-MM-DD');
          // console.log("orgExpiryDate",orgExpiryDate)
          const expiryDate = new Date(config.expirydate);
          const now = new Date();
          const diffDays = Math.ceil(
            (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
          );

          const mailOptions = {
            data: {
              days: diffDays,
              legalName: organization?.legalName,
              userId: user?.id,
            },
            email: organization?.email,
            filePath: 'org-expire-admin',
            subject: '**Alert** | Action Required | Organization is About to Expire',
            key: 'TO_ADMIN',
            id: user?.id,
          };
          const msg = await sendnewMail(mailOptions);
        }

        return 'Cron Executed and mails sent Successfully to Admins!';
      } catch (error) {
        console.log(`Error in getting expiring organization records in cron:`, error);
      }
    } catch (error) {
      return console.log('getExpiringOrganizations ERROR', error);
    }
  }
}

export async function readLocalOrgDetails(existingUser) {
  try {
    let user = await User.findOne({ where: { id: existingUser }, relations: ['organization'] });

    const filePath = './orgdetails.json';

    const fileContent = fs.readFileSync(filePath, 'utf-8');

    const jsonData = JSON.parse(fileContent);

    const organizationFilter = jsonData.filter(
      (item: any) => item.organization === user.organization.id,
    );

    return organizationFilter;
  } catch (error) {
    console.error('Error reading JSON file:', error.message);
    return null;
  }
}

export async function writeLocalOrgDetails(data: any) {
  try {
    const filePath = './orgdetails.json';
    console.error('writing JSON file');
    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonData, 'utf-8');
  } catch (error) {
    console.error('Error writing JSON file:', error.message);
  }
}

export const getclientinvoicebilled = (payload: any) => {
  let sql = `SELECT 
  t.id, 
  t.name, 
  t.task_number AS tasknumber, 
  t.status AS status, 
  DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
  DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
  SUM(i.grand_total) AS amount,
  (SELECT COUNT(t2.id) 
   FROM task t2 
   LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
   WHERE t2.client_id = '${payload.clientId}'
       AND t2.status != 'deleted' 
       AND t2.status != 'terminated' 
       AND t2.payment_status = 'BILLED'
       AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
       AND t2.parent_task_id IS NULL
  ) AS total_count
FROM task t 
LEFT JOIN invoice i ON i.id = t.invoice_id
WHERE t.client_id = '${payload.clientId}'
  AND t.status != 'deleted' 
  AND t.status != 'terminated' 
  AND t.payment_status = 'BILLED'
  AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
  AND t.parent_task_id IS NULL 
GROUP BY t.id
LIMIT ${payload.pageCount} OFFSET ${payload.offset};`;

  let result = getManager().query(sql);

  return {
    result,
  };
};
