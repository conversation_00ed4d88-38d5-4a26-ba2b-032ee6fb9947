interface DscRegisterIssue {
    userId: number;
    issuedTo: string;
    clientId: number;
    clientGroupId: number;
}
interface DscRegisterReceive {
    userId: number;
    clientId: number;
    receivedBy: string;
    dscRegisterId: number;
}
export declare class DscRegisterListener {
    handleDscRegisterIssue(event: DscRegisterIssue): Promise<void>;
    handleDscRegisterReceive(event: DscRegisterReceive): Promise<void>;
}
export {};
