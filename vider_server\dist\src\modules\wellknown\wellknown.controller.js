"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WellknownController = void 0;
const common_1 = require("@nestjs/common");
let WellknownController = class WellknownController {
    constructor() { }
    async getWellknown() {
        return {
            "associatedApplications": [
                {
                    "applicationId": "085cd4bb-b2ec-4791-9f4f-5b46c5818f9b"
                }
            ]
        };
    }
};
__decorate([
    (0, common_1.Get)('microsoft-identity-association.json'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WellknownController.prototype, "getWellknown", null);
WellknownController = __decorate([
    (0, common_1.Controller)('.well-known'),
    __metadata("design:paramtypes", [])
], WellknownController);
exports.WellknownController = WellknownController;
//# sourceMappingURL=wellknown.controller.js.map