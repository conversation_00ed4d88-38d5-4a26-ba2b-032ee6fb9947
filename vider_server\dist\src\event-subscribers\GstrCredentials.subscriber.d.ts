import { Connection, EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
export declare class GstrCredentialsSubscriber implements EntitySubscriberInterface<GstrCredentials> {
    private readonly connection;
    constructor(connection: Connection);
    listenTo(): typeof GstrCredentials;
    beforeUpdate(event: UpdateEvent<GstrCredentials>): Promise<void>;
    afterInsert(event: InsertEvent<GstrCredentials>): Promise<void>;
    afterUpdate(event: UpdateEvent<GstrCredentials>): Promise<void>;
}
