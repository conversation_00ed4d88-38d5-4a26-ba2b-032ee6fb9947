import GstrCredentials from '../entity/gstrCredentials.entity';
import * as ExcelJS from 'exceljs';
import { GstrOutstandingDemand } from '../entity/gstrDemands.entity';
export declare class GstrDashboardService {
    getNoticeAndOrdersIssueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'date_of_issuance', query: any, userId: any, ViewAll: any, ViewAssigned: any): Promise<number>;
    getAddNoticeAndOrderIssueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'category_date', query: any, userId: any, ViewAll: any, ViewAssigned: any): Promise<number>;
    getNoticeAndOrderDueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'due_date', query: any, userId: any, ViewAll: any, ViewAssigned: any): Promise<number>;
    getAddNoticeAndOrderDueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'due_date', query: any, userId: any, ViewAll: any, ViewAssigned: any): Promise<number>;
    getNoticeOrdersDateCount(userId: number, query: any): Promise<{
        issuanceDate: {
            last1Week: number;
            last15Days: number;
            last1Month: number;
            today: number;
        };
        dueDate: {
            nxt1Week: number;
            nxt15Days: number;
            nxt1Month: number;
            today: number;
        };
    }>;
    getAdditionalNoticeOrdersDateCount(userId: number, query: any): Promise<{
        issuanceDate: {
            last1Week: number;
            last15Days: number;
            last1Month: number;
            today: number;
        };
        dueDate: {
            nxt1WeekDue: number;
            nxt15DaysDue: number;
            nxt1MonthDue: number;
            today: number;
        };
    }>;
    getAddNoticeAndOrdersStats(organizationId: number, status: 'open' | 'closed' | 'replied' | 'notReplied', query: any, userId: any, ViewAll: any, ViewAssigned: any): Promise<number>;
    getAdditionalNoticesStatCount(userId: number, query: any): Promise<{
        stats: {
            open: number;
            closed: number;
            replied: number;
            notReplied: number;
        };
    }>;
    getGstrConfigStatus(userId: any, query: Date): Promise<{
        totalLimit: any;
        difference: number;
        presentClients: number;
    }>;
    clientCheck(userId: number, queryy: any): Promise<{
        queryResult: GstrCredentials[];
        totalClients: number;
        count: number;
        totalCount: number;
    }>;
    exportGstrInvalid(userId: number, query: any): Promise<ExcelJS.Buffer>;
    outstandingDemandStats(userId: number, query: any): Promise<{
        result: GstrOutstandingDemand[];
    }>;
    getAdditionalNoticeStats(userId: number, query: any): Promise<{
        noticesByCaseType: Record<string, {
            openReplied: number;
            openNotReplied: number;
            closedReplied: number;
            closedNotReplied: number;
            totalNotices: number;
            totalOrders: number;
        }>;
        totals: {
            totalNotices: number;
            totalOrders: number;
        };
    }>;
}
