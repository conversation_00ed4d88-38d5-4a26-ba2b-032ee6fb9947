"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendnewMailToBusinessTeam = exports.sendnewMail = exports.sendMailViaAny = void 0;
const ejs = require("ejs");
const nodemailer = require("nodemailer");
const puppeteer_1 = require("puppeteer");
const notifications_preferences_entity_1 = require("../modules/notification-settings/notifications-preferences.entity");
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../modules/users/entities/user.entity");
let transporter = nodemailer.createTransport({
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
});
async function sendMailViaAny(data) {
    if (data.userId) {
        try {
            let user = await user_entity_1.User.findOne({ where: { id: data.userId }, relations: ['organization'] });
            let orgdetails = {};
            if (data.subject.includes('Invoice Reminder') ||
                data.subject.includes('Invoice for Services Rendered') ||
                data.subject.includes('Confirmation of Payment Received for Service Rendered')) {
                if (user.organization.billingSmtp) {
                    orgdetails['smtp'] = user.organization.billingSmtp
                        ? user.organization.billingSmtp[1]
                        : null;
                }
                else {
                    orgdetails['smtp'] = user.organization.othersSmtp
                        ? user.organization.othersSmtp[1]
                        : null;
                }
            }
            else if (data.subject.includes('Secure Documents Upload Requested for Service') ||
                data.subject.includes('Updated Secure Documents Upload Link')) {
                if (user.organization.iproSmtp) {
                    orgdetails['smtp'] = user.organization.iproSmtp ? user.organization.iproSmtp[1] : null;
                }
                else {
                    orgdetails['smtp'] = user.organization.othersSmtp
                        ? user.organization.othersSmtp[1]
                        : null;
                }
            }
            else if (data.subject.includes('Receipt of your DSC Token') ||
                data.subject.includes('Issue of your DSC Token') ||
                data.subject.includes('Welcome to Your ATOM Client Portal!')) {
                if (user.organization.clientSmtp) {
                    orgdetails['smtp'] = user.organization.clientSmtp
                        ? user.organization.clientSmtp[1]
                        : null;
                }
                else {
                    orgdetails['smtp'] = user.organization.othersSmtp
                        ? user.organization.othersSmtp[1]
                        : null;
                }
            }
            else if (data.fromTemplate) {
                if (user.organization.broadcastSmtp) {
                    orgdetails['smtp'] = user.organization.broadcastSmtp
                        ? user.organization.broadcastSmtp[1]
                        : null;
                }
                else {
                    orgdetails['smtp'] = user.organization.othersSmtp
                        ? user.organization.othersSmtp[1]
                        : null;
                }
            }
            else {
                orgdetails['smtp'] = user.organization.othersSmtp ? user.organization.othersSmtp[1] : null;
            }
            let res = await this.CustomMailService.customsendmailthrottled(Object.assign({ to: data.to, org: orgdetails, data: data }, (data.attachments && { attachments: data.attachments })));
            return res;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error);
        }
    }
}
exports.sendMailViaAny = sendMailViaAny;
async function invoiceBuffer(invoiceId, type) {
    let url = type === 'INVOICE'
        ? `${process.env.WEBSITE_URL}/billing/invoices/${invoiceId}/preview?fromApi=true`
        : `${process.env.WEBSITE_URL}/billing/proforma/${invoiceId}/preview?fromApi=true`;
    const options = {
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
    };
    try {
        const browser = await puppeteer_1.default.launch(options);
        const page = await browser.newPage();
        await page.setCacheEnabled(false);
        await page.setUserAgent('Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36');
        const maxRetries = 3;
        let retries = 0;
        let loaded = false;
        while (retries < maxRetries && !loaded) {
            try {
                await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });
                await page.waitForFunction('document.querySelector("body").innerText.includes("Powered by")', { timeout: 60000 });
                await page.waitForFunction('Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)', { timeout: 60000 });
                loaded = true;
            }
            catch (error) {
                retries += 1;
                console.log(`Retrying to load the page (${retries}/${maxRetries})`);
                if (retries >= maxRetries) {
                    throw error;
                }
            }
        }
        await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
        await page.addStyleTag({ content: '.hide { display: none }' });
        await page.emulateMediaType('print');
        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0px',
                right: '0px',
                bottom: '0px',
                left: '0px',
            },
            scale: 0.6,
        });
        await browser.close();
        return pdf;
    }
    catch (error) {
        console.error('Failed to download the invoice:', error);
        throw new common_1.InternalServerErrorException('Failed to download the invoice');
    }
}
async function sendnewMail({ data, email, filePath, subject, key, id, clientMail, invoiceId = null, type = 'INVOICE', }) {
    if (subject === 'Reminder - Invitation to join | Vider' ||
        subject === 'Organization Registration - Vider' ||
        subject === "User Signup | Vider's ATOM" ||
        subject === 'Reset Password | Vider' ||
        subject === 'Invitation to join | Vider' ||
        key === 'TIMESHEET' ||
        key === 'MFA_SEND_OTP') {
        return new Promise((resolve, reject) => {
            let html = '';
            if (filePath == '') {
                html = data;
            }
            else {
                let templatePath = `src/emails/templates/${filePath}.ejs`;
                let templateStr = ejs.fileLoader(templatePath);
                let template = ejs.compile(templateStr.toString());
                html = filePath == '' ? data : template(data);
            }
            let mailOptions = {
                from: {
                    name: 'Vider',
                    address: process.env.FROM_EMAIL,
                },
                to: email,
                subject: subject,
                html: html,
            };
            transporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(error);
                    reject(error);
                }
                else {
                    resolve(info.response);
                }
            });
        });
    }
    else if (clientMail === 'ORGANIZATION_CLIENT_EMAIL') {
        return new Promise(async (resolve, reject) => {
            let html = '';
            if (filePath == '') {
                html = data;
            }
            else {
                let templatePath = `src/emails/templates/${filePath}.ejs`;
                try {
                    let templateStr = ejs.fileLoader(templatePath);
                    let template = ejs.compile(templateStr.toString());
                    html = template(data);
                }
                catch (error) {
                    console.error('Error while loading or compiling the template: ', error);
                }
            }
            let mailOptions = Object.assign({ from: {
                    name: 'Vider',
                    address: process.env.FROM_EMAIL,
                }, to: email, subject: subject, html: html, userId: data.userId }, (invoiceId && {
                attachments: [
                    {
                        filename: `Invoice_${data === null || data === void 0 ? void 0 : data.invoiceNumber}.pdf`,
                        content: await invoiceBuffer(invoiceId, type),
                    },
                ],
            }));
            sendMailViaAny(mailOptions)
                .then(() => {
                console.log('Client Email sent successfully');
                resolve('Email sent successfully');
            })
                .catch((error) => {
                reject(`Error sending email: ${error}`);
            });
        });
    }
    else if (key === "TO_ADMIN") {
        setTimeout(() => {
            const sendEmail = async () => {
                try {
                    let html = '';
                    if (filePath === '') {
                        html = data;
                    }
                    else {
                        const templatePath = `src/emails/templates/${filePath}.ejs`;
                        const templateStr = ejs.fileLoader(templatePath);
                        const template = ejs.compile(templateStr.toString());
                        html = template(data);
                    }
                    const mailOptions = {
                        from: {
                            name: 'Vider',
                            address: process.env.FROM_EMAIL,
                        },
                        to: email,
                        subject: subject,
                        html: html,
                        userId: data.userId,
                    };
                    await sendMailViaAny(mailOptions);
                    console.log('Email sent successfully');
                }
                catch (error) {
                    console.error('Error sending email:', error);
                }
            };
            sendEmail();
        }, 100);
    }
    else {
        const userPresenentCheck = await notifications_preferences_entity_1.default.findOne({
            where: { user: id },
        });
        if (userPresenentCheck) {
            if (userPresenentCheck === null || userPresenentCheck === void 0 ? void 0 : userPresenentCheck.email) {
                const emailKeys = Object.keys(userPresenentCheck === null || userPresenentCheck === void 0 ? void 0 : userPresenentCheck.email);
                const emailKeysInPush = Object.keys(userPresenentCheck === null || userPresenentCheck === void 0 ? void 0 : userPresenentCheck.push);
                const checkKeyPresent = emailKeys.includes(key) || emailKeysInPush.includes(key);
                if (checkKeyPresent) {
                    setTimeout(() => {
                        const sendEmail = async () => {
                            try {
                                let html = '';
                                if (filePath === '') {
                                    html = data;
                                }
                                else {
                                    const templatePath = `src/emails/templates/${filePath}.ejs`;
                                    const templateStr = ejs.fileLoader(templatePath);
                                    const template = ejs.compile(templateStr.toString());
                                    html = template(data);
                                }
                                const mailOptions = {
                                    from: {
                                        name: 'Vider',
                                        address: process.env.FROM_EMAIL,
                                    },
                                    to: email,
                                    subject: subject,
                                    html: html,
                                    userId: data.userId,
                                };
                                await sendMailViaAny(mailOptions);
                                console.log('Email sent successfully');
                            }
                            catch (error) {
                                console.error('Error sending email:', error);
                            }
                        };
                        sendEmail();
                    }, 100);
                }
            }
        }
    }
}
exports.sendnewMail = sendnewMail;
async function sendnewMailToBusinessTeam({ data, email, filePath, subject, key, id, clientMail, }) {
    if (subject === 'New Organization On-boarded to VIDER' ||
        subject === 'New User On-boarded to VIDER' ||
        subject === 'New Request for Quantum Subscription in ATOM' ||
        subject === 'Atom Pro Machines Status' ||
        subject === 'Atom Pro Daily Complete Status' ||
        subject === 'Expiring and Expired Organization Report') {
        return new Promise((resolve, reject) => {
            let html = '';
            if (filePath == '') {
                html = data;
            }
            else {
                let templatePath = `src/emails/templates/${filePath}.ejs`;
                let templateStr = ejs.fileLoader(templatePath);
                let template = ejs.compile(templateStr.toString());
                html = filePath == '' ? data : template(data);
            }
            let mailOptions = {
                from: {
                    name: 'Vider',
                    address: process.env.FROM_EMAIL,
                },
                to: email,
                subject: subject,
                html: html,
            };
            transporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(error);
                    reject(error);
                }
                else {
                    resolve(info.response);
                }
            });
        });
    }
}
exports.sendnewMailToBusinessTeam = sendnewMailToBusinessTeam;
//# sourceMappingURL=newemails.js.map