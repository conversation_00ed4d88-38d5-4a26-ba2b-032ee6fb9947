import { GstrConfigService } from '../service/config.services';
export declare class GstrConfigController {
    private service;
    constructor(service: GstrConfigService);
    gstAtomClient(req: any, id: number): Promise<import("../../clients/entity/client.entity").default>;
    disableAtomProGstrClient(body: any, req: any): Promise<void>;
    disableGstrSingleClient(req: any, id: number): Promise<void>;
    getDeletedGstrClients(req: any, query: any): Promise<{
        count: number;
        result: import("../entity/gstrCredentials.entity").default[];
    }>;
    exportdeletedGstClient(req: any, body: any): Promise<import("exceljs").Buffer>;
    enableGstrClient(req: any, id: number): Promise<void>;
    enableBulkGstrClient(req: any, body: any): Promise<void>;
    getBulkSyncStatus(req: any): Promise<string>;
    enableStatus(req: any): Promise<void>;
    updateDisableStatus(req: any): Promise<void>;
    organizationScheduling(req: any, body: any): Promise<string>;
    createNoticeAndOrderItem(req: any, body: any): Promise<void>;
    updateNoticeAndOrder(req: any, body: any): Promise<boolean>;
    deleteNoticeAndOrder(req: any, id: number): Promise<boolean>;
    createAdditionalNotice(req: any, body: any): Promise<void>;
    updateAdditionalNotice(req: any, body: any): Promise<boolean>;
    deleteAdditionalNotice(req: any, id: number): Promise<boolean>;
    completeTaskGstrOne(req: any, body: any): Promise<{
        gstCompletedTasks: import("../../tasks/entity/task.entity").default[];
        successRows: any;
    }>;
}
