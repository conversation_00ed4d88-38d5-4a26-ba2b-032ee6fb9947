import { CreatePosterDto } from './dto/create-poster.dto';
import { PosterService } from './poster.service';
export declare class PosterController {
    private service;
    constructor(service: PosterService);
    getEvents(request: any, query: any): Promise<import("./poster-config.entity").default>;
    updateImage(req: any, body: any): Promise<void>;
    createPoster(body: CreatePosterDto, request: any): Promise<import("./poster-config.entity").default>;
    getPosterEventTypes(): Promise<any[]>;
    getPosterEventsByType(typeName: string): Promise<{
        id: any;
        name: any;
        date: any;
    }[]>;
    createTemplate1(body: any, request: any): Promise<{
        template1: {
            imageBase64: string;
        };
    }>;
    createTemplate2(body: any, request: any): Promise<{
        template2: {
            imageBase64: string;
        };
    }>;
    createTemplate3(body: any, request: any): Promise<{
        template3: {
            imageBase64: string;
        };
    }>;
    getPosters(query: any): Promise<import("../storage/storage.entity").default[]>;
}
