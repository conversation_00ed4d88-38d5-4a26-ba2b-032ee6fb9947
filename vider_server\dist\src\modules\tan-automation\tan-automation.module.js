"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanAutomationModule = void 0;
const common_1 = require("@nestjs/common");
const tan_automation_controller_1 = require("./controller/tan-automation.controller");
const tan_automation_service_1 = require("./service/tan-automation.service");
const tan_client_credentials_entity_1 = require("./entity/tan-client-credentials.entity");
const typeorm_1 = require("@nestjs/typeorm");
const tan_profile_entity_1 = require("./entity/tan-profile.entity");
const tan_e_challan_entity_1 = require("./entity/tan-e-challan.entity");
const tan_income_tax_forms_entity_1 = require("./entity/tan-income-tax-forms.entity");
const tan_my_cas_entity_1 = require("./entity/tan-my-cas.entity");
const tan_sync_service_1 = require("./service/tan-sync.service");
const tan_sync_controller_1 = require("./controller/tan-sync.controller");
const tan_update_tracker_entity_1 = require("./entity/tan-update-tracker.entity");
const tan_key_person_entity_1 = require("./entity/tan-key-person.entity");
const tan_dashboard_service_1 = require("./service/tan-dashboard-service");
const tan_dashboard_controller_1 = require("./controller/tan-dashboard.controller");
const TanClientCredentials_subscriber_1 = require("../../event-subscribers/TanClientCredentials.subscriber");
const tan_communication_inbox_entity_1 = require("./entity/tan-communication-inbox.entity");
const tan_temp_epro_fya_entity_1 = require("./entity/tan_temp_epro_fya.entity");
const tan_temp_epro_fyi_entity_1 = require("./entity/tan_temp_epro_fyi.entity");
const tan_cron_controller_1 = require("./controller/tan-cron.controller");
const tan_cron_service_1 = require("./service/tan-cron.service");
const trace_outstanding_deman_entity_1 = require("./entity/trace-outstanding-deman.entity");
let TanAutomationModule = class TanAutomationModule {
};
TanAutomationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                tan_client_credentials_entity_1.default,
                tan_profile_entity_1.default,
                tan_e_challan_entity_1.default,
                tan_income_tax_forms_entity_1.default,
                tan_my_cas_entity_1.default,
                tan_update_tracker_entity_1.default,
                tan_key_person_entity_1.default,
                tan_communication_inbox_entity_1.default,
                tan_temp_epro_fya_entity_1.default,
                tan_temp_epro_fyi_entity_1.default,
                trace_outstanding_deman_entity_1.default
            ]),
        ],
        controllers: [tan_automation_controller_1.TanAutomationController, tan_sync_controller_1.TanSyncController, tan_dashboard_controller_1.TanDashboardController, tan_cron_controller_1.TanCronController],
        providers: [
            tan_automation_service_1.TanAutomationService,
            tan_sync_service_1.TanSyncService,
            tan_dashboard_service_1.TanDashboardService,
            TanClientCredentials_subscriber_1.TanClientCredentialsSubscriber,
            tan_cron_service_1.TanCronService
        ],
    })
], TanAutomationModule);
exports.TanAutomationModule = TanAutomationModule;
//# sourceMappingURL=tan-automation.module.js.map