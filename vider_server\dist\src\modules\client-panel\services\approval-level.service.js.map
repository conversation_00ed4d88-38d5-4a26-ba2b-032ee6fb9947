{"version": 3, "file": "approval-level.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/client-panel/services/approval-level.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,kEAA8D;AAC9D,2GAAkG;AAClG,qCAA6C;AAC7C,gEAAwD;AACxD,iCAA0B;AAC1B,iDAA6D;AAKtD,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAE/B,KAAK,CAAC,IAAI,CAAC,MAAc;QACvB,IAAI,IAAI,GAAG,MAAM,kBAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACtF,IAAI,MAAM,GAAG,MAAM,IAAA,4BAAkB,EAAC,oCAAkB,EAAE,oBAAoB,CAAC;aAC5E,QAAQ,CAAC,iCAAiC,EAAE,cAAc,CAAC;aAC3D,iBAAiB,CAAC,6BAA6B,EAAE,UAAU,CAAC;aAC5D,iBAAiB,CAAC,yBAAyB,EAAE,eAAe,CAAC;aAC7D,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;aAClE,QAAQ,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;aACjE,OAAO,EAAE,CAAC;QACb,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAS;QACvB,IAAI,IAAI,GAAG,MAAM,qBAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,MAAM,GAAQ,EAAE,CAAC;YAErB,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAKtE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;SAC5C;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;;QAC3C,IAAI;YACF,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,8BAA8B,EAAE,EAAE,CAAC;YACzE,IAAI,IAAI,GAAQ;gBACd,MAAM,EAAE,KAAK;gBACb,aAAa,EAAE,QAAQ;gBACvB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,MAAM,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,KAAK,CAAC,MAAM,CACzC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,6BAA6B,CACtD,CAAC;YACF,MAAM,aAAa,GAAG,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,IAAI,0CAAE,aAAa,CAAC;YAC1D,MAAM,sBAAsB,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAKhF,IAAI,WAAW,GAAQ,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;gBACvC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,IAAI,SAAS,IAAI,MAAM,EAAE;gBAC5B,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;gBAC5B,IAAI,cAAc,GAAQ,EAAE,CAAC;gBAC7B,IAAI,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,IAAI,0CAAE,aAAa,EAAE;oBACvC,cAAc,GAAG,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,CACnC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;wBACjB,MAAM,IAAI,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CACzB,CAAC,IAAI,EAAE,EAAE,CACP,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,IAAI;4BACnB,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,MAAK,UAAU;gCAC1B,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,MAAK,eAAe;gCAChC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,MAAK,UAAU,CAAC,CACjC,CAAC;wBAEF,IAAI,IAAI,EAAE;4BACR,OAAO;gCACL,aAAa,EAAE,IAAI,CAAC,aAAa;gCACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gCACf,MAAM,EAAE,IAAI,CAAC,MAAM;6BACpB,CAAC;yBACH;wBAED,OAAO,IAAI,CAAC;oBACd,CAAC,EACA,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;iBACpC;gBACD,IAAI,MAAM,IAAI,sBAAc,CAAC,YAAY,EAAE;oBACzC,cAAc,CAAC,IAAI,CAAC;wBAClB,aAAa,EAAE,SAAS,CAAC,aAAa;wBACtC,QAAQ,EAAE,SAAS,CAAC,QAAQ;wBAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,MAAM,EAAE,SAAS,CAAC,MAAM;qBACzB,CAAC,CAAC;iBACJ;gBAED,IAAI,MAAM,KAAK,sBAAc,CAAC,YAAY,EAAE;oBAC1C,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;wBACvB,cAAc,CAAC,IAAI,CAAC;4BAClB,aAAa,EAAE,SAAS,CAAC,aAAa;4BACtC,QAAQ,EAAE,SAAS,CAAC,QAAQ;4BAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,MAAM,EAAE,SAAS,CAAC,MAAM;yBACzB,CAAC,CAAC;qBACJ;iBACF;gBAED,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;gBAC1C,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC7B;YAED,WAAW,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE;gBAClC,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnD,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAEnD,IAAI,SAAS,GAAG,SAAS,EAAE;oBACzB,OAAO,CAAC,CAAC,CAAC;iBACX;qBAAM,IAAI,SAAS,GAAG,SAAS,EAAE;oBAChC,OAAO,CAAC,CAAC;iBACV;qBAAM;oBACL,OAAO,CAAC,CAAC;iBACV;YACH,CAAC,CAAC,CAAC;YACH,OAAO,WAAW,CAAC;SACpB;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACpB;IACH,CAAC;IAID,KAAK,CAAC,sBAAsB,CAAC,EAAU,EAAE,KAAa;;QACpD,IAAI;YACF,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,8BAA8B,EAAE,EAAE,CAAC;YACzE,IAAI,IAAI,GAAQ;gBACd,MAAM,EAAE,KAAK;gBACb,aAAa,EAAE,QAAQ;gBACvB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,MAAM,GAAG,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,KAAK,0CAAE,MAAM,CAC1C,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,6BAA6B,CAC3D,CAAC;YACF,IAAI,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,MAAM,EAAE;gBACrB,MAAM,GAAG,GAAG,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC;aACZ;iBAAM;gBACL,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,MAAM,YAAY,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC1C,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;gBACxD,MAAM,SAAS,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;gBAC3F,OAAO,SAAS,CAAC;aAClB;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACpB;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,IAA4B,EAAE,MAAc;;QACpE,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAE7D,IAAI,IAAI,GAAG,MAAM,qBAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,IAAI,GAAG,MAAM,kBAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,mBAAmB,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,oCAAkB,CAAC,OAAO,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;gBACtE,SAAS,EAAE,CAAC,UAAU,EAAE,yBAAyB,EAAE,8BAA8B,CAAC;gBAClF,MAAM,EAAE,CAAC,IAAI,CAAC;aACf,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;YACnC,IAAI;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;oBAC1B,UAAU,EAAE,wBAAwB;oBACpC,QAAQ,EAAE;wBACR,cAAc,EAAE,WAAW;wBAC3B,iBAAiB,EAAE,QAAQ,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,0CAAE,cAAc,CAAC,MAAM,iBAAiB;qBACtF;iBACF,CAAC,CAAC;gBAEH,IAAI,MAAM,GAAQ;oBAChB,MAAM,EAAE,MAAM;oBACd,aAAa,EAAE,QAAQ;oBACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,4BAA4B;oBAC3D,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;qBACnC;oBACD,IAAI,EAAE,IAAI;iBACX,CAAC;gBAEF,MAAM,eAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;;oBAClD,MAAM,iBAAiB,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,iBAAiB,CAAC;oBAE5D,IAAI,iBAAiB;wBACnB,IAAI,CAAC,cAAc,GAAG;4BACpB;gCACE,MAAM,EAAE,oBAAoB,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,0CAAE,cAAc,CAAC,MAAM,GAAG;gCACxE,SAAS,EAAE,KAAK;6BACjB;yBACF,CAAC;oBACJ,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;oBAC3C,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,8CAA8C,GAAG,EAAE,CAAC,CAAC;aAClE;SACF;QACD,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAElB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU,EAAE,YAAgC;;QAChE,IAAI;YACF,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,8BAA8B,EAAE,EAAE,CAAC;YACzE,IAAI,IAAI,GAAQ;gBACd,MAAM,EAAE,KAAK;gBACb,aAAa,EAAE,QAAQ;gBACvB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC;YAClC,MAAM,EAAE,cAAc,EAAE,GAAG,QAAQ,CAAC;YACpC,IAAI,mBAAmB,GAAU,EAAE,CAAC;YACpC,mBAAmB,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,KAAK,CACxC,MAAM,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,6BAA6B,EACjE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE;gBACjB,IAAI,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC;gBAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;gBAC3B,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;gBAEpB,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,2BAA2B,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC;YACrF,CAAC,CAAC,CAAC;YAEL,MAAM,WAAW,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;gBAChC,IAAI;oBACF,IAAI,MAAM,GAAQ;wBAChB,MAAM,EAAE,KAAK;wBACb,aAAa,EAAE,QAAQ;wBACvB,OAAO,EAAE;4BACP,cAAc,EAAE,kBAAkB;yBACnC;qBACF,CAAC;oBAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBAC9C,OAAO,QAAQ,CAAC,IAAI,CAAC;iBACtB;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5C,MAAM,KAAK,CAAC;iBACb;YACH,CAAC,CAAC;YAEF,MAAM,WAAW,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjF,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;iBACrB,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,CAAC;iBAC3B,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAClB;IACH,CAAC;CACF,CAAA;AA9QY,oBAAoB;IADhC,IAAA,mBAAU,GAAE;GACA,oBAAoB,CA8QhC;AA9QY,oDAAoB"}