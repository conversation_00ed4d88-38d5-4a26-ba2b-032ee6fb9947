import { <PERSON>, Post, Body } from '@nestjs/common';
import { EmailThrottleService } from './email-throttle.service';

@Controller('email-throttle')
export class EmailThrottleController {
  constructor(private readonly emailThrottleService: EmailThrottleService) {}

  @Post('queue')
  async queueEmails(@Body() body: { to: string; subject: string; body: string }) {
    await this.emailThrottleService.enqueueEmail(body.to, body.subject, body.body);
    return { message: `Queued email to ${body.to}` };
  }

  // Optional: send multiple at once
  @Post('bulk-queue')
  async queueBulkEmails(
    @Body() body: { emails: { to: string; subject: string; body: string }[] },
  ) {
    for (const e of body.emails) {
      await this.emailThrottleService.enqueueEmail(e.to, e.subject, e.body);
    }
    return { message: `${body.emails.length} emails queued.` };
  }
}
