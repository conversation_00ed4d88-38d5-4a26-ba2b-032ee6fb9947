/// <reference types="multer" />
/// <reference types="node" />
/// <reference types="node" />
import { S3 } from 'aws-sdk';
import * as ExcelJS from 'exceljs';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { EmailThrottleService } from '../email-throttle/email-throttle.service';
export declare class CommonService {
    emailThrottleService: EmailThrottleService;
    constructor(emailThrottleService: EmailThrottleService);
    getclientinvoicebilled(clientId: any, payload: any): Promise<{
        result: any;
    }>;
    exportClientBilledTasks(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    exportClientGroupBilledTasks(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    getclientinvoiceexport(clientId: any, payload: any): Promise<{
        result: any;
    }>;
    exportClientInvoiceReport(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    exportClientGroupInvoiceReport(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    getclientinvoiceUnbilled(clientId: any, payload: any): Promise<{
        result: any;
    }>;
    exportUnClientBilledTasks(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    exportUnClientGroupUnBilledTasks(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    getclientinvoiceOverView(orgId: any, payload: any, ViewAll: any, ViewAssigned: any, user: any): Promise<{
        result: any;
    }>;
    exportClientOverviewlist(orgId: any, payload: any, ViewAll: any, ViewAssigned: any, user: any): Promise<ExcelJS.Buffer>;
    exportClientGroupOverviewlist(orgId: any, payload: any, user: any, ViewAll: any, ViewAssigned: any): Promise<ExcelJS.Buffer>;
    getClientReceiptsReport(clientId: any, payload: any): Promise<{
        result: any;
    }>;
    exportClientReceipts(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    exportClientGroupReceipts(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    upload(file: Express.Multer.File): Promise<unknown>;
    uploadInvoice(data: any): Promise<void>;
    uploadReceipt(data: any): Promise<void>;
    uploadReceiptForEdit(data: any): Promise<void>;
    uploadProformaInvoice(data: any): Promise<void>;
    uploadInvoiceForEdit(data: any): Promise<void>;
    uploadProformaInvoiceForEdit(data: any): Promise<void>;
    summaryReport(): Promise<void>;
    uploadS3(file: Buffer, bucket: string, name: string, contentType?: string): Promise<unknown>;
    getS3(): S3;
    synccalender(data: any): Promise<boolean>;
    addTaskDataForReport(data: any): Promise<any>;
    synccalenderstatus(data: any): Promise<{
        message: string;
        completed: boolean;
    }>;
    exportClientProformaInvoiceReport(clientId: any, payload: any): Promise<ExcelJS.Buffer>;
    getclientproformainvoiceexport(clientId: any, payload: any): Promise<{
        result: any;
    }>;
    sendTrailExpiredMessage(): Promise<void>;
    sendSubscriptionMail(): Promise<void>;
    GetReport(): Promise<void>;
    sendCronEmail(): Promise<void>;
    addSchedulingOrganization(): Promise<void>;
    disableWhatsappForExpiredOrganizations(): Promise<void>;
    InvoiceReminder(data: any, userId: any): Promise<void>;
    getOrganizationPreference(userId: any): Promise<OrganizationPreferences>;
    sendOrganizationExpiryAlertMail(): Promise<void | "Cron Executed and mails sent Successfully to Admins!">;
}
export declare function readLocalOrgDetails(existingUser: any): Promise<any>;
export declare function writeLocalOrgDetails(data: any): Promise<void>;
export declare const getclientinvoicebilled: (payload: any) => {
    result: Promise<any>;
};
