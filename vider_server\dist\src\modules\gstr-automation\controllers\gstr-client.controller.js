"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrClientController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
const gstr_client_service_1 = require("../service/gstr-client.service");
let GstrClientController = class GstrClientController {
    constructor(service) {
        this.service = service;
    }
    async getGstrClients(req, query) {
        const { userId } = req.user;
        return this.service.getGstrClients(userId, query);
    }
    async exportGstrClient(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportGstrClient(userId, query);
    }
    async getAtomClients(req, query) {
        const { userId } = req.user;
        return this.service.getAtomClients(userId, query);
    }
    addClientAutCredentials(body, req) {
        const { userId } = req.user;
        return this.service.addGstrCredentials(userId, body);
    }
    update(id, body, req) {
        const { userId } = req.user;
        return this.service.updateGstrCredentials(id, body, userId);
    }
    createGsrRequest(req, id, body) {
        const { userId } = req.user;
        return this.service.createGsrRequest(userId, id, body);
    }
    bulkAutomationSync(body, req) {
        const { userId } = req.user;
        return this.service.bulkGstrSync(userId, body);
    }
    getActivityLog(req, id, query) {
        const { userId } = req.user;
        return this.service.getActivityLog(id, query, userId);
    }
    getActivityArchiveLog(req, id, query) {
        const { userId } = req.user;
        return this.service.getActivityArchiveLog(id, query, userId);
    }
    getclientSyncStatus(id, req) {
        const { userId } = req.user;
        return this.service.getclientSyncStatus(id, userId);
    }
    async getCaseIdBasedClientNotices(id, req, query) {
        const { userId } = req.user;
        return this.service.getCaseIdBasedClientNotices(id, userId, query);
    }
    async exportCaseBasedNotices(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportCaseBasedNotices(userId, query);
    }
    async getCaseIdBasedOrgNotices(req, query) {
        const { userId } = req.user;
        return this.service.getCaseIdBasedOrgNotices(userId, query);
    }
    async exportCasebasedOrgNotices(req, body) {
        const { userId } = req === null || req === void 0 ? void 0 : req.user;
        const query = body;
        return this.service.exportCasebasedOrgNotices(userId, query);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "getGstrClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/gstr-client-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "exportGstrClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('clients'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "getAtomClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('credentials'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "addClientAutCredentials", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('credentials/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "update", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('gstr-request/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "createGsrRequest", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('bulkSync'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "bulkAutomationSync", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('activity/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "getActivityLog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('activityArchive/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "getActivityArchiveLog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('syncStatus/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], GstrClientController.prototype, "getclientSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('case-id/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "getCaseIdBasedClientNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/export-casenotices'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "exportCaseBasedNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('case-ids'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "getCaseIdBasedOrgNotices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/gstr-casebased-export'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GstrClientController.prototype, "exportCasebasedOrgNotices", null);
GstrClientController = __decorate([
    (0, common_1.Controller)('gstr-client'),
    __metadata("design:paramtypes", [gstr_client_service_1.GstrClientService])
], GstrClientController);
exports.GstrClientController = GstrClientController;
//# sourceMappingURL=gstr-client.controller.js.map