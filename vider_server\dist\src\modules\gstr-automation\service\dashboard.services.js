"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrDashboardService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const gstrCredentials_entity_1 = require("../entity/gstrCredentials.entity");
const organization_preferences_entity_1 = require("../../organization-preferences/entity/organization-preferences.entity");
const aut_client_credentials_entity_1 = require("../../automation/entities/aut_client_credentials.entity");
const permission_1 = require("../../tasks/permission");
const ExcelJS = require("exceljs");
const gstrDemands_entity_1 = require("../entity/gstrDemands.entity");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
const aut_income_tax_eproceedings_fyi_notice_response_fya_entity_1 = require("../../automation/entities/aut_income_tax_eproceedings_fyi_notice_response_fya.entity");
let GstrDashboardService = class GstrDashboardService {
    async getNoticeAndOrdersIssueDates(organizationId, interval, dateColumn, query, userId, ViewAll, ViewAssigned) {
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 0 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let dateStr = '';
            if (interval === 'today') {
                dateStr = `STR_TO_DATE(${dateColumn}, '%d/%m/%Y') = CURDATE()`;
            }
            else {
                dateStr = `STR_TO_DATE(${dateColumn}, '%d/%m/%Y') >= DATE_SUB(CURDATE(), ${intervalQuery})`;
            }
            let sql = `
        SELECT COUNT(DISTINCT gstr_notice_orders.id) AS count
        FROM gstr_notice_orders
        LEFT JOIN client ON gstr_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${dateStr} AND gstr_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${user_entity_1.UserStatus.DELETED}'
        AND gstr_credentials.status != '${gstrCredentials_entity_1.GstrStatus.DISABLE}'
      `;
            if (ViewAssigned && !ViewAll) {
                sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt(result[0].count);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getAddNoticeAndOrderIssueDates(organizationId, interval, dateColumn, query, userId, ViewAll, ViewAssigned) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 0 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let dateStr = '';
            if (interval === 'today') {
                dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') = CURDATE()`;
            }
            else {
                dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') >= DATE_SUB(CURDATE(), ${intervalQuery})`;
            }
            let sql = `
        SELECT COUNT(DISTINCT(gstr_additional_notice_orders.id)) AS count
        FROM gstr_additional_notice_orders
        LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${dateStr}
        AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${user_entity_1.UserStatus.DELETED}'
        AND (gstr_credentials.status != '${gstrCredentials_entity_1.GstrStatus.DISABLE}' OR gstr_credentials.status IS NULL)
      `;
            if (ViewAssigned && !ViewAll) {
                sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
            }
            if ((query === null || query === void 0 ? void 0 : query.assessmentYear) && (query === null || query === void 0 ? void 0 : query.assessmentYear) !== '') {
                sql += ` AND fy = '${query.assessmentYear}'`;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getNoticeAndOrderDueDates(organizationId, interval, dateColumn, query, userId, ViewAll, ViewAssigned) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 0 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
      SELECT COUNT(DISTINCT gstr_notice_orders.id) AS count
      FROM gstr_notice_orders
       LEFT JOIN client ON gstr_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
      WHERE STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND gstr_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${user_entity_1.UserStatus.DELETED}'
        AND gstr_credentials.status != '${gstrCredentials_entity_1.GstrStatus.DISABLE}'
    `;
            if (ViewAssigned && !ViewAll) {
                sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getAddNoticeAndOrderDueDates(organizationId, interval, dateColumn, query, userId, ViewAll, ViewAssigned) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (interval) {
                case 'today':
                    intervalQuery = 'INTERVAL 0 DAY';
                    break;
                case '1week':
                    intervalQuery = 'INTERVAL 1 WEEK';
                    break;
                case '15days':
                    intervalQuery = 'INTERVAL 15 DAY';
                    break;
                case '1month':
                    intervalQuery = 'INTERVAL 1 MONTH';
                    break;
                case '1year':
                    intervalQuery = 'INTERVAL 1 YEAR';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let dateStr = '';
            if (interval === 'today') {
                dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') = CURDATE()`;
            }
            else {
                dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})`;
            }
            let sql = `
      SELECT COUNT(*) AS count
      FROM gstr_additional_notice_orders
      LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
      LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
      WHERE ${dateStr}
      AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${user_entity_1.UserStatus.DELETED}'
        AND gstr_credentials.status != '${gstrCredentials_entity_1.GstrStatus.DISABLE}'
    `;
            if (ViewAssigned && !ViewAll) {
                sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
            }
            if ((query === null || query === void 0 ? void 0 : query.assessmentYear) && (query === null || query === void 0 ? void 0 : query.assessmentYear) !== '') {
                sql += `AND fy = '${query === null || query === void 0 ? void 0 : query.assessmentYear}'`;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count, 10);
        }
        catch (error) {
            console.error(`Error fetching ${dateColumn} dates:`, error);
            throw error;
        }
    }
    async getNoticeOrdersDateCount(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            const todayIssueDate = await this.getNoticeAndOrdersIssueDates(organizationId, 'today', 'date_of_issuance', query, userId, ViewAll, ViewAssigned);
            const last1WeekIssueDate = await this.getNoticeAndOrdersIssueDates(organizationId, '1week', 'date_of_issuance', query, userId, ViewAll, ViewAssigned);
            const last15DaysIssueDate = await this.getNoticeAndOrdersIssueDates(organizationId, '15days', 'date_of_issuance', query, userId, ViewAll, ViewAssigned);
            const last1MonthIssueDate = await this.getNoticeAndOrdersIssueDates(organizationId, '1month', 'date_of_issuance', query, userId, ViewAll, ViewAssigned);
            const todayDueDate = await this.getNoticeAndOrderDueDates(organizationId, 'today', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt1WeekDueDate = await this.getNoticeAndOrderDueDates(organizationId, '1week', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt15DaysDueDate = await this.getNoticeAndOrderDueDates(organizationId, '15days', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt1MonthDueDate = await this.getNoticeAndOrderDueDates(organizationId, '1month', 'due_date', query, userId, ViewAll, ViewAssigned);
            return {
                issuanceDate: {
                    last1Week: last1WeekIssueDate,
                    last15Days: last15DaysIssueDate,
                    last1Month: last1MonthIssueDate,
                    today: todayIssueDate
                },
                dueDate: {
                    nxt1Week: nxt1WeekDueDate,
                    nxt15Days: nxt15DaysDueDate,
                    nxt1Month: nxt1MonthDueDate,
                    today: todayDueDate
                }
            };
        }
        else {
            return {
                issuanceDate: {
                    last1Week: 0,
                    last15Days: 0,
                    last1Month: 0,
                    today: 0
                },
                dueDate: {
                    nxt1Week: 0,
                    nxt15Days: 0,
                    nxt1Month: 0,
                    today: 0
                },
            };
        }
    }
    async getAdditionalNoticeOrdersDateCount(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            const todayIssueDate = await this.getAddNoticeAndOrderIssueDates(organizationId, 'today', 'category_date', query, userId, ViewAll, ViewAssigned);
            const last1WeekIssueDate = await this.getAddNoticeAndOrderIssueDates(organizationId, '1week', 'category_date', query, userId, ViewAll, ViewAssigned);
            const last15DaysIssueDate = await this.getAddNoticeAndOrderIssueDates(organizationId, '15days', 'category_date', query, userId, ViewAll, ViewAssigned);
            const last1MonthIssueDate = await this.getAddNoticeAndOrderIssueDates(organizationId, '1month', 'category_date', query, userId, ViewAll, ViewAssigned);
            const todayDueDate = await this.getAddNoticeAndOrderDueDates(organizationId, 'today', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt1WeekDueDate = await this.getAddNoticeAndOrderDueDates(organizationId, '1week', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt15DaysDueDate = await this.getAddNoticeAndOrderDueDates(organizationId, '15days', 'due_date', query, userId, ViewAll, ViewAssigned);
            const nxt1MonthDueDate = await this.getAddNoticeAndOrderDueDates(organizationId, '1month', 'due_date', query, userId, ViewAll, ViewAssigned);
            return {
                issuanceDate: {
                    last1Week: last1WeekIssueDate,
                    last15Days: last15DaysIssueDate,
                    last1Month: last1MonthIssueDate,
                    today: todayIssueDate
                },
                dueDate: {
                    nxt1WeekDue: nxt1WeekDueDate,
                    nxt15DaysDue: nxt15DaysDueDate,
                    nxt1MonthDue: nxt1MonthDueDate,
                    today: todayDueDate
                },
            };
        }
        else {
            return {
                issuanceDate: {
                    last1Week: 0,
                    last15Days: 0,
                    last1Month: 0,
                    today: 0
                },
                dueDate: {
                    nxt1WeekDue: 0,
                    nxt15DaysDue: 0,
                    nxt1MonthDue: 0,
                    today: 0
                },
            };
        }
    }
    async getAddNoticeAndOrdersStats(organizationId, status, query, userId, ViewAll, ViewAssigned) {
        var _a;
        try {
            if (!organizationId) {
                throw new Error('Organization not found for the user.');
            }
            let intervalQuery = '';
            switch (status) {
                case 'open':
                    intervalQuery = 'gstr_additional_notice_orders.case_status LIKE "OPEN"';
                    break;
                case 'closed':
                    intervalQuery = 'gstr_additional_notice_orders.case_status LIKE "CLOSED"';
                    break;
                case 'replied':
                    intervalQuery = 'gstr_additional_notice_orders.ref_status LIKE "REPLIED"';
                    break;
                case 'notReplied':
                    intervalQuery = 'gstr_additional_notice_orders.ref_status LIKE "NOT_REPLIED"';
                    break;
                default:
                    throw new Error('Invalid interval');
            }
            let sql = `
        SELECT COUNT(DISTINCT(gstr_additional_notice_orders.id)) AS count
        FROM gstr_additional_notice_orders
        LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${intervalQuery} AND gstr_additional_notice_orders.case_folder_type_name NOT IN ('REPLIES','APPLICATIONS')
        AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${user_entity_1.UserStatus.DELETED}'
        AND (gstr_credentials.status != '${gstrCredentials_entity_1.GstrStatus.DISABLE}' OR gstr_credentials.status IS NULL)
      `;
            if (ViewAssigned && !ViewAll) {
                sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
            }
            if ((query === null || query === void 0 ? void 0 : query.assessmentYear) && (query === null || query === void 0 ? void 0 : query.assessmentYear) !== '') {
                sql += ` AND fy = '${query.assessmentYear}'`;
            }
            const result = await (0, typeorm_1.getManager)().query(sql);
            return parseInt((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count);
        }
        catch (error) {
            console.error(`Error fetching ${status} status:`, error);
            throw error;
        }
    }
    async getAdditionalNoticesStatCount(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const organizationId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
        if (organizationId) {
            const openCount = await this.getAddNoticeAndOrdersStats(organizationId, 'open', query, userId, ViewAll, ViewAssigned);
            const closedCount = await this.getAddNoticeAndOrdersStats(organizationId, 'closed', query, userId, ViewAll, ViewAssigned);
            const repliedCount = await this.getAddNoticeAndOrdersStats(organizationId, 'replied', query, userId, ViewAll, ViewAssigned);
            const notRepliedCount = await this.getAddNoticeAndOrdersStats(organizationId, 'notReplied', query, userId, ViewAll, ViewAssigned);
            return {
                stats: {
                    open: openCount,
                    closed: closedCount,
                    replied: repliedCount,
                    notReplied: notRepliedCount
                }
            };
        }
        else {
            return {
                stats: {
                    open: 0,
                    closed: 0,
                    replied: 0,
                    notReplied: 0
                }
            };
        }
    }
    async getGstrConfigStatus(userId, query) {
        var _a, _b;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization'] });
        const organizationPref = await organization_preferences_entity_1.default.findOne({
            where: { organization: user === null || user === void 0 ? void 0 : user.organization },
        });
        if (organizationPref) {
            const organizationLimit = ((_a = organizationPref === null || organizationPref === void 0 ? void 0 : organizationPref.automationConfig) === null || _a === void 0 ? void 0 : _a.gstrLimit) || 300;
            let clientCredentials = (0, typeorm_1.createQueryBuilder)(gstrCredentials_entity_1.default, 'gstrCredentials')
                .leftJoinAndSelect('gstrCredentials.client', 'client')
                .where('gstrCredentials.organizationId = :id', { id: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gstrCredentials.status IS NULL').orWhere('gstrCredentials.status = :enabledStatus', { enabledStatus: gstrCredentials_entity_1.GstrStatus.ENABLE });
            }));
            let result = (await clientCredentials.getCount()) || 0;
            const abc = {
                totalLimit: organizationLimit,
                difference: organizationLimit - result,
                presentClients: result,
            };
            return abc;
        }
    }
    async clientCheck(userId, queryy) {
        var _a;
        const { search, limit, offset } = queryy;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization', 'role'],
        });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const entityManager = (0, typeorm_1.getRepository)(gstrCredentials_entity_1.default);
        const query = await entityManager
            .createQueryBuilder('gstrCredentials')
            .leftJoinAndSelect('gstrCredentials.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('gstrCredentials.status = :inStatus', { inStatus: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .andWhere('gstrCredentials.remarks IN (:...remarks)', {
            remarks: [
                'Invalid Username or Password. Please try again.',
                'Your GST Login Password is Expired. Please change it with New Password!',
                'You have entered a wrong password for 3 consecutive times. Your account has been locked. Kindly access ‘Forgot Password’ link to reset your password.',
            ],
        });
        if (search) {
            query.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gstrCredentials.userName LIKE :search', {
                    search: `%${search}%`,
                });
                qb.orWhere('client.displayName LIKE :namesearch', {
                    namesearch: `%${search}%`,
                });
            }));
        }
        if (ViewAssigned && !ViewAll) {
            query.andWhere('clientManagers.id = :userId', { userId });
        }
        if (offset) {
            query.skip(offset);
        }
        if (limit) {
            query.take(limit);
        }
        let [queryResult, totalCount] = await query.getManyAndCount();
        const totalClients = await (0, typeorm_1.createQueryBuilder)(gstrCredentials_entity_1.default, 'credentials')
            .leftJoin('credentials.client', 'client')
            .where('credentials.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere('credentials.status = :inStatus', { inStatus: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE })
            .getCount();
        const result = {
            queryResult,
            totalClients,
            count: queryResult.length,
            totalCount,
        };
        return result;
    }
    async exportGstrInvalid(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: ********* });
        let invalidRows = await this.clientCheck(userId, newQuery);
        if (!invalidRows.queryResult.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Invalid Credentials');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Category', key: 'category' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Client ID', key: 'clientId' },
            { header: 'User Name', key: 'userName' },
            { header: 'Remarks', key: 'remarks' }
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        invalidRows.queryResult.forEach((invalidRow) => {
            var _a, _b, _c;
            const formatDateTime1 = (dateString) => {
                if (!dateString)
                    return '-';
                const date = new Date(dateString);
                return date.toLocaleString();
            };
            const rowData = {
                serialNo: serialCounter++,
                category: (_a = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.client) === null || _a === void 0 ? void 0 : _a.category,
                clientName: (_b = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.client) === null || _b === void 0 ? void 0 : _b.displayName,
                clientId: (_c = invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.client) === null || _c === void 0 ? void 0 : _c.clientId,
                userName: invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.userName,
                remarks: invalidRow === null || invalidRow === void 0 ? void 0 : invalidRow.remarks,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async outstandingDemandStats(userId, query) {
        var _a;
        const user = await user_entity_1.User.findOne({ where: { id: userId } });
        const demandStats = (0, typeorm_1.createQueryBuilder)(gstrDemands_entity_1.GstrOutstandingDemand, 'gstrOutstandingDemand')
            .leftJoinAndSelect('gstrOutstandingDemand.gstrCredentials', 'gstrCredentials')
            .leftJoinAndSelect('gstrCredentials.client', 'client')
            .select([
            'gstrOutstandingDemand.id',
            'client.id',
            'client.displayName',
            'gstrCredentials.id',
            'gstrOutstandingDemand.gstIn',
            'gstrOutstandingDemand.demandDt',
            'gstrOutstandingDemand.totalTot',
        ])
            .where('gstrOutstandingDemand.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
            .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
        })).orderBy('gstrOutstandingDemand.demandDt', 'DESC')
            .take(5);
        const result = await demandStats.getMany();
        return { result };
    }
    async getAdditionalNoticeStats(userId, query) {
        var _a, _b, _c, _d, _e;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId } });
            const orgId = (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id;
            if (!orgId) {
                return null;
            }
            const uniqueCaseTypeNames = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'n')
                .leftJoinAndSelect('n.client', 'client')
                .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
                .select('DISTINCT n.caseTypeName', 'caseTypeName')
                .where('n.organizationId = :orgId', { orgId })
                .andWhere('n.caseFolderTypeName IN  (:...caseNames)', { caseNames: ['NOTICES', 'ORDERS'] })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gstrCredentials.status != :disStatus', {
                    disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
                }).orWhere('gstrCredentials.status IS NULL');
            }))
                .andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
                createdType: aut_income_tax_eproceedings_fyi_notice_response_fya_entity_1.CreatedType.MANUAL,
            })
                .getRawMany();
            const noticeRows = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'n')
                .leftJoin('n.client', 'client')
                .leftJoin('client.gstrCredentials', 'gc')
                .select([
                'n.caseTypeName AS caseTypeName',
                'n.caseStatus AS caseStatus',
                'n.refStatus AS refStatus',
                'COUNT(DISTINCT n.refNum) AS cnt',
            ])
                .where('n.organizationId = :orgId', { orgId })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gc.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE }).orWhere('gc.status IS NULL');
            }))
                .andWhere('n.caseFolderTypeName = :folder', { folder: 'NOTICES' })
                .andWhere('n.refNum IS NOT NULL')
                .andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
                createdType: aut_income_tax_eproceedings_fyi_notice_response_fya_entity_1.CreatedType.MANUAL,
            }).groupBy('n.caseTypeName, n.caseStatus, n.refStatus')
                .getRawMany();
            const orderRows = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'n')
                .leftJoin('n.client', 'client')
                .leftJoin('client.gstrCredentials', 'gc')
                .select([
                'n.caseTypeName AS caseTypeName',
                'COUNT(DISTINCT n.refNum) AS cnt',
            ])
                .where('n.organizationId = :orgId', { orgId })
                .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gc.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE }).orWhere('gc.status IS NULL');
            }))
                .andWhere('n.caseFolderTypeName = :folder', { folder: 'ORDERS' })
                .andWhere('n.refNum IS NOT NULL')
                .andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
                createdType: aut_income_tax_eproceedings_fyi_notice_response_fya_entity_1.CreatedType.MANUAL,
            }).groupBy('n.caseTypeName')
                .getRawMany();
            const noticesByCaseType = {};
            const uniqueKeys = new Set();
            for (const u of uniqueCaseTypeNames) {
                const raw = u.caseTypeName;
                if (raw === null || raw === undefined)
                    continue;
                const key = String(raw).trim();
                if (!key)
                    continue;
                uniqueKeys.add(key);
                noticesByCaseType[key] = {
                    openReplied: 0,
                    openNotReplied: 0,
                    closedReplied: 0,
                    closedNotReplied: 0,
                    totalNotices: 0,
                    totalOrders: 0,
                };
            }
            let totalNotices = 0;
            for (const r of noticeRows) {
                const rawCaseType = r.caseTypeName;
                if (rawCaseType === null || rawCaseType === undefined)
                    continue;
                const caseType = String(rawCaseType).trim();
                if (!caseType)
                    continue;
                if (!uniqueKeys.has(caseType))
                    continue;
                const caseStatus = ((_b = r.caseStatus) !== null && _b !== void 0 ? _b : '').toString().trim().toUpperCase();
                const refStatus = ((_c = r.refStatus) !== null && _c !== void 0 ? _c : '').toString().trim().toUpperCase();
                const cnt = Number((_d = r.cnt) !== null && _d !== void 0 ? _d : 0);
                const isReplied = refStatus === 'REPLIED';
                const isNotReplied = refStatus === 'NOT_REPLIED';
                const isOpen = caseStatus === 'OPEN';
                const isClosed = caseStatus === 'CLOSED';
                if (isClosed) {
                    if (isReplied)
                        noticesByCaseType[caseType].closedReplied += cnt;
                    else if (isNotReplied)
                        noticesByCaseType[caseType].closedNotReplied += cnt;
                }
                else if (isOpen) {
                    if (isReplied)
                        noticesByCaseType[caseType].openReplied += cnt;
                    else if (isNotReplied)
                        noticesByCaseType[caseType].openNotReplied += cnt;
                }
                noticesByCaseType[caseType].totalNotices += cnt;
                totalNotices += cnt;
            }
            let totalOrders = 0;
            for (const r of orderRows) {
                const rawCaseType = r.caseTypeName;
                if (rawCaseType === null || rawCaseType === undefined)
                    continue;
                const caseType = String(rawCaseType).trim();
                if (!caseType)
                    continue;
                if (!uniqueKeys.has(caseType))
                    continue;
                const cnt = Number((_e = r.cnt) !== null && _e !== void 0 ? _e : 0);
                if (!noticesByCaseType[caseType]) {
                    noticesByCaseType[caseType] = {
                        openReplied: 0,
                        openNotReplied: 0,
                        closedReplied: 0,
                        closedNotReplied: 0,
                        totalNotices: 0,
                        totalOrders: 0,
                    };
                }
                noticesByCaseType[caseType].totalOrders = cnt;
                totalOrders += cnt;
            }
            return {
                noticesByCaseType,
                totals: { totalNotices, totalOrders },
            };
        }
        catch (e) {
            console.log('Error while fetching additional notice and orders stats', e);
        }
    }
};
GstrDashboardService = __decorate([
    (0, common_1.Injectable)()
], GstrDashboardService);
exports.GstrDashboardService = GstrDashboardService;
//# sourceMappingURL=dashboard.services.js.map