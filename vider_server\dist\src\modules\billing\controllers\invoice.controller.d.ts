/// <reference types="node" />
/// <reference types="node" />
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { FindInvoicesDto, NextInvoiceNumberDto } from '../dto/find-invoices.dto';
import { GetUnbilledTasksDto } from '../dto/get-unbilled.dto';
import { InvoiceService } from '../services/invoice.service';
import { FindClientBillingInvoices } from '../dto/find-client-billing-invoices.dto';
export declare class InvoiceController {
    private service;
    constructor(service: InvoiceService);
    getInvoices(request: any, query: FindInvoicesDto): Promise<{
        totalCount: number;
        result: import("../entitities/invoice.entity").Invoice[];
    }>;
    exportTdsBilling(req: any, body: any): Promise<import("exceljs").Buffer>;
    exportGstBilling(req: any, body: any): Promise<import("exceljs").Buffer>;
    getClientPortalInvoices(clientid: number, request: any, query: FindInvoicesDto): Promise<{
        totalCount: number;
        result: import("../entitities/invoice.entity").Invoice[];
    }>;
    getClientBillingInvoices(query: FindClientBillingInvoices): Promise<{
        totalCount: any;
        invoices: any;
    }>;
    createInvoice(body: CreateInvoiceDto, request: any): Promise<Buffer | import("../entitities/invoice.entity").Invoice>;
    updateInvoice(id: number, body: any, req: any): Promise<Buffer | import("../entitities/invoice.entity").Invoice>;
    getClientTasks(query: GetUnbilledTasksDto): Promise<{
        totalCount: number;
        result: import("../../tasks/entity/task.entity").default[];
    }>;
    exportInvoices(req: any, body: FindInvoicesDto): Promise<{
        file: import("exceljs").Buffer;
        type: string;
    }>;
    getClientInvoices(clientid: number): Promise<any>;
    getInvoice(id: number, query: any): Promise<import("../entitities/invoice.entity").Invoice | "Un-Authorized">;
    downloadEstimate(id: number, body: any): Promise<Buffer>;
    cancelEstimate(id: number, req: any): Promise<import("../entitities/invoice.entity").Invoice>;
    submitForApproval(id: number): Promise<import("../entitities/invoice.entity").Invoice>;
    getNextEstimateNumber(req: any, query: NextInvoiceNumberDto): Promise<{
        prifix: string;
        number: string;
    }>;
    downloadEstimatewithoutEmittor(id: number): Promise<Buffer>;
    getTaskActivity(req: any, id: number, query: any): Promise<import("../../activity/activity.entity").default[]>;
}
