"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunicationModule = void 0;
const common_1 = require("@nestjs/common");
const communication_controller_1 = require("./communication.controller");
const communication_service_1 = require("./communication.service");
const typeorm_1 = require("@nestjs/typeorm");
const client_group_broadcast_entity_1 = require("./entity/client-group-broadcast.entity");
const broadcast_email_templates_entity_1 = require("./entity/broadcast-email-templates-entity");
const broadcast_activity_entity_1 = require("./entity/broadcast-activity.entity");
const upload_service_1 = require("../storage/upload.service");
const broadcast_activity_details_entity_1 = require("./entity/broadcast-activity-details.entity");
let CommunicationModule = class CommunicationModule {
};
CommunicationModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([client_group_broadcast_entity_1.default, broadcast_email_templates_entity_1.default, broadcast_activity_entity_1.default, broadcast_activity_details_entity_1.default])],
        controllers: [communication_controller_1.CommunicationController],
        providers: [communication_service_1.CommunicationService, upload_service_1.AwsService],
    })
], CommunicationModule);
exports.CommunicationModule = CommunicationModule;
//# sourceMappingURL=communication.module.js.map