"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getclientinvoicebilled = exports.writeLocalOrgDetails = exports.readLocalOrgDetails = exports.CommonService = void 0;
const common_1 = require("@nestjs/common");
const aws_sdk_1 = require("aws-sdk");
const schedule_1 = require("@nestjs/schedule");
const moment = require("moment");
const typeorm_1 = require("typeorm");
const notification_entity_1 = require("../../notifications/notification.entity");
const ExcelJS = require("exceljs");
const axios_1 = require("axios");
const whatsapp_service_1 = require("../whatsapp/whatsapp.service");
const re_use_1 = require("../../utils/re-use");
const event_entity_1 = require("../events/event.entity");
const organization_entity_1 = require("../organization/entities/organization.entity");
const task_entity_1 = require("../tasks/entity/task.entity");
const dsc_register_entity_1 = require("../dsc-register/entity/dsc-register.entity");
const ejs = require("ejs");
const nodemailer = require("nodemailer");
const fs = require("fs");
const user_entity_1 = require("../users/entities/user.entity");
const puppeteer_1 = require("puppeteer");
const client_entity_1 = require("../clients/entity/client.entity");
const receipt_entity_1 = require("../billing/entitities/receipt.entity");
const utils_1 = require("../../utils");
const cron_activity_entity_1 = require("../cron-activity/cron-activity.entity");
const organization_preferences_entity_1 = require("../organization-preferences/entity/organization-preferences.entity");
const newemails_1 = require("../../emails/newemails");
const fullMobileWithCountry_1 = require("../../utils/validations/fullMobileWithCountry");
const sqlqueries_1 = require("../../utils/sqlqueries");
const task_status_entity_1 = require("../tasks/entity/task-status.entity");
const invoice_entity_1 = require("../billing/entitities/invoice.entity");
const proforma_invoice_entity_1 = require("../billing/entitities/proforma-invoice.entity");
const noticeOrders_entity_1 = require("../gstr-automation/entity/noticeOrders.entity");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../gstr-automation/entity/gstrAdditionalOrdersAndNotices.entity");
const aut_income_tax_eproceedings_fya_notice_entity_1 = require("../automation/entities/aut_income_tax_eproceedings_fya_notice.entity");
const aut_income_tax_eproceedings_fyi_notice_entity_1 = require("../automation/entities/aut_income_tax_eproceedings_fyi_notice.entity");
const expenditure_entity_1 = require("../expenditure/expenditure.entity");
const viderWhatsappSessions_1 = require("../whatsapp/entity/viderWhatsappSessions");
const AWS = require("aws-sdk");
const log_hour_entity_1 = require("../log-hours/log-hour.entity");
const attendance_entity_1 = require("../attendance/attendance.entity");
const automation_machines_entity_1 = require("../automation/entities/automation_machines.entity");
const gstrCredentials_entity_1 = require("../gstr-automation/entity/gstrCredentials.entity");
const aut_client_credentials_entity_1 = require("../automation/entities/aut_client_credentials.entity");
const types_1 = require("../tasks/dto/types");
const email_throttle_service_1 = require("../email-throttle/email-throttle.service");
let transporter = nodemailer.createTransport({
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
});
const s3 = new AWS.S3();
let CommonService = class CommonService {
    constructor(emailThrottleService) {
        this.emailThrottleService = emailThrottleService;
    }
    async getclientinvoicebilled(clientId, payload) {
        let sql = `SELECT 
    t.id, 
    t.name, 
    t.task_number AS tasknumber, 
    t.status AS status, 
    DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
    DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
    SUM(i.grand_total) AS amount,
    (SELECT COUNT(t2.id) 
     FROM task t2 
     LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
     WHERE t2.client_id = '${payload.clientId}'
         AND t2.status != 'deleted' 
         AND t2.status != 'terminated' 
         AND t2.payment_status = 'BILLED'
         AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
         AND t2.parent_task_id IS NULL
    ) AS total_count
  FROM task t 
  LEFT JOIN invoice i ON i.id = t.invoice_id
  WHERE t.client_id = '${payload.clientId}'
    AND t.status != 'deleted' 
    AND t.status != 'terminated' 
    AND t.payment_status = 'BILLED'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
    AND t.parent_task_id IS NULL `;
        sql += ` group by t.id having t.id is not null 
    limit ${(payload === null || payload === void 0 ? void 0 : payload.offset) || 0}, ${(payload === null || payload === void 0 ? void 0 : payload.limit) || 1000}`;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async exportClientBilledTasks(clientId, payload) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let tasks = await this.getclientinvoicebilled(clientId, newQuery);
        if (!((_a = tasks === null || tasks === void 0 ? void 0 : tasks.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Billed Task');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Task ID', key: 'tasknumber' },
            { header: 'Task Name', key: 'name' },
            { header: 'Task Status', key: 'status' },
            { header: 'Invoice Date', key: 'invoice_date' },
            { header: 'Due Date', key: 'invoice_due_date' },
            { header: 'Invoice Value (₹)', key: 'amount' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = tasks === null || tasks === void 0 ? void 0 : tasks.result) === null || _b === void 0 ? void 0 : _b.forEach((task) => {
            const rowData = {
                serialNo: serialCounter++,
                tasknumber: task === null || task === void 0 ? void 0 : task.tasknumber,
                name: task === null || task === void 0 ? void 0 : task.name,
                status: (0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status),
                invoice_date: task === null || task === void 0 ? void 0 : task.invoice_date,
                invoice_due_date: task === null || task === void 0 ? void 0 : task.invoice_due_date,
                amount: 1 * (task === null || task === void 0 ? void 0 : task.amount),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'todo':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'on hold':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'under review':
                        statusCell.font = { color: { argb: '653BBA' }, bold: true };
                        break;
                    case 'completed':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientGroupBilledTasks(clientId, payload) {
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let sql = await (0, sqlqueries_1.clientgroupinvoicebilled)(payload);
        let result = await (0, typeorm_1.getManager)().query(sql);
        if (!(result === null || result === void 0 ? void 0 : result.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Billed Task');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Task ID', key: 'tasknumber' },
            { header: 'Task Name', key: 'name' },
            { header: 'Task Status', key: 'status' },
            { header: 'Pure Agent ₹', key: 'pureagent' },
            { header: 'Additional Amount ₹', key: 'additional' },
            { header: 'Service Fee ₹', key: 'serviceFee' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        result === null || result === void 0 ? void 0 : result.forEach((task) => {
            const rowData = {
                serialNo: serialCounter++,
                tasknumber: task === null || task === void 0 ? void 0 : task.tasknumber,
                name: task === null || task === void 0 ? void 0 : task.name,
                status: (0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status),
                pureagent: 1 * (task === null || task === void 0 ? void 0 : task.pureagent) || 0,
                additional: 1 * (task === null || task === void 0 ? void 0 : task.additional) || 0,
                serviceFee: 1 * (task === null || task === void 0 ? void 0 : task.fee_amount) || 0,
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'todo':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'on hold':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'under review':
                        statusCell.font = { color: { argb: '653BBA' }, bold: true };
                        break;
                    case 'completed':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getclientinvoiceexport(clientId, payload) {
        let sql = `select * from invoice where client_id = ${payload.clientId}
    AND invoice_number LIKE '%${payload.search}%'; `;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async exportClientInvoiceReport(clientId, payload) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let invoices = await this.getclientinvoiceexport(clientId, newQuery);
        if (!((_a = invoices === null || invoices === void 0 ? void 0 : invoices.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Invoice');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Invoice Date', key: 'invoiceDate' },
            { header: 'Invoice Number', key: 'invoiceNumber' },
            { header: 'Amount', key: 'amount' },
            { header: 'Status', key: 'status' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = invoices === null || invoices === void 0 ? void 0 : invoices.result) === null || _b === void 0 ? void 0 : _b.forEach((invoice) => {
            const rowData = {
                serialNo: serialCounter++,
                invoiceDate: (0, utils_1.formatDate)(invoice === null || invoice === void 0 ? void 0 : invoice.created_at),
                invoiceNumber: invoice === null || invoice === void 0 ? void 0 : invoice.invoice_number,
                amount: 1 * (invoice === null || invoice === void 0 ? void 0 : invoice.grand_total),
                status: (invoice === null || invoice === void 0 ? void 0 : invoice.status) == 'APPROVAL_PENDING' ? 'INVOICED' : (0, utils_1.getTitle)(invoice === null || invoice === void 0 ? void 0 : invoice.status),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'created':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'invoiced':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'cancelled':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'overdue':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'partially paid':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'converted':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    case 'paid':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientGroupInvoiceReport(clientId, payload) {
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let sql = await (0, sqlqueries_1.clientgroupinvoicebilling)(payload);
        let result = await (0, typeorm_1.getManager)().query(sql, newQuery);
        if (!(result === null || result === void 0 ? void 0 : result.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Invoice');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Invoice Date', key: 'invoiceDate' },
            { header: 'Invoice Number', key: 'invoiceNumber' },
            { header: 'Amount', key: 'amount' },
            { header: 'Status', key: 'status' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        result === null || result === void 0 ? void 0 : result.forEach((invoice) => {
            const rowData = {
                serialNo: serialCounter++,
                invoiceDate: (0, utils_1.formatDate)(invoice === null || invoice === void 0 ? void 0 : invoice.created_at),
                invoiceNumber: invoice === null || invoice === void 0 ? void 0 : invoice.invoice_number,
                amount: 1 * (invoice === null || invoice === void 0 ? void 0 : invoice.grand_total),
                status: (invoice === null || invoice === void 0 ? void 0 : invoice.status) == 'APPROVAL_PENDING' ? 'INVOICED' : (0, utils_1.getTitle)(invoice === null || invoice === void 0 ? void 0 : invoice.status),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'created':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'invoiced':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'cancelled':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'overdue':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'partially paid':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'converted':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    case 'paid':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getclientinvoiceUnbilled(clientId, payload) {
        let sql = `SELECT t.id,t.task_number as tasknumber,t.name, t.status, t.fee_amount, SUM(e.amount) AS total_expenditure,
    SUM( CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END) AS pureagent,
    SUM( CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END) AS additional,
    (
        SELECT COUNT(t2.id) 
        FROM task t2 
        LEFT JOIN expenditure e2 ON e2.task_id = t2.id 
        WHERE t2.client_id = '${payload.clientId}'
        AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%')
        AND (t2.recurring_status != 'pending' OR t2.recurring_status IS NULL)
        AND t2.status != 'deleted' AND t2.status != 'terminated'
        AND t2.payment_status = 'UNBILLED'
        AND t2.billable is true
        AND t2.parent_task_id IS NULL
    ) AS total_count
    FROM task t 
    LEFT JOIN expenditure e ON e.task_id = t.id 
    WHERE t.client_id = '${payload.clientId}'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%')
    AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL)
    AND t.status != 'deleted' and t.status !='terminated'
    AND t.billable is true
    AND t.payment_status = 'UNBILLED'
    AND t.billable is true
    AND t.parent_task_id IS NULL
    GROUP BY t.id, t.name, t.status, t.fee_amount`;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async exportUnClientBilledTasks(clientId, payload) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let tasks = await this.getclientinvoiceUnbilled(clientId, newQuery);
        if (!((_a = tasks === null || tasks === void 0 ? void 0 : tasks.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Un-Billed Task');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Task ID', key: 'tasknumber' },
            { header: 'Task Name', key: 'name' },
            { header: 'Task Status', key: 'status' },
            { header: 'Pure Agent ₹', key: 'pureagent' },
            { header: 'Additional Amount ₹', key: 'additional' },
            { header: 'Service Fee ₹', key: 'serviceFee' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = tasks === null || tasks === void 0 ? void 0 : tasks.result) === null || _b === void 0 ? void 0 : _b.forEach((task) => {
            const rowData = {
                serialNo: serialCounter++,
                tasknumber: task === null || task === void 0 ? void 0 : task.tasknumber,
                name: task === null || task === void 0 ? void 0 : task.name,
                status: (0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status),
                pureagent: 1 * (task === null || task === void 0 ? void 0 : task.pureagent) || 0,
                additional: 1 * (task === null || task === void 0 ? void 0 : task.additional) || 0,
                serviceFee: 1 * (task === null || task === void 0 ? void 0 : task.fee_amount) || 0,
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'todo':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'on hold':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'under review':
                        statusCell.font = { color: { argb: '653BBA' }, bold: true };
                        break;
                    case 'completed':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportUnClientGroupUnBilledTasks(clientId, payload) {
        let sql = await (0, sqlqueries_1.clientGroupinvoiceunbilled)(payload);
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let result = await (0, typeorm_1.getManager)().query(sql, newQuery);
        if (!(result === null || result === void 0 ? void 0 : result.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Un-Billed Task');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Task ID', key: 'tasknumber' },
            { header: 'Task Name', key: 'name' },
            { header: 'Task Status', key: 'status' },
            { header: 'Pure Agent ₹', key: 'pureagent' },
            { header: 'Additional Amount ₹', key: 'additional' },
            { header: 'Service Fee ₹', key: 'serviceFee' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        result === null || result === void 0 ? void 0 : result.forEach((task) => {
            const rowData = {
                serialNo: serialCounter++,
                tasknumber: task === null || task === void 0 ? void 0 : task.tasknumber,
                name: task === null || task === void 0 ? void 0 : task.name,
                status: (0, utils_1.getTitle)(task === null || task === void 0 ? void 0 : task.status),
                pureagent: 1 * (task === null || task === void 0 ? void 0 : task.pureagent) || 0,
                additional: 1 * (task === null || task === void 0 ? void 0 : task.additional) || 0,
                serviceFee: 1 * (task === null || task === void 0 ? void 0 : task.fee_amount) || 0,
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'todo':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'on hold':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'under review':
                        statusCell.font = { color: { argb: '653BBA' }, bold: true };
                        break;
                    case 'completed':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getclientinvoiceOverView(orgId, payload, ViewAll, ViewAssigned, user) {
        let sql = `SELECT 
    c.id,
    c.display_name,
    c.category,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.billable=true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable=true THEN t.id END) AS billedtasks,
    CASE 
    WHEN ABS(
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${receipt_entity_1.ReceiptStatus.CREATED}' 
                      AND r.type = '${receipt_entity_1.ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    ) < 0.0000001 THEN 0
    ELSE (
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${receipt_entity_1.ReceiptStatus.CREATED}' 
                      AND r.type = '${receipt_entity_1.ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    )
END AS dueamount,
IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ) as tdsAmount,


    (
        SELECT COUNT(DISTINCT c2.id)
        FROM client c2
        LEFT JOIN task t2 ON c2.id = t2.client_id 
        AND (t2.recurring_status != 'pending' OR t2.recurring_status IS NULL) 
        AND t2.status != 'terminated' 
        AND t2.status != 'deleted' 
        AND t2.parent_task_id IS NULL
        LEFT JOIN client_client_managers_user ccmu ON c2.id = ccmu.client_id
        LEFT JOIN user u ON ccmu.user_id = u.id
        WHERE c2.organization_id = '${payload.organizationid}' 
        AND c2.status != 'deleted' 
        AND c2.display_name LIKE '%${payload.search}%'
        ${ViewAssigned && !ViewAll ? `AND u.id = ${user.id}` : ' '}
    ) AS total_count
FROM 
    client c 
    LEFT JOIN task t ON c.id = t.client_id 
    AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL) 
    AND t.status != 'terminated' 
    AND t.status != 'deleted' 
    AND t.parent_task_id IS NULL
    LEFT JOIN client_client_managers_user ON c.id = client_client_managers_user.client_id
    LEFT JOIN user ON client_client_managers_user.user_id = user.id
WHERE 
    c.organization_id = '${payload.organizationid}' 
     AND (t.billable is true OR t.id is null OR t.billable is false)
    AND c.status != 'deleted' 
    ${ViewAssigned && !ViewAll ? `AND user.id = ${user.id}` : ' '}
AND c.display_name LIKE '%${payload.search}%' group by c.id;`;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async exportClientOverviewlist(orgId, payload, ViewAll, ViewAssigned, user) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000 });
        let clients = await this.getclientinvoiceOverView(orgId, newQuery, ViewAll, ViewAssigned, user);
        if (!((_a = clients === null || clients === void 0 ? void 0 : clients.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Client Billing');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Category', key: 'clientCategory' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Billable Task', key: 'billableTask' },
            { header: 'Unbilled Task', key: 'unbilledTask' },
            { header: 'Billed Tasks', key: 'billedTasks' },
            { header: 'Amount Due', key: 'amountDue' },
            { header: 'TDS Amount', key: 'tdsAmount' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = clients === null || clients === void 0 ? void 0 : clients.result) === null || _b === void 0 ? void 0 : _b.forEach((client) => {
            const rowData = {
                serialNo: serialCounter++,
                clientCategory: (0, utils_1.getTitle)(client.category),
                clientName: client === null || client === void 0 ? void 0 : client.display_name,
                billableTask: 1 * (client === null || client === void 0 ? void 0 : client.billabletasks),
                unbilledTask: 1 * (client === null || client === void 0 ? void 0 : client.unbilledtasks),
                billedTasks: 1 * (client === null || client === void 0 ? void 0 : client.billedtasks),
                amountDue: 1 * (client === null || client === void 0 ? void 0 : client.dueamount),
                tdsAmount: 1 * (client === null || client === void 0 ? void 0 : client.tdsAmount),
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientGroupOverviewlist(orgId, payload, user, ViewAll, ViewAssigned) {
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let sql = await (0, sqlqueries_1.clientsgrouplistinvoice)(payload, user, ViewAll, ViewAssigned);
        let result = await (0, typeorm_1.getManager)().query(sql, newQuery);
        if (!(result === null || result === void 0 ? void 0 : result.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Group Billing');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Group', key: 'clientName' },
            { header: 'Billable Task', key: 'billableTask' },
            { header: 'Unbilled Task', key: 'unbilledTask' },
            { header: 'Billed Tasks', key: 'billedTasks' },
            { header: 'Amount Due', key: 'amountDue' },
            { header: 'TDS Amount', key: 'tdsAmount' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        result === null || result === void 0 ? void 0 : result.forEach((client) => {
            const rowData = {
                serialNo: serialCounter++,
                clientName: client === null || client === void 0 ? void 0 : client.display_name,
                billableTask: 1 * (client === null || client === void 0 ? void 0 : client.billabletasks),
                unbilledTask: 1 * (client === null || client === void 0 ? void 0 : client.unbilledtasks),
                billedTasks: 1 * (client === null || client === void 0 ? void 0 : client.billedtasks),
                amountDue: 1 * (client === null || client === void 0 ? void 0 : client.dueamount),
                tdsAmount: 1 * (client === null || client === void 0 ? void 0 : client.tdsAmount),
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getClientReceiptsReport(clientId, payload) {
        let sql = `select receipt_number,
    status,
    DATE_FORMAT(receipt_date, '%d-%m-%y')receipt_date,
    payment_mode,amount,
    DATE_FORMAT(r.created_at, '%d-%m-%y') created_at,
    (
      SELECT COUNT(*)
      FROM receipt r
      WHERE r.client_id = '${payload.clientId}'
      AND r.receipt_number LIKE '%${payload.search}%'
  ) AS total_count,
  r.type,
  be.trade_name
  from receipt r LEFT JOIN billing_entity be ON r.billing_entity_id = be.id
  where client_id='${payload.clientId}'
    AND receipt_number LIKE '%${payload.search}%'
    order by r.created_at desc;`;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async exportClientReceipts(clientId, payload) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let receipts = await this.getClientReceiptsReport(clientId, newQuery);
        if (!((_a = receipts === null || receipts === void 0 ? void 0 : receipts.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Receipts');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Receipt Type', key: 'type' },
            { header: 'Receipt #', key: 'receipt_number' },
            { header: 'Receipt Date', key: 'created_at' },
            { header: 'Billing Entity', key: 'trade_name' },
            { header: 'Amount Received (₹)', key: 'amount' },
            { header: 'Status', key: 'status' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = receipts === null || receipts === void 0 ? void 0 : receipts.result) === null || _b === void 0 ? void 0 : _b.forEach((receipt) => {
            const rowData = {
                serialNo: serialCounter++,
                type: receipt === null || receipt === void 0 ? void 0 : receipt.type,
                receipt_number: receipt === null || receipt === void 0 ? void 0 : receipt.receipt_number,
                created_at: receipt === null || receipt === void 0 ? void 0 : receipt.created_at,
                trade_name: receipt === null || receipt === void 0 ? void 0 : receipt.trade_name,
                amount: 1 * (receipt === null || receipt === void 0 ? void 0 : receipt.amount),
                status: (0, utils_1.getTitle)(receipt === null || receipt === void 0 ? void 0 : receipt.status),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'created':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'invoiced':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'cancelled':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'overdue':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'partially paid':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'converted':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    case 'paid':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'trade_name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientGroupReceipts(clientId, payload) {
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let sql = await (0, sqlqueries_1.clientgroupinvoicereceipts)(payload);
        let result = await (0, typeorm_1.getManager)().query(sql, newQuery);
        if (!(result === null || result === void 0 ? void 0 : result.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Receipts');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Receipt Type', key: 'type' },
            { header: 'Receipt #', key: 'receipt_number' },
            { header: 'Receipt Date', key: 'created_at' },
            { header: 'Billing Entity', key: 'trade_name' },
            { header: 'Amount Received (₹)', key: 'amount' },
            { header: 'Status', key: 'status' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        result === null || result === void 0 ? void 0 : result.forEach((receipt) => {
            const rowData = {
                serialNo: serialCounter++,
                type: receipt === null || receipt === void 0 ? void 0 : receipt.type,
                receipt_number: receipt === null || receipt === void 0 ? void 0 : receipt.receipt_number,
                created_at: receipt === null || receipt === void 0 ? void 0 : receipt.created_at,
                trade_name: receipt === null || receipt === void 0 ? void 0 : receipt.trade_name,
                amount: 1 * (receipt === null || receipt === void 0 ? void 0 : receipt.amount),
                status: (0, utils_1.getTitle)(receipt === null || receipt === void 0 ? void 0 : receipt.status),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'created':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'invoiced':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'cancelled':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'overdue':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'partially paid':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'converted':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    case 'paid':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'trade_name') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async upload(file) {
        try {
            const { originalname } = file;
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const upload = await this.uploadS3(file.buffer, bucketS3, originalname, file.mimetype);
            return upload;
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadInvoice(data) {
        var _a, _b;
        try {
            const { invoiceData, invoiceBuffer } = data;
            const { organization, client, invoiceNumber, invoiceDueDate, grandTotal, id, invoiceDate, clientGroup, userId, } = invoiceData;
            const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
            const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(invoiceBuffer), bucketS3, `invoice/${id}.pdf`, 'application/pdf');
            const { Location } = uploadData;
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                const clientName = ((_a = client === null || client === void 0 ? void 0 : client.displayName) === null || _a === void 0 ? void 0 : _a.length) > 0
                    ? client === null || client === void 0 ? void 0 : client.displayName
                    : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
                const title = 'Invoice Created';
                if (invoiceData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: 'invoice-client-template',
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: (0, fullMobileWithCountry_1.fullMobileNumberWithCountry)((client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber), (client === null || client === void 0 ? void 0 : client.countryCode) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.countryCode)),
                        name: 'invoice_client',
                        header: [
                            {
                                type: 'document',
                                link: Location,
                            },
                        ],
                        body: [clientName, invoiceNumber, invoiceDateFormat, grandTotal, invoiceDueDateFormat],
                        fileName: `Invoice-${invoiceNumber}`,
                        key: 'INVOICE_CREATION_WHATSAPP',
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                }
                if (invoiceData.emailCheck === true) {
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization.id },
                    });
                    const clientPreferences = (_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _b === void 0 ? void 0 : _b.email;
                    const keyy = 'INVOICE_CREATION_MAIL';
                    if (clientPreferences && clientPreferences[keyy]) {
                        const addressParts = [
                            organization.buildingNo || '',
                            organization.floorNumber || '',
                            organization.buildingName || '',
                            organization.street || '',
                            organization.location || '',
                            organization.city || '',
                            organization.district || '',
                            organization.state || '',
                        ].filter((part) => part && part.trim() !== '');
                        const pincode = organization.pincode && organization.pincode.trim() !== ''
                            ? ` - ${organization.pincode}`
                            : '';
                        const address = addressParts.join(', ') + pincode;
                        const mailOptions = {
                            id: userId,
                            key: 'INVOICE_CREATION_MAIL',
                            email: client === null || client === void 0 ? void 0 : client.email,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                serviceName: '',
                                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                                invoiceNumber: invoiceNumber,
                                invoiceDate: invoiceDateFormat,
                                invoiceDueDateFormat: invoiceDueDateFormat,
                                invoiceTotalAmount: grandTotal,
                                clientName: clientName,
                                address: address,
                                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                                mail: organization === null || organization === void 0 ? void 0 : organization.email,
                                userId: userId,
                            },
                            filePath: 'client-invoice-created',
                            subject: 'Invoice for Services Rendered',
                            invoiceId: id,
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
                const whatsappOptions = {
                    to: `91${mobileNumber}`,
                    name: 'invoice',
                    header: [
                        {
                            type: 'document',
                            link: Location,
                        },
                    ],
                    body: [fullName, invoiceNumber, clientName, invoiceDueDate, grandTotal],
                    fileName: `Invoice-${invoiceNumber}`,
                };
                const key = 'INVOICES_CREATED_WHATSAPP';
                const whatsappMessageBody = `
Hi *${fullName}*
A new Invoice has been generated in ATOM:
*Invoice number:* ${invoiceNumber}
*Client name:* ${clientName}
*Due date:* ${invoiceDueDate}
*Total amount:* ${grandTotal}
        
 We hope this helps!
        `;
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadReceipt(data) {
        var _a, _b;
        try {
            const { receiptData, receiptBuffer } = data;
            const { organization, client, receiptNumber, receiptDate, amount, id, clientGroup, emailCheck, paymentMode, address, logInUser, } = receiptData;
            const receiptDateFormat = moment(receiptDate).format("DD-MM-YYYY");
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(receiptBuffer), bucketS3, `receipt/${id}.pdf`, "application/pdf");
            const { Location } = uploadData;
            console.log("organization", organization === null || organization === void 0 ? void 0 : organization.id);
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            console.log("admins", admins);
            const clientName = ((_a = client === null || client === void 0 ? void 0 : client.displayName) === null || _a === void 0 ? void 0 : _a.length) > 0
                ? client === null || client === void 0 ? void 0 : client.displayName
                : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                if (receiptData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: "receipt-client-template",
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: (0, fullMobileWithCountry_1.fullMobileNumberWithCountry)((client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber), (client === null || client === void 0 ? void 0 : client.countryCode) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.countryCode)),
                        name: "receipt_client",
                        header: [
                            {
                                type: "document",
                                link: Location,
                            },
                        ],
                        body: [fullName, clientName, receiptNumber, receiptDateFormat, amount],
                        fileName: `Receipt-${receiptNumber}`,
                        key: "RECEIPT_CREATION_WHATSAPP",
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                    const key = "RECEIPT_CREATED_WHATSAPP";
                    const whatsappMessageBody = `
Hi *${fullName}*
A new receipt has been generated in ATOM:
*Receipt number:* ${receiptNumber}
*Client name:* ${clientName}
*Receipt date:* ${receiptDate}
*Total amount:* ${amount}
        
We hope this helps!
        `;
                    const whatsappTitle = "Receipt Created";
                }
            }
            try {
                const key = "RECEIPT_CREATION_MAIL";
                if (emailCheck && key === "RECEIPT_CREATION_MAIL" && client) {
                    const mailOptions = {
                        id: logInUser,
                        key: "RECEIPT_CREATION_MAIL",
                        email: client === null || client === void 0 ? void 0 : client.email,
                        clientMail: "ORGANIZATION_CLIENT_EMAIL",
                        data: {
                            legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                            receiptDate: receiptDate,
                            Amountreceived: amount,
                            clientName: clientName,
                            address: address,
                            phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                            mail: organization === null || organization === void 0 ? void 0 : organization.email,
                            paymentMode: paymentMode,
                            userId: receiptData === null || receiptData === void 0 ? void 0 : receiptData.userId,
                        },
                        filePath: "client-receipt-created",
                        subject: "Confirmation of Payment Received for Service Rendered",
                    };
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization === null || organization === void 0 ? void 0 : organization.id },
                    });
                    const clientPreferences = (_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _b === void 0 ? void 0 : _b.email;
                    if (clientPreferences && clientPreferences[key]) {
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
            catch (mailErr) {
                console.error("Failed to send receipt creation email:", mailErr);
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadReceiptForEdit(data) {
        var _a, _b;
        try {
            const { receiptData, receiptBuffer } = data;
            const { organization, client, receiptNumber, receiptDate, amount, id, clientGroup, emailCheck, paymentMode, address, logInUser, } = receiptData;
            const receiptDateFormat = moment(receiptDate).format("DD-MM-YYYY");
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(receiptBuffer), bucketS3, `Receipt_${id}.pdf`, "application/pdf");
            const { Location } = uploadData;
            console.log("organization", organization === null || organization === void 0 ? void 0 : organization.id);
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            console.log("admins", admins);
            const clientName = ((_a = client === null || client === void 0 ? void 0 : client.displayName) === null || _a === void 0 ? void 0 : _a.length) > 0
                ? client === null || client === void 0 ? void 0 : client.displayName
                : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                if (receiptData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: "receipt-client-template",
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: (0, fullMobileWithCountry_1.fullMobileNumberWithCountry)((client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber), (client === null || client === void 0 ? void 0 : client.countryCode) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.countryCode)),
                        name: "receipt_updated_client",
                        header: [
                            {
                                type: "document",
                                link: Location,
                            },
                        ],
                        fileName: `Receipt-${receiptNumber}`,
                        key: "RECEIPT_EDITED_WHATSAPP",
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                    const key = "RECEIPT_EDITED_WHATSAPP";
                    const whatsappMessageBody = `
Hi *${fullName}*
A new receipt has been generated in ATOM:
*Receipt number:* ${receiptNumber}
*Client name:* ${clientName}
*Receipt date:* ${receiptDate}
*Total amount:* ${amount}
        
We hope this helps!
        `;
                    const whatsappTitle = "Receipt Created";
                }
            }
            try {
                const key = "RECEIPT_EDITED_MAIL";
                if (emailCheck && key === "RECEIPT_EDITED_MAIL" && client) {
                    const mailOptions = {
                        id: logInUser,
                        key: "RECEIPT_EDITED_MAIL",
                        email: client === null || client === void 0 ? void 0 : client.email,
                        clientMail: "ORGANIZATION_CLIENT_EMAIL",
                        data: {
                            legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                            receiptDate: receiptDate,
                            Amountreceived: amount,
                            clientName: clientName,
                            address: address,
                            phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                            mail: organization === null || organization === void 0 ? void 0 : organization.email,
                            paymentMode: paymentMode,
                            userId: receiptData === null || receiptData === void 0 ? void 0 : receiptData.userId,
                        },
                        filePath: "client-receipt-edited",
                        subject: "Confirmation of Updated Payment Received for Service Rendered",
                    };
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization === null || organization === void 0 ? void 0 : organization.id },
                    });
                    const clientPreferences = (_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _b === void 0 ? void 0 : _b.email;
                    if (clientPreferences && clientPreferences[key]) {
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
            catch (mailErr) {
                console.error("Failed to send receipt creation email:", mailErr);
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadProformaInvoice(data) {
        var _a;
        try {
            const { invoiceData, invoiceBuffer } = data;
            const { organization, client, invoiceNumber, invoiceDueDate, grandTotal, id, invoiceDate, clientGroup, } = invoiceData;
            const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
            const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(invoiceBuffer), bucketS3, `invoice/${id}.pdf`, 'application/pdf');
            const { Location } = uploadData;
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                const clientName = (client === null || client === void 0 ? void 0 : client.displayName.length) > 0
                    ? client === null || client === void 0 ? void 0 : client.displayName
                    : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
                const clientNumber = (client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber);
                const title = 'Invoice Proforma Created';
                if (invoiceData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: 'proforma-invoice-client-template',
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: `91${clientNumber}`,
                        name: 'invoiceproforma',
                        header: [
                            {
                                type: 'document',
                                link: Location,
                            },
                        ],
                        body: [clientName, invoiceNumber, invoiceDateFormat, grandTotal, invoiceDueDateFormat],
                        fileName: `Proforma-Invoice-${invoiceNumber}`,
                        key: 'INVOICE_PROFORMA_CREATION_WHATSAPP',
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                }
                if (invoiceData.emailCheck === true) {
                    const keyy = 'INVOICE_PROFORMA_CREATION_MAIL';
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization.id },
                    });
                    const clientPreferences = (_a = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _a === void 0 ? void 0 : _a.email;
                    if (clientPreferences === null || clientPreferences === void 0 ? void 0 : clientPreferences['INVOICE_PROFORMA_CREATION_MAIL']) {
                        const address = `${organization.buildingNo || ' ' ? organization.buildingNo || ' ' + ', ' : ''}${organization.floorNumber || ' ' ? organization.floorNumber || ' ' + ', ' : ''}${organization.buildingName || ' ' ? organization.buildingName + ', ' : ''}${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''}${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''}${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''}`;
                        const mailOptions = {
                            id: userId,
                            key: 'INVOICE_PROFORMA_CREATION_MAIL',
                            email: client === null || client === void 0 ? void 0 : client.email,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                serviceName: '',
                                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                                invoiceNumber: invoiceNumber,
                                invoiceDate: invoiceDateFormat,
                                invoiceDueDateFormat: invoiceDueDateFormat,
                                invoiceTotalAmount: grandTotal,
                                clientName: clientName,
                                address: address,
                                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                                mail: organization === null || organization === void 0 ? void 0 : organization.email,
                                userId: userId,
                            },
                            filePath: 'client-invoice-created',
                            subject: 'Proforma Invoice for Services Rendered',
                            invoiceId: id,
                            type: 'PROFORMA',
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadInvoiceForEdit(data) {
        var _a, _b;
        try {
            const { invoiceData, invoiceBuffer } = data;
            const { organization, client, invoiceNumber, invoiceDueDate, grandTotal, id, invoiceDate, clientGroup, userId, } = invoiceData;
            const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
            const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(invoiceBuffer), bucketS3, `Invoice_${id}.pdf`, 'application/pdf');
            const { Location } = uploadData;
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                const clientName = ((_a = client === null || client === void 0 ? void 0 : client.displayName) === null || _a === void 0 ? void 0 : _a.length) > 0
                    ? client === null || client === void 0 ? void 0 : client.displayName
                    : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
                const title = 'Invoice Edited';
                if (invoiceData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: 'client-invoice-edited',
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: (0, fullMobileWithCountry_1.fullMobileNumberWithCountry)((client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber), (client === null || client === void 0 ? void 0 : client.countryCode) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.countryCode)),
                        name: 'invoiceupdated_clientt',
                        header: [
                            {
                                type: 'document',
                                link: Location,
                            },
                        ],
                        fileName: `Invoice-${invoiceNumber}`,
                        key: 'INVOICE_EDITED_WHATSAPP',
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                }
                if (invoiceData.emailCheck === true) {
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization.id },
                    });
                    const clientPreferences = (_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _b === void 0 ? void 0 : _b.email;
                    const keyy = 'INVOICE_EDITED_MAIL';
                    if (clientPreferences && clientPreferences[keyy]) {
                        const addressParts = [
                            organization.buildingNo || '',
                            organization.floorNumber || '',
                            organization.buildingName || '',
                            organization.street || '',
                            organization.location || '',
                            organization.city || '',
                            organization.district || '',
                            organization.state || '',
                        ].filter((part) => part && part.trim() !== '');
                        const pincode = organization.pincode && organization.pincode.trim() !== ''
                            ? ` - ${organization.pincode}`
                            : '';
                        const address = addressParts.join(', ') + pincode;
                        const mailOptions = {
                            id: userId,
                            key: 'INVOICE_EDITED_MAIL',
                            email: client === null || client === void 0 ? void 0 : client.email,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                serviceName: '',
                                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                                invoiceNumber: invoiceNumber,
                                invoiceDate: invoiceDateFormat,
                                invoiceDueDateFormat: invoiceDueDateFormat,
                                invoiceTotalAmount: grandTotal,
                                clientName: clientName,
                                address: address,
                                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                                mail: organization === null || organization === void 0 ? void 0 : organization.email,
                                userId: userId,
                            },
                            filePath: 'client-invoice-edited',
                            subject: 'Updated Invoice for Services Rendered',
                            invoiceId: id,
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
                const whatsappOptions = {
                    to: `91${mobileNumber}`,
                    name: 'invoice',
                    header: [
                        {
                            type: 'document',
                            link: Location,
                        },
                    ],
                    body: [fullName, invoiceNumber, clientName, invoiceDueDate, grandTotal],
                    fileName: `Invoice-${invoiceNumber}`,
                };
                const key = 'INVOICES_EDITED_WHATSAPP';
                const whatsappMessageBody = `
Hi *${fullName}*
An existing invoice has been updated in ATOM:
*Invoice number:* ${invoiceNumber}
*Client name:* ${clientName}
*Due date:* ${invoiceDueDate}
*Total amount:* ${grandTotal}
        
 We hope this helps!
        `;
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async uploadProformaInvoiceForEdit(data) {
        var _a;
        try {
            const { invoiceData, invoiceBuffer } = data;
            const { organization, client, invoiceNumber, invoiceDueDate, grandTotal, id, invoiceDate, clientGroup, } = invoiceData;
            const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
            const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(invoiceBuffer), bucketS3, `Invoice_${id}.pdf`, 'application/pdf');
            const { Location } = uploadData;
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId, organization_id: orgId, } = userDetails;
                const clientName = (client === null || client === void 0 ? void 0 : client.displayName.length) > 0
                    ? client === null || client === void 0 ? void 0 : client.displayName
                    : (client === null || client === void 0 ? void 0 : client.fullName) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName);
                const clientNumber = (client === null || client === void 0 ? void 0 : client.mobileNumber) || (clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber);
                const title = 'Invoice Proforma Edited';
                if (invoiceData.whatsappCheck === true) {
                    const whatsappOptions = {
                        title: 'proforma-invoice-client-template',
                        userId: userId,
                        orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                        to: `91${clientNumber}`,
                        name: 'proforma_updated_client',
                        header: [
                            {
                                type: 'document',
                                link: Location,
                            },
                        ],
                        fileName: `Proforma-Invoice-${invoiceNumber}`,
                        key: 'INVOICE_PROFORMA_EDITED_WHATSAPP',
                    };
                    await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
                }
                if (invoiceData.emailCheck === true) {
                    const keyy = 'INVOICE_PROFORMA_EDITED_MAIL';
                    const orgPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization.id },
                    });
                    const clientPreferences = (_a = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _a === void 0 ? void 0 : _a.email;
                    if (clientPreferences === null || clientPreferences === void 0 ? void 0 : clientPreferences['INVOICE_PROFORMA_EDITED_MAIL']) {
                        const address = `${organization.buildingNo || ' ' ? organization.buildingNo || ' ' + ', ' : ''}${organization.floorNumber || ' ' ? organization.floorNumber || ' ' + ', ' : ''}${organization.buildingName || ' ' ? organization.buildingName + ', ' : ''}${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''}${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''}${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''}`;
                        const mailOptions = {
                            id: userId,
                            key: 'INVOICE_PROFORMA_EDITED_MAIL',
                            email: client === null || client === void 0 ? void 0 : client.email,
                            clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                            data: {
                                serviceName: '',
                                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                                invoiceNumber: invoiceNumber,
                                invoiceDate: invoiceDateFormat,
                                invoiceDueDateFormat: invoiceDueDateFormat,
                                invoiceTotalAmount: grandTotal,
                                clientName: clientName,
                                address: address,
                                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                                mail: organization === null || organization === void 0 ? void 0 : organization.email,
                                userId: userId,
                            },
                            filePath: 'client-invoice-edited',
                            subject: 'Updated Proforma Invoice for Services Rendered',
                            invoiceId: id,
                            type: 'PROFORMA',
                        };
                        await (0, newemails_1.sendnewMail)(mailOptions);
                    }
                }
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async summaryReport() {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        if (process.env.Cron_Running === 'true') {
            const cronData = new cron_activity_entity_1.default();
            cronData.cronType = 'Summary Report';
            cronData.cronDate = moment().toDate().toString();
            cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const cornActivityID = await cronData.save();
            try {
                const allOrganizations = await organization_entity_1.Organization.find();
                for (const organization of allOrganizations) {
                    const organizationPreferences = await organization_preferences_entity_1.default.findOne({
                        where: { organization: organization === null || organization === void 0 ? void 0 : organization.id },
                    });
                    const whatsappCheck = (_a = organizationPreferences === null || organizationPreferences === void 0 ? void 0 : organizationPreferences.notificationConfig) === null || _a === void 0 ? void 0 : _a.whatsappPreferences;
                    if (whatsappCheck) {
                        const userDetails = await user_entity_1.User.find({
                            where: {
                                organization: organization === null || organization === void 0 ? void 0 : organization.id,
                                status: user_entity_1.UserStatus.ACTIVE,
                                type: user_entity_1.UserType.ORGANIZATION,
                            },
                        });
                        const startOfDay = new Date();
                        startOfDay.setHours(0, 0, 0, 0);
                        const endOfDay = new Date();
                        endOfDay.setHours(23, 59, 59, 999);
                        for (const user of userDetails) {
                            const sessionValidation = await viderWhatsappSessions_1.default.findOne({
                                where: { userId: user === null || user === void 0 ? void 0 : user.id, status: 'ACTIVE' },
                            });
                            if (sessionValidation) {
                                const { fullName, mobileNumber, id } = user;
                                const tasks = await task_status_entity_1.default.createQueryBuilder('taskStatus')
                                    .leftJoinAndSelect('taskStatus.task', 'task')
                                    .leftJoinAndSelect('taskStatus.user', 'user')
                                    .leftJoinAndSelect('task.client', 'client')
                                    .leftJoinAndSelect('task.clientGroup', 'clientGroup')
                                    .leftJoinAndSelect('task.service', 'service')
                                    .leftJoinAndSelect('task.category', 'category')
                                    .leftJoinAndSelect('task.organization', 'organization')
                                    .andWhere('taskStatus.status = :status', { status: 'completed' })
                                    .andWhere('taskStatus.createdAt BETWEEN :startOfDay AND :endOfDay', {
                                    startOfDay,
                                    endOfDay,
                                })
                                    .andWhere('organization.id = :organizationId', {
                                    organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id,
                                })
                                    .getMany();
                                let completedTasks = [];
                                if ((tasks === null || tasks === void 0 ? void 0 : tasks.length) > 0) {
                                    completedTasks = tasks.map((taskStatus) => {
                                        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            clientName: ((_b = (_a = taskStatus.task) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName) ||
                                                ((_d = (_c = taskStatus.task) === null || _c === void 0 ? void 0 : _c.clientGroup) === null || _d === void 0 ? void 0 : _d.displayName),
                                            serviceCategory: (_f = (_e = taskStatus.task) === null || _e === void 0 ? void 0 : _e.category) === null || _f === void 0 ? void 0 : _f.name,
                                            taskId: (_g = taskStatus.task) === null || _g === void 0 ? void 0 : _g.taskNumber,
                                            taskName: (_h = taskStatus.task) === null || _h === void 0 ? void 0 : _h.name,
                                            priority: (_j = taskStatus.task) === null || _j === void 0 ? void 0 : _j.priority,
                                            status: (_k = taskStatus.task) === null || _k === void 0 ? void 0 : _k.status,
                                            taskDueDate: (_l = taskStatus.task) === null || _l === void 0 ? void 0 : _l.dueDate,
                                            userName: (_m = taskStatus === null || taskStatus === void 0 ? void 0 : taskStatus.user) === null || _m === void 0 ? void 0 : _m.fullName,
                                        });
                                    });
                                }
                                const completedTaskCount = tasks.length;
                                const clients = await client_entity_1.default.find({
                                    where: {
                                        organization: (_c = user === null || user === void 0 ? void 0 : user.organization) === null || _c === void 0 ? void 0 : _c.id,
                                        createdAt: (0, typeorm_1.Between)(startOfDay, endOfDay),
                                    },
                                });
                                let clientsOnBoardedToday = [];
                                if ((clients === null || clients === void 0 ? void 0 : clients.length) > 0) {
                                    clientsOnBoardedToday = clients.map((client) => ({
                                        clientId: client === null || client === void 0 ? void 0 : client.clientId,
                                        clientCategory: client === null || client === void 0 ? void 0 : client.category,
                                        clientSubCategory: (client === null || client === void 0 ? void 0 : client.subCategory) || '-',
                                        clientNumber: client === null || client === void 0 ? void 0 : client.clientNumber,
                                        clientName: client === null || client === void 0 ? void 0 : client.displayName,
                                        clientTradeName: (client === null || client === void 0 ? void 0 : client.tradeName) || '-',
                                        mobileNumber: client === null || client === void 0 ? void 0 : client.mobileNumber,
                                        email: client === null || client === void 0 ? void 0 : client.email,
                                        status: client === null || client === void 0 ? void 0 : client.status,
                                    }));
                                }
                                const clientsCreated = clients === null || clients === void 0 ? void 0 : clients.length;
                                const proformaInvoice = await proforma_invoice_entity_1.ProformaInvoice.find({
                                    where: {
                                        organization: (_d = user === null || user === void 0 ? void 0 : user.organization) === null || _d === void 0 ? void 0 : _d.id,
                                        createdAt: (0, typeorm_1.Between)(startOfDay, endOfDay),
                                    },
                                    relations: ['billingEntity', 'client', 'clientGroup'],
                                });
                                let proformaInvoicesToday = [];
                                if ((proformaInvoice === null || proformaInvoice === void 0 ? void 0 : proformaInvoice.length) > 0) {
                                    proformaInvoicesToday = proformaInvoice.map((proforma) => {
                                        var _a, _b, _c;
                                        return ({
                                            proformaInvoiceNumber: proforma === null || proforma === void 0 ? void 0 : proforma.invoiceNumber,
                                            proformaInvoiceDate: proforma === null || proforma === void 0 ? void 0 : proforma.invoiceDate,
                                            proformaBillingEntity: (_a = proforma === null || proforma === void 0 ? void 0 : proforma.billingEntity) === null || _a === void 0 ? void 0 : _a.tradeName,
                                            proformaClientName: ((_b = proforma === null || proforma === void 0 ? void 0 : proforma.client) === null || _b === void 0 ? void 0 : _b.displayName) || ((_c = proforma === null || proforma === void 0 ? void 0 : proforma.clientGroup) === null || _c === void 0 ? void 0 : _c.displayName),
                                            proformaInvoiceAmount: proforma === null || proforma === void 0 ? void 0 : proforma.grandTotal,
                                            proformaDueDate: proforma === null || proforma === void 0 ? void 0 : proforma.invoiceDueDate,
                                            proformaStatus: proforma === null || proforma === void 0 ? void 0 : proforma.status,
                                        });
                                    });
                                }
                                const proformaInvoiceCreated = proformaInvoice === null || proformaInvoice === void 0 ? void 0 : proformaInvoice.length;
                                const invoice = await invoice_entity_1.Invoice.find({
                                    where: {
                                        organization: (_e = user === null || user === void 0 ? void 0 : user.organization) === null || _e === void 0 ? void 0 : _e.id,
                                        createdAt: (0, typeorm_1.Between)(startOfDay, endOfDay),
                                    },
                                    relations: ['billingEntity', 'client', 'clientGroup'],
                                });
                                let invoicesToday = [];
                                if ((invoice === null || invoice === void 0 ? void 0 : invoice.length) > 0) {
                                    invoicesToday = invoice.map((inv) => {
                                        var _a, _b, _c;
                                        return ({
                                            invoiceNumber: inv === null || inv === void 0 ? void 0 : inv.invoiceNumber,
                                            invoiceDate: inv === null || inv === void 0 ? void 0 : inv.invoiceDate,
                                            billingEntity: (_a = inv === null || inv === void 0 ? void 0 : inv.billingEntity) === null || _a === void 0 ? void 0 : _a.tradeName,
                                            clientName: ((_b = inv === null || inv === void 0 ? void 0 : inv.client) === null || _b === void 0 ? void 0 : _b.displayName) || ((_c = inv === null || inv === void 0 ? void 0 : inv.clientGroup) === null || _c === void 0 ? void 0 : _c.displayName),
                                            invoiceAmount: inv === null || inv === void 0 ? void 0 : inv.grandTotal,
                                            dueDate: inv === null || inv === void 0 ? void 0 : inv.invoiceDueDate,
                                            status: (inv === null || inv === void 0 ? void 0 : inv.status) === invoice_entity_1.InvoiceStatus.APPROVAL_PENDING ||
                                                (inv === null || inv === void 0 ? void 0 : inv.status) === invoice_entity_1.InvoiceStatus.PARTIALLY_PAID
                                                ? (inv === null || inv === void 0 ? void 0 : inv.invoiceDueDate) > moment().subtract(1, 'day').format('YYYY-MM-DD')
                                                    ? inv.status === invoice_entity_1.InvoiceStatus.APPROVAL_PENDING
                                                        ? 'Created'
                                                        : 'Partially Paid'
                                                    : 'Overdue'
                                                : (inv === null || inv === void 0 ? void 0 : inv.status) === invoice_entity_1.InvoiceStatus.CANCELLED
                                                    ? 'Cancelled'
                                                    : 'Paid',
                                        });
                                    });
                                }
                                const invoiceCreated = invoice === null || invoice === void 0 ? void 0 : invoice.length;
                                const receipts = await receipt_entity_1.default.find({
                                    where: {
                                        organization: (_f = user === null || user === void 0 ? void 0 : user.organization) === null || _f === void 0 ? void 0 : _f.id,
                                        createdAt: (0, typeorm_1.Between)(startOfDay, endOfDay),
                                    },
                                    relations: ['billingEntity', 'client', 'clientGroup'],
                                });
                                let receiptsToday = [];
                                if ((receipts === null || receipts === void 0 ? void 0 : receipts.length) > 0) {
                                    receiptsToday = receipts.map((receipt) => {
                                        var _a, _b, _c;
                                        return ({
                                            receiptNumber: receipt === null || receipt === void 0 ? void 0 : receipt.receiptNumber,
                                            receiptDate: receipt === null || receipt === void 0 ? void 0 : receipt.receiptDate,
                                            billingEntity: (_a = receipt === null || receipt === void 0 ? void 0 : receipt.billingEntity) === null || _a === void 0 ? void 0 : _a.tradeName,
                                            receiptType: receipt === null || receipt === void 0 ? void 0 : receipt.type,
                                            clientName: ((_b = receipt === null || receipt === void 0 ? void 0 : receipt.client) === null || _b === void 0 ? void 0 : _b.displayName) || ((_c = receipt === null || receipt === void 0 ? void 0 : receipt.clientGroup) === null || _c === void 0 ? void 0 : _c.displayName),
                                            receiptMode: receipt === null || receipt === void 0 ? void 0 : receipt.paymentMode,
                                            receiptAmount: receipt === null || receipt === void 0 ? void 0 : receipt.amount,
                                            status: receipt === null || receipt === void 0 ? void 0 : receipt.status,
                                        });
                                    });
                                }
                                const startDate = moment(new Date()).format('YYYY-MM-DD');
                                const endDate = moment(new Date()).format('YYYY-MM-DD');
                                const activeUserss = await user_entity_1.User.find({
                                    where: {
                                        organization: (_g = user === null || user === void 0 ? void 0 : user.organization) === null || _g === void 0 ? void 0 : _g.id,
                                        type: 'ORGANIZATION',
                                    },
                                    relations: ['role'],
                                });
                                const userIdss = activeUserss.map((user) => user.id);
                                const receiptsCreated = receipts === null || receipts === void 0 ? void 0 : receipts.length;
                                const expenditure = await expenditure_entity_1.default.createQueryBuilder('expenditure')
                                    .leftJoinAndSelect('expenditure.task', 'task')
                                    .leftJoinAndSelect('expenditure.client', 'client')
                                    .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
                                    .leftJoinAndSelect('task.organization', 'organization')
                                    .leftJoinAndSelect('expenditure.user', 'user')
                                    .andWhere('expenditure.date BETWEEN :startDate AND :endDate', {
                                    startDate,
                                    endDate,
                                })
                                    .andWhere('user.id IN (:...users)', { users: userIdss })
                                    .getMany();
                                let expenditureCreatedToday = [];
                                if ((expenditure === null || expenditure === void 0 ? void 0 : expenditure.length) > 0) {
                                    expenditureCreatedToday = expenditure.map((exp) => {
                                        var _a, _b, _c, _d, _e;
                                        return ({
                                            expenditureType: exp === null || exp === void 0 ? void 0 : exp.type,
                                            client: ((_a = exp === null || exp === void 0 ? void 0 : exp.client) === null || _a === void 0 ? void 0 : _a.displayName) || ((_b = exp === null || exp === void 0 ? void 0 : exp.clientGroup) === null || _b === void 0 ? void 0 : _b.displayName),
                                            expenseType: exp === null || exp === void 0 ? void 0 : exp.taskExpenseType,
                                            taskId: (_c = exp === null || exp === void 0 ? void 0 : exp.task) === null || _c === void 0 ? void 0 : _c.taskNumber,
                                            taskName: (_d = exp === null || exp === void 0 ? void 0 : exp.task) === null || _d === void 0 ? void 0 : _d.name,
                                            expenseTitle: exp === null || exp === void 0 ? void 0 : exp.particularName,
                                            amount: exp === null || exp === void 0 ? void 0 : exp.amount,
                                            userName: (_e = exp === null || exp === void 0 ? void 0 : exp.user) === null || _e === void 0 ? void 0 : _e.fullName,
                                        });
                                    });
                                }
                                const expenditureToday = expenditure === null || expenditure === void 0 ? void 0 : expenditure.length;
                                let tasksDue = await (0, typeorm_1.createQueryBuilder)(task_entity_1.default, 'task')
                                    .leftJoin('task.organization', 'organization')
                                    .leftJoin('task.members', 'taskMembers')
                                    .leftJoinAndSelect('task.category', 'category')
                                    .leftJoinAndSelect('task.members', 'members')
                                    .leftJoinAndSelect('members.imageStorage', 'imageStorage')
                                    .leftJoinAndSelect('task.client', 'client')
                                    .leftJoinAndSelect('task.clientGroup', 'clientGroup')
                                    .where('organization.id = :id', { id: user.organization.id })
                                    .andWhere('task.status not in (:...status)', {
                                    status: ['terminated', 'deleted', 'completed'],
                                })
                                    .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
                                    .andWhere('task.parentTask is null')
                                    .andWhere('DATE(task.dueDate) = :dueDate', {
                                    dueDate: moment().add(1, 'day').format('YYYY-MM-DD'),
                                })
                                    .orderBy('task.dueDate', 'ASC')
                                    .getMany();
                                let tasksDueTommorow = [];
                                if ((tasksDue === null || tasksDue === void 0 ? void 0 : tasksDue.length) > 0) {
                                    tasksDueTommorow = tasksDue.map((task) => {
                                        var _a, _b, _c, _d;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            clientName: ((_a = task === null || task === void 0 ? void 0 : task.client) === null || _a === void 0 ? void 0 : _a.displayName) || ((_b = task === null || task === void 0 ? void 0 : task.clientGroup) === null || _b === void 0 ? void 0 : _b.displayName),
                                            serviceCategory: (_c = task === null || task === void 0 ? void 0 : task.category) === null || _c === void 0 ? void 0 : _c.name,
                                            taskId: task === null || task === void 0 ? void 0 : task.taskNumber,
                                            taskName: task === null || task === void 0 ? void 0 : task.name,
                                            priority: task === null || task === void 0 ? void 0 : task.priority,
                                            status: task === null || task === void 0 ? void 0 : task.status,
                                            taskDueDate: task === null || task === void 0 ? void 0 : task.dueDate,
                                            userName: (_d = task === null || task === void 0 ? void 0 : task.user) === null || _d === void 0 ? void 0 : _d.fullName,
                                        });
                                    });
                                }
                                const tasksDueTommorowCount = tasksDue === null || tasksDue === void 0 ? void 0 : tasksDue.length;
                                const additionalNoticeOrders = await gstrAdditionalOrdersAndNotices_entity_1.default.createQueryBuilder('additionalNotice')
                                    .where('additionalNotice.organizationId = :organizationId', {
                                    organizationId: organization.id,
                                })
                                    .andWhere(new typeorm_1.Brackets((qb) => {
                                    qb.where('STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay,"%Y-%m-%d") AND STR_TO_DATE(:endOfDay,"%Y-%m-%d")', { startOfDay, endOfDay }).orWhere('STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay, "%Y-%m-%d") AND STR_TO_DATE(:endOfDay, "%Y-%m-%d")', { startOfDay, endOfDay });
                                }))
                                    .leftJoinAndSelect('additionalNotice.client', 'client')
                                    .getMany();
                                let additionalNoticeOrdersArray = [];
                                if ((additionalNoticeOrders === null || additionalNoticeOrders === void 0 ? void 0 : additionalNoticeOrders.length) > 0) {
                                    additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => {
                                        var _a;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                            issuanceDate: moment(order === null || order === void 0 ? void 0 : order.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                            dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                                ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                                : order === null || order === void 0 ? void 0 : order.dueDate,
                                            financialYear: (order === null || order === void 0 ? void 0 : order.fy) || '-',
                                            folder: order === null || order === void 0 ? void 0 : order.caseFolderTypeName,
                                            referenceNumber: order === null || order === void 0 ? void 0 : order.refNum,
                                            type: order === null || order === void 0 ? void 0 : order.caseTypeName,
                                        });
                                    });
                                }
                                const additionalGSTNoticeCountToday = additionalNoticeOrders === null || additionalNoticeOrders === void 0 ? void 0 : additionalNoticeOrders.length;
                                const noticeOrders = await noticeOrders_entity_1.default.createQueryBuilder('noticeOrder')
                                    .where('noticeOrder.organizationId = :organizationId', {
                                    organizationId: organization.id,
                                })
                                    .andWhere(new typeorm_1.Brackets((qb) => {
                                    qb.where('STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay,"%Y-%m-%d") AND STR_TO_DATE(:endOfDay,"%Y-%m-%d")', { startOfDay, endOfDay }).orWhere('STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:startOfDay, "%Y-%m-%d") AND STR_TO_DATE(:endOfDay, "%Y-%m-%d")', { startOfDay, endOfDay });
                                }))
                                    .leftJoinAndSelect('noticeOrder.client', 'client')
                                    .getMany();
                                let noticeAndOrdersArray = [];
                                if ((noticeOrders === null || noticeOrders === void 0 ? void 0 : noticeOrders.length) > 0) {
                                    noticeAndOrdersArray = noticeOrders.map((order) => {
                                        var _a;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            clientName: (_a = order === null || order === void 0 ? void 0 : order.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                            orderNumber: order === null || order === void 0 ? void 0 : order.orderNumber,
                                            issuanceDate: moment(order === null || order === void 0 ? void 0 : order.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                                            dueDate: (order === null || order === void 0 ? void 0 : order.dueDate) && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                                                ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                                : order === null || order === void 0 ? void 0 : order.dueDate,
                                            type: order === null || order === void 0 ? void 0 : order.type,
                                            amount: order === null || order === void 0 ? void 0 : order.amountOfDemand,
                                        });
                                    });
                                }
                                const gstNoticeCountToday = noticeOrders.length;
                                const totalGSTDueCount = additionalGSTNoticeCountToday + gstNoticeCountToday;
                                const fyaRecords = await (0, typeorm_1.createQueryBuilder)(aut_income_tax_eproceedings_fya_notice_entity_1.default, 'autFyaNotice')
                                    .where('autFyaNotice.organizationId = :orgId', { orgId: organization === null || organization === void 0 ? void 0 : organization.id })
                                    .andWhere(new typeorm_1.Brackets((qb) => {
                                    qb.where('DATE(autFyaNotice.issuedOn) BETWEEN :startOfDay AND :endOfDay', {
                                        startOfDay,
                                        endOfDay,
                                    }).orWhere('DATE(autFyaNotice.responseDueDate) BETWEEN :startOfDay AND :endOfDay', {
                                        startOfDay,
                                        endOfDay,
                                    });
                                }))
                                    .leftJoinAndSelect('autFyaNotice.client', 'client')
                                    .getMany();
                                let fyaRecordsArray = [];
                                if ((fyaRecords === null || fyaRecords === void 0 ? void 0 : fyaRecords.length) > 0) {
                                    fyaRecordsArray = fyaRecords.map((item) => {
                                        var _a;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            id: item.id,
                                            clientName: (_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                            panNumber: item === null || item === void 0 ? void 0 : item.pan,
                                            proceedingName: item === null || item === void 0 ? void 0 : item.proceedingName,
                                            din: (item === null || item === void 0 ? void 0 : item.documentIdentificationNumber) || '-',
                                            ay: (item === null || item === void 0 ? void 0 : item.assesmentYear)
                                                ? `${item.assesmentYear}-${parseInt(item === null || item === void 0 ? void 0 : item.assesmentYear) + 1}`
                                                : '-',
                                            issuedOnDate: (item === null || item === void 0 ? void 0 : item.issuedOn) &&
                                                moment(item === null || item === void 0 ? void 0 : item.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                                                ? moment(item === null || item === void 0 ? void 0 : item.issuedOn).format('DD-MM-YYYY')
                                                : '-',
                                            responseDueDate: (item === null || item === void 0 ? void 0 : item.responseDueDate) &&
                                                moment(item === null || item === void 0 ? void 0 : item.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                                                ? moment(item === null || item === void 0 ? void 0 : item.responseDueDate).format('DD-MM-YYYY')
                                                : '-',
                                        });
                                    });
                                }
                                const fyaNoticeCountToday = fyaRecords.length;
                                const fyiRecords = await (0, typeorm_1.createQueryBuilder)(aut_income_tax_eproceedings_fyi_notice_entity_1.default, 'autFyiNotice')
                                    .leftJoinAndSelect('autFyiNotice.eProceeding', 'eProceeding')
                                    .where('autFyiNotice.organizationId = :orgId', { orgId: organization === null || organization === void 0 ? void 0 : organization.id })
                                    .andWhere(new typeorm_1.Brackets((qb) => {
                                    qb.where('DATE(autFyiNotice.issuedOn) BETWEEN :startOfDay AND :endOfDay', {
                                        startOfDay,
                                        endOfDay,
                                    }).orWhere('DATE(autFyiNotice.responseDueDate) BETWEEN :startOfDay AND :endOfDay', {
                                        startOfDay,
                                        endOfDay,
                                    });
                                }))
                                    .leftJoinAndSelect('autFyiNotice.client', 'client')
                                    .getMany();
                                let fyiRecordsArray = [];
                                if ((fyiRecords === null || fyiRecords === void 0 ? void 0 : fyiRecords.length) > 0) {
                                    fyiRecordsArray = fyiRecords.map((item) => {
                                        var _a, _b, _c, _d;
                                        return ({
                                            orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                            id: item.id,
                                            clientName: (_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                            panNumber: item === null || item === void 0 ? void 0 : item.pan,
                                            proceedingName: item === null || item === void 0 ? void 0 : item.proceedingName,
                                            din: (item === null || item === void 0 ? void 0 : item.documentIdentificationNumber) || '-',
                                            ay: ((_b = item === null || item === void 0 ? void 0 : item.eProceeding) === null || _b === void 0 ? void 0 : _b.assessmentYear)
                                                ? `${(_c = item === null || item === void 0 ? void 0 : item.eProceeding) === null || _c === void 0 ? void 0 : _c.assessmentYear}-${parseInt((_d = item === null || item === void 0 ? void 0 : item.eProceeding) === null || _d === void 0 ? void 0 : _d.assessmentYear) + 1}`
                                                : '-',
                                            issuedOnDate: (item === null || item === void 0 ? void 0 : item.issuedOn) &&
                                                moment(item === null || item === void 0 ? void 0 : item.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                                                ? moment(item === null || item === void 0 ? void 0 : item.issuedOn).format('DD-MM-YYYY')
                                                : '-',
                                            responseDueDate: (item === null || item === void 0 ? void 0 : item.responseDueDate) &&
                                                moment(item === null || item === void 0 ? void 0 : item.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
                                                ? moment(item === null || item === void 0 ? void 0 : item.responseDueDate).format('DD-MM-YYYY')
                                                : '-',
                                        });
                                    });
                                }
                                const orgId = organization === null || organization === void 0 ? void 0 : organization.id;
                                const activeUsers = await user_entity_1.User.find({
                                    where: {
                                        organization: orgId,
                                        status: 'ACTIVE',
                                        type: 'ORGANIZATION',
                                    },
                                    relations: ['role'],
                                });
                                const userIds = activeUsers.map((user) => user.id);
                                const attendanceDetails = await attendance_entity_1.default.find({
                                    where: {
                                        userId: (0, typeorm_1.In)(userIds),
                                        attendanceDate: (0, typeorm_1.Between)(moment(startOfDay).format('YYYY-MM-DD'), moment(endOfDay).format('YYYY-MM-DD')),
                                    },
                                });
                                const logHours = await log_hour_entity_1.default.find({
                                    where: {
                                        user: (0, typeorm_1.In)(userIds),
                                        completedDate: (0, typeorm_1.Between)(startOfDay, endOfDay),
                                    },
                                    relations: ['user', 'client', 'clientGroup', 'task'],
                                });
                                const mergedData = activeUsers.map((user) => {
                                    var _a;
                                    const attendance = attendanceDetails.find((att) => att.userId === user.id);
                                    const userLogHours = logHours.filter((log) => log.user.id === user.id);
                                    return {
                                        userId: user.id,
                                        userName: user.fullName,
                                        role: ((_a = user.role) === null || _a === void 0 ? void 0 : _a.name) || '-',
                                        attendanceStatus: (attendance === null || attendance === void 0 ? void 0 : attendance.type) || '-',
                                        checkinTime: (attendance === null || attendance === void 0 ? void 0 : attendance.checkin_time)
                                            ? moment(attendance.checkin_time).format('h:mm A')
                                            : '-',
                                        checkoutTime: (attendance === null || attendance === void 0 ? void 0 : attendance.checkout_time)
                                            ? moment(attendance.checkout_time).format('h:mm A')
                                            : '-',
                                        totalLogHours: attendance === null || attendance === void 0 ? void 0 : attendance.hours_logged,
                                        logHours: userLogHours.map((log) => {
                                            var _a, _b, _c, _d;
                                            return ({
                                                type: log === null || log === void 0 ? void 0 : log.type,
                                                client: ((_a = log.client) === null || _a === void 0 ? void 0 : _a.displayName) || ((_b = log.clientGroup) === null || _b === void 0 ? void 0 : _b.displayName),
                                                taskName: ((_c = log === null || log === void 0 ? void 0 : log.task) === null || _c === void 0 ? void 0 : _c.name) || (log === null || log === void 0 ? void 0 : log.title),
                                                taskId: ((_d = log.task) === null || _d === void 0 ? void 0 : _d.taskNumber) || '-',
                                                duration: moment.utc(+(log === null || log === void 0 ? void 0 : log.duration)).format('HH:mm') || '-',
                                            });
                                        }),
                                    };
                                });
                                const today = new Date();
                                const reportDate = `${today.getDate().toString().padStart(2, '0')}-${(today.getMonth() + 1)
                                    .toString()
                                    .padStart(2, '0')}-${today.getFullYear()}`;
                                const fyiNoticeCountToday = fyiRecords.length;
                                const totalITNoticeDue = fyiNoticeCountToday + fyaNoticeCountToday;
                                const ejsData = {
                                    reportDate,
                                    completedTaskCount,
                                    clientsCreated,
                                    proformaInvoiceCreated,
                                    invoiceCreated,
                                    receiptsCreated,
                                    expenditureToday,
                                    tasksDueTommorowCount,
                                    totalITNoticeDue,
                                    totalGSTDueCount,
                                    completedTasks,
                                    clientsOnBoardedToday,
                                    proformaInvoicesToday,
                                    invoicesToday,
                                    receiptsToday,
                                    expenditureCreatedToday,
                                    tasksDueTommorow,
                                    noticeAndOrdersArray,
                                    additionalNoticeOrdersArray,
                                    fyaRecordsArray,
                                    fyiRecordsArray,
                                    mergedData,
                                };
                                const htmlContent = await ejs.renderFile('src/emails/templates/summary-report.ejs', ejsData);
                                const browser = await puppeteer_1.default.launch({
                                    headless: true,
                                    executablePath: '/usr/bin/google-chrome',
                                    args: ['--no-sandbox', '--disable-setuid-sandbox'],
                                });
                                const page = await browser.newPage();
                                await page.setContent(htmlContent);
                                const pdfBuffer = await page.pdf({
                                    format: 'A4',
                                    printBackground: true,
                                    margin: {
                                        top: '1cm',
                                        bottom: '1cm',
                                        left: '1cm',
                                        right: '1cm',
                                    },
                                });
                                await browser.close();
                                const s3Params = {
                                    Bucket: process.env.AWS_BUCKET_NAME,
                                    Key: `summary-report-${organization.id}-${moment().format('YYYY-MM-DD HH:mm:ss')}.pdf`,
                                    Body: pdfBuffer,
                                    ContentType: 'application/pdf',
                                };
                                const uploadResult = await s3.upload(s3Params).promise();
                                const pdfLink = uploadResult.Location;
                                const title = 'Summary Report';
                                const key = 'SUMMARY_REPORT_WHATSAPP';
                                try {
                                    const caption = 'Summary Report';
                                    const filename = 'Summary-Report.pdf';
                                    await (0, whatsapp_service_1.sendDocumentTextMessage)(user === null || user === void 0 ? void 0 : user.mobileNumber, pdfLink, caption, filename, (_h = user === null || user === void 0 ? void 0 : user.organization) === null || _h === void 0 ? void 0 : _h.id, user === null || user === void 0 ? void 0 : user.id, title, key);
                                }
                                catch (error) {
                                    console.error('Error sending document message for user:', user.mobileNumber, error);
                                }
                            }
                        }
                    }
                }
                const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                    .where('id = :id', { id: cornActivityID.id })
                    .getOne();
                getcornActivityID.responseData = 'Success';
                getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                await getcornActivityID.save();
            }
            catch (error) {
                console.log(error);
                const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                    .where('id = :id', { id: cornActivityID.id })
                    .getOne();
                getcornActivityID.responseData = error === null || error === void 0 ? void 0 : error.message;
                getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                await getcornActivityID.save();
            }
        }
    }
    async uploadS3(file, bucket, name, contentType = '') {
        const s3 = this.getS3();
        const params = {
            Bucket: bucket,
            Key: name,
            Body: file,
            ContentType: contentType,
        };
        return new Promise((resolve, reject) => {
            s3.upload(params, (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err.message);
                }
                resolve(data);
            });
        });
    }
    getS3() {
        return new aws_sdk_1.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID_VIDER,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_VIDER,
        });
    }
    async synccalender(data) {
        try {
            const headers = {
                'accept': 'application/json',
                'X-ACCESS-TOKEN': data.token,
                'X-USER-ID': data.email,
                'X-DB-USER-ID': data.userid,
                'X-DB-ORG-ID': data.orgid,
            };
            const url = `${process.env.QUANTUM_API_URL}/events`;
            const response = await axios_1.default.get(url, { headers });
            if ((response === null || response === void 0 ? void 0 : response.status) == 200) {
                return true;
            }
            else {
                return false;
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    async addTaskDataForReport(data) {
        const taskIds = data.map(d => d.id);
        const tasksWithRelations = await (0, typeorm_1.createQueryBuilder)(task_entity_1.default, 'task')
            .leftJoinAndSelect('task.members', 'taskMember')
            .leftJoinAndSelect('task.taskLeader', 'taskLeader')
            .whereInIds(taskIds)
            .getMany();
        const taskMap = new Map(tasksWithRelations.map(t => [
            t.id,
            {
                members: t.members.map((member) => ({
                    id: member.id,
                    fullName: member.fullName,
                })),
                taskLeader: t.taskLeader.map((leader) => ({
                    id: leader.id,
                    fullName: leader.fullName,
                })),
            },
        ]));
        const Data = data.map(d => {
            var _a, _b;
            return (Object.assign(Object.assign({}, d), { members: ((_a = taskMap.get(d.id)) === null || _a === void 0 ? void 0 : _a.members) || [], taskLeaders: ((_b = taskMap.get(d.id)) === null || _b === void 0 ? void 0 : _b.taskLeader) || [] }));
        });
        console.log("final:", JSON.stringify(Data, null, 2));
        return Data;
    }
    async synccalenderstatus(data) {
        try {
            const headers = {
                'accept': 'application/json',
                'X-ACCESS-TOKEN': data.token,
                'X-USER-ID': data.user,
            };
            const url = `${process.env.QUANTUM_API_URL}/sync/status/${data.user}`;
            const response = await axios_1.default.get(url, { headers });
            if ((response === null || response === void 0 ? void 0 : response.status) == 200) {
                return {
                    message: 'Calendar sync is successfull',
                    completed: true,
                };
            }
            else {
                return {
                    message: 'Calendar sync is failed',
                    completed: false,
                };
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    async exportClientProformaInvoiceReport(clientId, payload) {
        var _a, _b;
        const newQuery = Object.assign(Object.assign({}, payload), { offset: 0, limit: 100000000 });
        let invoices = await this.getclientproformainvoiceexport(clientId, newQuery);
        if (!((_a = invoices === null || invoices === void 0 ? void 0 : invoices.result) === null || _a === void 0 ? void 0 : _a.length))
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Proforma Invoice');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Invoice Date', key: 'invoiceDate' },
            { header: 'Invoice Number', key: 'invoiceNumber' },
            { header: 'Amount', key: 'amount' },
            { header: 'Status', key: 'status' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const columnMaxLengths = Array(headers.length).fill(0);
        (_b = invoices === null || invoices === void 0 ? void 0 : invoices.result) === null || _b === void 0 ? void 0 : _b.forEach((invoice) => {
            const rowData = {
                serialNo: serialCounter++,
                invoiceDate: invoice === null || invoice === void 0 ? void 0 : invoice.created_at,
                invoiceNumber: invoice === null || invoice === void 0 ? void 0 : invoice.invoice_number,
                amount: 1 * (invoice === null || invoice === void 0 ? void 0 : invoice.grand_total),
                status: (invoice === null || invoice === void 0 ? void 0 : invoice.status) == 'APPROVAL_PENDING' ? 'INVOICED' : (0, utils_1.getTitle)(invoice === null || invoice === void 0 ? void 0 : invoice.status),
            };
            const row = worksheet.addRow(rowData);
            const statusCell = row.getCell('status');
            if (rowData.status) {
                switch (rowData.status.toLowerCase()) {
                    case 'created':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'invoiced':
                        statusCell.font = { color: { argb: '149ECD' }, bold: true };
                        break;
                    case 'in progress':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'cancelled':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'overdue':
                        statusCell.font = { color: { argb: 'F63338' }, bold: true };
                        break;
                    case 'partially paid':
                        statusCell.font = { color: { argb: 'F49752' }, bold: true };
                        break;
                    case 'converted':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    case 'paid':
                        statusCell.font = { color: { argb: '008000' }, bold: true };
                        break;
                    default:
                        statusCell.font = { color: { argb: '000000' }, bold: true };
                        break;
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getclientproformainvoiceexport(clientId, payload) {
        const clientCondation = (payload === null || payload === void 0 ? void 0 : payload.clientId)
            ? `client_id=${payload.clientId}`
            : `client_group_id=${payload.clientGroupId}`;
        let sql = `select * from proforma_invoice where ${clientCondation}
    AND invoice_number LIKE '%${payload.search}%'; `;
        let result = await (0, typeorm_1.getManager)().query(sql);
        return {
            result,
        };
    }
    async sendTrailExpiredMessage() {
        var _a;
        if (process.env.Cron_Running === 'true') {
            const entityManager = (0, typeorm_1.getManager)();
            const expiryOrgsql = `SELECT * FROM organization WHERE DATE_SUB(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')),
     INTERVAL 2 DAY) = CURDATE() - INTERVAL 1 DAY;`;
            const nextdayExpiryOrgs = await entityManager.query(expiryOrgsql);
            let notifications = [];
            for (let expiryOrg of nextdayExpiryOrgs) {
                const userbyorgIdsql = `SELECT * FROM user WHERE organization_id = ${expiryOrg.id} and type = 'ORGANIZATION';`;
                const usersbyorgId = await entityManager.query(userbyorgIdsql);
                if (Array.isArray(usersbyorgId) && usersbyorgId.length > 0) {
                    for (let orguser of usersbyorgId) {
                        if (expiryOrg &&
                            expiryOrg.config &&
                            expiryOrg.config.expirydate &&
                            ((_a = expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) === null || _a === void 0 ? void 0 : _a.demo) == 'yes') {
                            let newNotification = new notification_entity_1.Notification();
                            newNotification.title = 'Your Trial Period expires Soon';
                            newNotification.body = `Trail period end date is ${expiryOrg.config.expirydate}`;
                            newNotification.user = orguser;
                            notifications.push(newNotification);
                        }
                    }
                }
            }
            if (notifications.length > 0) {
                await notification_entity_1.Notification.save(notifications);
            }
        }
    }
    async sendSubscriptionMail() {
        var _a, _b, _c, _d;
        if (process.env.Cron_Running === 'true') {
            const entityManager = (0, typeorm_1.getManager)();
            const expiryOrgsql = `SELECT * FROM organization WHERE DATE_SUB(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')),
     INTERVAL 2 DAY) = CURDATE() - INTERVAL 1 DAY;`;
            const nextdayExpiryOrgs = await entityManager.query(expiryOrgsql);
            let notifications = [];
            for (let expiryOrg of nextdayExpiryOrgs) {
                if (((_a = expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) === null || _a === void 0 ? void 0 : _a.subscriptionmode) == 'monthly' ||
                    ((_b = expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) === null || _b === void 0 ? void 0 : _b.subscriptionmode) == 'yearly') {
                    const userbyorgIdsql = `SELECT * FROM user WHERE organization_id = ${expiryOrg.id} and type = 'ORGANIZATION';`;
                    const usersbyorgId = await entityManager.query(userbyorgIdsql);
                    if (Array.isArray(usersbyorgId) && usersbyorgId.length > 0) {
                        for (let orguser of usersbyorgId) {
                            if (expiryOrg &&
                                (expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) &&
                                ((_c = expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) === null || _c === void 0 ? void 0 : _c.expirydate) &&
                                ((_d = expiryOrg === null || expiryOrg === void 0 ? void 0 : expiryOrg.config) === null || _d === void 0 ? void 0 : _d.demo) == 'no') {
                                let newNotification = new notification_entity_1.Notification();
                                newNotification.title = 'Your subscription expires soon';
                                newNotification.body = `subscription end date is ${expiryOrg.config.expirydate}`;
                                newNotification.user = orguser;
                                notifications.push(newNotification);
                            }
                        }
                    }
                }
            }
            if (notifications.length > 0) {
                await notification_entity_1.Notification.save(notifications);
            }
        }
    }
    async GetReport() {
        var _a, _b, _c, _d, _e, _f;
        const currentTime = moment();
        const startTime = moment().set({ hour: 3, minute: 55, second: 0 });
        const endTime = moment().set({ hour: 4, minute: 5, second: 0 });
        const cronData = new cron_activity_entity_1.default();
        cronData.cronType = 'REMAINDER MAIL';
        cronData.cronDate = moment().toDate().toString();
        cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
        const cornActivityID = await cronData.save();
        let ErrorMails = [];
        try {
            const statutoryComplaianceEvents = await event_entity_1.default.find({
                where: { defaultOne: true, date: moment().format('YYYY-MM-DD') },
            });
            const organisationActiveList = await organization_entity_1.Organization.createQueryBuilder('organization')
                .select(['organization.id', 'user.id', 'user.fullName', 'user.email'])
                .leftJoin('organization.users', 'user')
                .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate", { expirydate: moment().format('YYYY-MM-DD') })
                .andWhere('user.status = :status', { status: 'active' })
                .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION })
                .getMany();
            for (let organization of organisationActiveList) {
                try {
                    const getDSCExpiry = await (0, typeorm_1.createQueryBuilder)(dsc_register_entity_1.default, 'dscRegister')
                        .leftJoin('dscRegister.organization', 'organization')
                        .where('organization.id = :id', { id: organization.id })
                        .andWhere('dscRegister.expiryDate = :date', { date: moment().format('YYYY-MM-DD') })
                        .getMany();
                    for (let user of organization.users) {
                        try {
                            const getDueTasks = await task_entity_1.default.createQueryBuilder('task')
                                .select([
                                'task.id',
                                'task.name',
                                'task.taskNumber',
                                'task.recurringStatus',
                                'task.status',
                                'client.displayName',
                                'clientGroup.displayName',
                            ])
                                .leftJoin('task.members', 'user')
                                .leftJoin('task.client', 'client')
                                .leftJoin('task.clientGroup', 'clientGroup')
                                .where('user.id = :id', { id: user.id })
                                .andWhere('task.dueDate = :duedate', { duedate: moment().format('YYYY-MM-DD') })
                                .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
                                recurringStatus: types_1.TaskRecurringStatus.CREATED,
                            })
                                .andWhere('task.status IN (:...statuses)', {
                                statuses: [
                                    types_1.TaskStatusEnum.TODO,
                                    types_1.TaskStatusEnum.IN_PROGRESS,
                                    types_1.TaskStatusEnum.ON_HOLD,
                                    types_1.TaskStatusEnum.UNDER_REVIEW,
                                ],
                            })
                                .getMany();
                            let dueTasks = [];
                            for (let i of getDueTasks) {
                                i['client']['displayName'] = ((_a = i === null || i === void 0 ? void 0 : i['client']) === null || _a === void 0 ? void 0 : _a['displayName'])
                                    ? (_b = i === null || i === void 0 ? void 0 : i['client']) === null || _b === void 0 ? void 0 : _b['displayName']
                                    : (_c = i === null || i === void 0 ? void 0 : i['clientGroup']) === null || _c === void 0 ? void 0 : _c['displayName'];
                                dueTasks.push(i);
                            }
                            const events = await (0, typeorm_1.createQueryBuilder)(event_entity_1.default, 'event')
                                .select([
                                'event.title',
                                'event.location',
                                'event.startTime',
                                'event.endTime',
                                'client.displayName',
                                'clientGroup.displayName',
                                'task.name',
                            ])
                                .leftJoin('event.task', 'task')
                                .leftJoin('event.client', 'client')
                                .leftJoin('event.clientGroup', 'clientGroup')
                                .leftJoin('event.user', 'user')
                                .where('user.id = :id', { id: user.id })
                                .andWhere('event.date = :date', { date: moment().format('YYYY-MM-DD') })
                                .getMany();
                            let generalEvents = [];
                            let taskEvents = [];
                            for (let i of events) {
                                i['client']['displayName'] = ((_d = i === null || i === void 0 ? void 0 : i['client']) === null || _d === void 0 ? void 0 : _d['displayName'])
                                    ? (_e = i === null || i === void 0 ? void 0 : i['client']) === null || _e === void 0 ? void 0 : _e['displayName']
                                    : (_f = i === null || i === void 0 ? void 0 : i['clientGroup']) === null || _f === void 0 ? void 0 : _f['displayName'];
                                if (i === null || i === void 0 ? void 0 : i.task) {
                                    i['duration'] = `${moment(i === null || i === void 0 ? void 0 : i.startTime).format('hh:mm A')} - ${moment(i === null || i === void 0 ? void 0 : i.endTime).format('hh:mm A')}`;
                                    taskEvents.push(i);
                                }
                                else {
                                    i['duration'] = `${moment(i === null || i === void 0 ? void 0 : i.startTime).format('hh:mm A')} - ${moment(i === null || i === void 0 ? void 0 : i.endTime).format('hh:mm A')}`;
                                    generalEvents.push(i);
                                }
                            }
                            let mailData = {
                                events: (statutoryComplaianceEvents === null || statutoryComplaianceEvents === void 0 ? void 0 : statutoryComplaianceEvents.length) ? statutoryComplaianceEvents : null,
                                getDSCExpiry: (getDSCExpiry === null || getDSCExpiry === void 0 ? void 0 : getDSCExpiry.length) ? getDSCExpiry : null,
                                getDueTasks: (dueTasks === null || dueTasks === void 0 ? void 0 : dueTasks.length) ? dueTasks : null,
                                taskEvents: (taskEvents === null || taskEvents === void 0 ? void 0 : taskEvents.length) ? taskEvents : null,
                                generalEvents: (generalEvents === null || generalEvents === void 0 ? void 0 : generalEvents.length) ? generalEvents : null,
                                fullname: user === null || user === void 0 ? void 0 : user.fullName,
                                email: user === null || user === void 0 ? void 0 : user.email,
                            };
                            if ((statutoryComplaianceEvents === null || statutoryComplaianceEvents === void 0 ? void 0 : statutoryComplaianceEvents.length) ||
                                (getDSCExpiry === null || getDSCExpiry === void 0 ? void 0 : getDSCExpiry.length) ||
                                (getDueTasks === null || getDueTasks === void 0 ? void 0 : getDueTasks.length) ||
                                (taskEvents === null || taskEvents === void 0 ? void 0 : taskEvents.length) ||
                                (generalEvents === null || generalEvents === void 0 ? void 0 : generalEvents.length)) {
                                try {
                                    let data = mailData;
                                    let subject = `Focused Goals for Today`;
                                    let email = user === null || user === void 0 ? void 0 : user.email;
                                    let filePath = 'remainder-mail';
                                    let html = {};
                                    if (filePath == '') {
                                        html = data;
                                    }
                                    else {
                                        try {
                                            let templatePath = `src/emails/templates/${filePath}.ejs`;
                                            let templateStr = fs.readFileSync(templatePath, 'utf-8');
                                            let template = ejs.compile(templateStr.toString());
                                            html = filePath == '' ? data : template(data);
                                        }
                                        catch (error) {
                                            console.log('error', error);
                                        }
                                    }
                                    let mailOptions = {
                                        from: {
                                            name: 'Vider',
                                            address: process.env.FROM_EMAIL,
                                        },
                                        to: '<EMAIL>',
                                        subject: subject,
                                        html: html,
                                    };
                                    await this.emailThrottleService.enqueueEmail(mailOptions.to, mailOptions.subject, mailOptions.html, organization.id);
                                }
                                catch (error) {
                                    ErrorMails.push((error === null || error === void 0 ? void 0 : error.message) ? error.message : error);
                                }
                            }
                        }
                        catch (error) {
                            ErrorMails.push((error === null || error === void 0 ? void 0 : error.message) ? error.message : error);
                        }
                    }
                }
                catch (error) {
                    ErrorMails.push((error === null || error === void 0 ? void 0 : error.message) ? error.message : error);
                }
            }
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = ErrorMails.length ? ErrorMails.join(',') : 'Success';
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
        }
        catch (error) {
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = ErrorMails.length ? ErrorMails.join(',') : error;
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
            console.log(error);
        }
    }
    async sendCronEmail() {
        if (process.env.Cron_Running === 'true') {
            const date = moment().format('YYYY-MM-DD');
            const getCronData = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('DATE(cronActivity.cronDate) = :date', { date })
                .getMany();
            let mailData = {
                cronData: getCronData,
                fullname: 'Shashank',
                email: '<EMAIL>',
                environment: process.env.WEBSITE_URL,
            };
            try {
                let data = mailData;
                let subject = `Cron Data`;
                let email = '<EMAIL>';
                let filePath = 'cron-mail';
                let html = {};
                if (filePath == '') {
                    html = data;
                }
                else {
                    try {
                        let templatePath = `src/emails/templates/${filePath}.ejs`;
                        let templateStr = ejs.fileLoader(templatePath);
                        let template = ejs.compile(templateStr.toString());
                        html = filePath == '' ? data : template(data);
                    }
                    catch (error) {
                        console.log('error', error);
                    }
                }
                let mailOptions = {
                    from: {
                        name: 'Vider',
                        address: process.env.FROM_EMAIL,
                    },
                    to: email,
                    subject: subject,
                    html: html,
                };
                transporter.sendMail(mailOptions, function (error, info) {
                    if (error) {
                        console.log(error);
                    }
                    else {
                        console.log(info.response);
                    }
                });
            }
            catch (error) {
                console.log(error);
            }
        }
    }
    async addSchedulingOrganization() {
        if (process.env.WEBSITE_URL === 'https://atom.vider.in' &&
            process.env.Cron_Running === 'true') {
            const organizationId = 514;
            const user = await user_entity_1.User.findOne({ where: { organization: organizationId } });
            const autClients = await aut_client_credentials_entity_1.default.find({
                where: { organizationId: organizationId, status: aut_client_credentials_entity_1.IncomeTaxStatus.ENABLE },
            });
            if (autClients) {
                for (let item of autClients) {
                    const automationMachines = new automation_machines_entity_1.default();
                    automationMachines.autoCredentials = item;
                    automationMachines.modules = ['OD', 'EP'];
                    automationMachines.type = automation_machines_entity_1.TypeEnum.INCOMETAX;
                    automationMachines.status = 'INQUEUE';
                    automationMachines.user = user;
                    await automationMachines.save();
                }
            }
            const gstrClients = await gstrCredentials_entity_1.default.find({
                where: { organizationId: organizationId, status: gstrCredentials_entity_1.GstrStatus.ENABLE },
            });
            if (gstrClients) {
                for (let item of gstrClients) {
                    const automationMachines = new automation_machines_entity_1.default();
                    automationMachines.gstrCredentials = item;
                    automationMachines.modules = ['P', 'NAO', 'ANO'];
                    automationMachines.type = automation_machines_entity_1.TypeEnum.GSTR;
                    automationMachines.status = 'INQUEUE';
                    automationMachines.user = user;
                    await automationMachines.save();
                }
            }
        }
    }
    async disableWhatsappForExpiredOrganizations() {
        var _a;
        if (process.env.Cron_Running === 'true') {
            const cronData = new cron_activity_entity_1.default();
            cronData.cronType = 'Disable Whatsapp To ExpiredOrgs';
            cronData.cronDate = moment().toDate().toString();
            cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const cornActivityID = await cronData.save();
            const organisationExpiredList = await organization_entity_1.Organization.createQueryBuilder('organization')
                .select(['organization.id', 'user.id', 'user.fullName', 'user.email'])
                .leftJoin('organization.users', 'user')
                .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') < :expirydate", { expirydate: moment().format('YYYY-MM-DD') })
                .andWhere('user.status = :status', { status: 'active' })
                .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION })
                .getMany();
            for (const org of organisationExpiredList) {
                const organizationPreferences = await organization_preferences_entity_1.default.findOne({
                    where: { organization: org === null || org === void 0 ? void 0 : org.id },
                });
                const whatsappCheck = (_a = organizationPreferences === null || organizationPreferences === void 0 ? void 0 : organizationPreferences.notificationConfig) === null || _a === void 0 ? void 0 : _a.whatsappPreferences;
                if (whatsappCheck) {
                    organizationPreferences.notificationConfig.whatsappPreferences = false;
                    await organization_preferences_entity_1.default.save(Object.assign(Object.assign({}, organizationPreferences), { notificationConfig: organizationPreferences.notificationConfig }));
                }
            }
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = 'success';
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
        }
    }
    async InvoiceReminder(data, userId) {
        var _a, _b;
        try {
            const { invoiceData, invoiceBuffer } = data;
            const { organization, client, clients, clientGroup, invoiceNumber, invoiceDueDate, invoiceDate, grandTotal, id, status, } = invoiceData;
            const invoiceDateFormat = new Date(invoiceDate).toLocaleDateString('en-GB');
            const invoiceDueDateFormat = new Date(invoiceDueDate).toLocaleDateString('en-GB');
            let statusText = status;
            const entityManager = (0, typeorm_1.getManager)();
            if ((status === 'APPROVAL_PENDING' || status === 'PARTIALLY_PAID') &&
                moment().format('YYYY-MM-DD') > moment(invoiceDueDate).format('YYYY-MM-DD')) {
                statusText = 'Overdue';
            }
            else if (status === 'APPROVAL_PENDING') {
                statusText = 'Invoiced';
            }
            else {
                const statusMap = {
                    PARTIALLY_PAID: 'Partially paid',
                };
                statusText = statusMap[status] || 'Unknown';
            }
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const uploadData = await this.uploadS3(Buffer.from(invoiceBuffer), bucketS3, `invoice/${id}.pdf`, 'application/pdf');
            const { Location } = uploadData;
            const admins = await (0, re_use_1.getAdminIDsBasedOnOrganizationId)(organization === null || organization === void 0 ? void 0 : organization.id);
            const client_id = client === null || client === void 0 ? void 0 : client.id;
            const orgQuery = `SELECT organization_id FROM client where id = ${client_id};`;
            const orgIdSql = await entityManager.query(orgQuery);
            const orgId = (_a = orgIdSql[0]) === null || _a === void 0 ? void 0 : _a.organization_id;
            const orgdetails = await organization_entity_1.Organization.findOne({ id: orgId });
            const addressParts = [
                orgdetails.buildingNo || '',
                orgdetails.floorNumber || '',
                orgdetails.buildingName || '',
                orgdetails.street || '',
                orgdetails.location || '',
                orgdetails.city || '',
                orgdetails.district || '',
                orgdetails.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
            const address = addressParts.join(', ') + pincode;
            const remindData = {
                clientName: client === null || client === void 0 ? void 0 : client.displayName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDueDate,
                dueDate: invoiceDueDate,
                invoiceAmount: grandTotal,
                legalName: (organization === null || organization === void 0 ? void 0 : organization.tradeName) || (organization === null || organization === void 0 ? void 0 : organization.legalName),
                userId: userId,
                address: address,
                phoneNumber: organization === null || organization === void 0 ? void 0 : organization.mobileNumber,
                mail: organization === null || organization === void 0 ? void 0 : organization.email,
            };
            let key = 'INVOICE_REMINDER_MAIL';
            const orgPreferences = await organization_preferences_entity_1.default.findOne({
                where: { organization: organization.id },
            });
            const clientPreferences = (_b = orgPreferences === null || orgPreferences === void 0 ? void 0 : orgPreferences.clientPreferences) === null || _b === void 0 ? void 0 : _b.email;
            if (clientPreferences && clientPreferences[key]) {
                await (0, newemails_1.sendnewMail)({
                    id: userId,
                    key: 'INVOICE_REMINDER_MAIL',
                    email: client.email,
                    data: remindData,
                    filePath: 'invoice-remind',
                    subject: `Invoice Reminder`,
                    clientMail: 'ORGANIZATION_CLIENT_EMAIL',
                });
            }
            for (let admin of admins) {
                const userDetails = await (0, re_use_1.getUserDetails)(admin);
                const { full_name: fullName, mobile_number: mobileNumber, id: userId } = userDetails;
                const clientName = client
                    ? (client === null || client === void 0 ? void 0 : client.displayName.length) > 0
                        ? client === null || client === void 0 ? void 0 : client.displayName
                        : client === null || client === void 0 ? void 0 : client.fullName
                    : clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.displayName;
                const title = 'Invoice Created';
                const whatsappOptions = {
                    title: 'invoice-reminder-client',
                    userId: userId,
                    orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                    to: (0, fullMobileWithCountry_1.fullMobileNumberWithCountry)(client ? client === null || client === void 0 ? void 0 : client.mobileNumber : clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.mobileNumber, client ? client === null || client === void 0 ? void 0 : client.countryCode : clientGroup === null || clientGroup === void 0 ? void 0 : clientGroup.countryCode),
                    name: 'invoice_reminder_client',
                    header: [
                        {
                            type: 'document',
                            link: Location,
                        },
                    ],
                    body: [
                        clientName,
                        clientName,
                        invoiceNumber,
                        invoiceDateFormat,
                        grandTotal,
                        invoiceDueDateFormat,
                        statusText,
                    ],
                    fileName: `Invoice-${invoiceNumber}`,
                    key: 'INVOICE_REMINDER_WHATSAPP',
                };
                await (0, whatsapp_service_1.sendWhatsAppTemplateMessageUS)(whatsappOptions);
            }
        }
        catch (err) {
            throw new common_1.BadRequestException(err);
        }
    }
    async getOrganizationPreference(userId) {
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const organizationPreferences = await organization_preferences_entity_1.default.findOne({
            where: { organization: user.organization },
        });
        return organizationPreferences;
    }
    async sendOrganizationExpiryAlertMail() {
        try {
            const today = moment().format('YYYY-MM-DD');
            const nextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
            const unexpiredOrganizations = await organization_entity_1.Organization.createQueryBuilder('organization')
                .leftJoinAndSelect('organization.users', 'user')
                .leftJoin('user.role', 'role')
                .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') between :today AND :nextSeventhDay", { today, nextSeventhDay })
                .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION })
                .andWhere('role.name = :role', { role: 'Admin' })
                .getMany();
            try {
                for (let organization of unexpiredOrganizations) {
                    const { users: [user], } = organization;
                    const config = organization === null || organization === void 0 ? void 0 : organization.config;
                    const expiryDate = new Date(config.expirydate);
                    const now = new Date();
                    const diffDays = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                    const mailOptions = {
                        data: {
                            days: diffDays,
                            legalName: organization === null || organization === void 0 ? void 0 : organization.legalName,
                            userId: user === null || user === void 0 ? void 0 : user.id,
                        },
                        email: organization === null || organization === void 0 ? void 0 : organization.email,
                        filePath: 'org-expire-admin',
                        subject: '**Alert** | Action Required | Organization is About to Expire',
                        key: 'TO_ADMIN',
                        id: user === null || user === void 0 ? void 0 : user.id,
                    };
                    const msg = await (0, newemails_1.sendnewMail)(mailOptions);
                }
                return 'Cron Executed and mails sent Successfully to Admins!';
            }
            catch (error) {
                console.log(`Error in getting expiring organization records in cron:`, error);
            }
        }
        catch (error) {
            return console.log('getExpiringOrganizations ERROR', error);
        }
    }
};
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_3PM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "summaryReport", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_6AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "sendTrailExpiredMessage", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_5AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "sendSubscriptionMail", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_4AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "GetReport", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_7AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "sendCronEmail", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_7PM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "addSchedulingOrganization", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_1AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommonService.prototype, "disableWhatsappForExpiredOrganizations", null);
CommonService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [email_throttle_service_1.EmailThrottleService])
], CommonService);
exports.CommonService = CommonService;
async function readLocalOrgDetails(existingUser) {
    try {
        let user = await user_entity_1.User.findOne({ where: { id: existingUser }, relations: ['organization'] });
        const filePath = './orgdetails.json';
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const jsonData = JSON.parse(fileContent);
        const organizationFilter = jsonData.filter((item) => item.organization === user.organization.id);
        return organizationFilter;
    }
    catch (error) {
        console.error('Error reading JSON file:', error.message);
        return null;
    }
}
exports.readLocalOrgDetails = readLocalOrgDetails;
async function writeLocalOrgDetails(data) {
    try {
        const filePath = './orgdetails.json';
        console.error('writing JSON file');
        const jsonData = JSON.stringify(data, null, 2);
        fs.writeFileSync(filePath, jsonData, 'utf-8');
    }
    catch (error) {
        console.error('Error writing JSON file:', error.message);
    }
}
exports.writeLocalOrgDetails = writeLocalOrgDetails;
const getclientinvoicebilled = (payload) => {
    let sql = `SELECT 
  t.id, 
  t.name, 
  t.task_number AS tasknumber, 
  t.status AS status, 
  DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
  DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
  SUM(i.grand_total) AS amount,
  (SELECT COUNT(t2.id) 
   FROM task t2 
   LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
   WHERE t2.client_id = '${payload.clientId}'
       AND t2.status != 'deleted' 
       AND t2.status != 'terminated' 
       AND t2.payment_status = 'BILLED'
       AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
       AND t2.parent_task_id IS NULL
  ) AS total_count
FROM task t 
LEFT JOIN invoice i ON i.id = t.invoice_id
WHERE t.client_id = '${payload.clientId}'
  AND t.status != 'deleted' 
  AND t.status != 'terminated' 
  AND t.payment_status = 'BILLED'
  AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
  AND t.parent_task_id IS NULL 
GROUP BY t.id
LIMIT ${payload.pageCount} OFFSET ${payload.offset};`;
    let result = (0, typeorm_1.getManager)().query(sql);
    return {
        result,
    };
};
exports.getclientinvoicebilled = getclientinvoicebilled;
//# sourceMappingURL=common.service.js.map