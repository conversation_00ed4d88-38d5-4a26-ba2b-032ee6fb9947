"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EmailThrottleService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailThrottleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const email_queue_entity_1 = require("./email-queue.entity");
const nodemailer = require("nodemailer");
const defaultsmtp = {
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
};
let EmailThrottleService = EmailThrottleService_1 = class EmailThrottleService {
    constructor(queueRepo) {
        this.queueRepo = queueRepo;
        this.logger = new common_1.Logger(EmailThrottleService_1.name);
        this.sending = false;
        this.batchSize = parseInt(process.env.EMAIL_BATCH_SIZE) || 5;
        this.maxRetries = parseInt(process.env.EMAIL_MAX_RETRIES) || 3;
        this.processingDelay = parseInt(process.env.EMAIL_PROCESSING_DELAY) || 50;
    }
    async enqueueEmail(to, subject, body, organizationId, smtpConfig, attachments) {
        const email = this.queueRepo.create({
            to,
            subject,
            body,
            organizationId,
            smtpConfig: smtpConfig ? JSON.stringify(smtpConfig) : null,
            attachments: attachments ? JSON.stringify(attachments) : null
        });
        await this.queueRepo.save(email);
        this.logger.log(`Email queued: ${to}`);
    }
    async handleEmailQueue() {
        if (this.sending)
            return;
        this.sending = true;
        try {
            const batch = await this.queueRepo.find({
                order: { createdAt: 'ASC' },
                take: this.batchSize,
            });
            console.log(batch, 'batch');
            for (const email of batch) {
                try {
                    await this.sendEmail(email);
                    await this.queueRepo.remove(email);
                    this.logger.log(`Email processed and removed from queue: ${email.to}`);
                }
                catch (err) {
                    email.retryCount++;
                    this.logger.warn(`Email failed for ${email.to}, retry count: ${email.retryCount}, error: ${err.message}`);
                    if (email.retryCount < this.maxRetries) {
                        await this.queueRepo.save(email);
                        this.logger.log(`Email queued for retry: ${email.to}`);
                    }
                    else {
                        await this.queueRepo.remove(email);
                        this.logger.error(`Email permanently failed after ${this.maxRetries} retries, removed from queue: ${email.to}`);
                    }
                }
            }
        }
        finally {
            this.sending = false;
        }
    }
    async sendEmail(email) {
        return new Promise((resolve, reject) => {
            var _a, _b;
            try {
                const smtpConfig = email.smtpConfig ? JSON.parse(email.smtpConfig) : null;
                const attachments = email.attachments ? JSON.parse(email.attachments) : null;
                const mailOptions = {
                    from: (smtpConfig === null || smtpConfig === void 0 ? void 0 : smtpConfig.name)
                        ? `"${smtpConfig.name}" <${(_a = smtpConfig.auth) === null || _a === void 0 ? void 0 : _a.user}>`
                        : ((_b = smtpConfig === null || smtpConfig === void 0 ? void 0 : smtpConfig.auth) === null || _b === void 0 ? void 0 : _b.user) || process.env.FROM_EMAIL || "<EMAIL>",
                    to: email.to,
                    subject: email.subject,
                    html: email.body,
                    attachments: attachments
                };
                if (smtpConfig) {
                    smtpConfig === null || smtpConfig === void 0 ? true : delete smtpConfig.name;
                    if ((smtpConfig === null || smtpConfig === void 0 ? void 0 : smtpConfig.service) === "outlook" || (smtpConfig === null || smtpConfig === void 0 ? void 0 : smtpConfig.service) === "yahoo") {
                        smtpConfig === null || smtpConfig === void 0 ? true : delete smtpConfig.service;
                    }
                }
                const smtp = smtpConfig || defaultsmtp;
                const customTransporter = nodemailer.createTransport(smtp);
                customTransporter.sendMail(mailOptions, (error, info) => {
                    if (error) {
                        this.logger.error(`Failed to send email to ${email.to}: ${error.message}`);
                        reject(error);
                    }
                    else {
                        this.logger.log(`Email sent successfully to ${email.to}`);
                        resolve();
                    }
                });
            }
            catch (error) {
                this.logger.error(`Error preparing email for ${email.to}: ${error.message}`);
                reject(error);
            }
        });
    }
    async getQueueStatus() {
        const totalEmails = await this.queueRepo.count();
        const pendingEmails = await this.queueRepo.count({ where: { retryCount: 0 } });
        const retryingEmails = await this.queueRepo.count({
            where: { retryCount: 1 }
        });
        const failingEmails = await this.queueRepo.count({
            where: { retryCount: this.maxRetries - 1 }
        });
        return {
            totalEmails,
            pendingEmails,
            retryingEmails,
            failingEmails,
            isProcessing: this.sending
        };
    }
    async getFailedEmails() {
        return await this.queueRepo.find({
            where: { retryCount: this.maxRetries - 1 },
            order: { createdAt: 'DESC' },
            take: 50
        });
    }
    async getConfiguration() {
        return {
            batchSize: this.batchSize,
            maxRetries: this.maxRetries,
            processingDelay: this.processingDelay,
            cronExpression: 'EVERY_SECOND',
            description: 'Email throttling service configuration'
        };
    }
};
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_SECOND),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailThrottleService.prototype, "handleEmailQueue", null);
EmailThrottleService = EmailThrottleService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(email_queue_entity_1.EmailQueue, 'sqliteConnection')),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EmailThrottleService);
exports.EmailThrottleService = EmailThrottleService;
//# sourceMappingURL=email-throttle.service.js.map