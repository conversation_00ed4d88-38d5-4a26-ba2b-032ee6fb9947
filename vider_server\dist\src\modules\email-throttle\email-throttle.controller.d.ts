import { EmailThrottleService } from './email-throttle.service';
interface QueueEmailDto {
    to: string;
    subject: string;
    body: string;
    organizationId?: number;
    smtpConfig?: any;
    attachments?: any[];
}
interface BulkQueueEmailDto {
    emails: QueueEmailDto[];
}
export declare class EmailThrottleController {
    private readonly emailThrottleService;
    constructor(emailThrottleService: EmailThrottleService);
    queueEmails(body: QueueEmailDto): Promise<{
        message: string;
    }>;
    queueBulkEmails(body: BulkQueueEmailDto): Promise<{
        message: string;
        queued: number;
        failed: number;
        invalidEmails: any[];
    }>;
    getQueueStatus(): Promise<{
        totalEmails: number;
        pendingEmails: number;
        retryingEmails: number;
        failingEmails: number;
        isProcessing: boolean;
    }>;
    getFailedEmails(): Promise<import("./email-queue.entity").EmailQueue[]>;
    getConfiguration(): Promise<{
        batchSize: number;
        maxRetries: number;
        processingDelay: number;
        cronExpression: string;
        description: string;
    }>;
}
export {};
