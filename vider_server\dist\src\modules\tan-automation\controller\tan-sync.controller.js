"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanSyncController = void 0;
const common_1 = require("@nestjs/common");
const tan_sync_service_1 = require("../service/tan-sync.service");
const jwt_auth_guard_1 = require("../../users/jwt/jwt-auth.guard");
let TanSyncController = class TanSyncController {
    constructor(service) {
        this.service = service;
    }
    createMachine(id, body, req) {
        const { userId } = req.user;
        return this.service.createMachine(userId, id, body);
    }
    bulkAutomationSync(body, req) {
        const { userId } = req.user;
        return this.service.bulkAutomationSync(userId, body);
    }
    checkAutomationInOrganization(req) {
        const { userId } = req.user;
        return this.service.checkAutomationInOrganization(userId);
    }
    getIncometexUpdates(req, query) {
        const { userId } = req.user;
        return this.service.getIncometexUpdates(userId);
    }
    findForm(req, id) {
        const { userId } = req.user;
        return this.service.getUpdatedItem(userId, id);
    }
    disableIncomeTaxClient(body, req) {
        const { userId } = req.user;
        return this.service.disableIncomeTaxClient(userId, body);
    }
    disableIncomeTaxSingleClient(req, id) {
        const { userId } = req.user;
        return this.service.disableIncomeTaxSingleClient(id, userId);
    }
    enableIncometaxClient(req, id) {
        const { userId } = req.user;
        return this.service.enableIncometaxClient(id, userId);
    }
    enableBulkIncometaxClient(req, body) {
        const { userId } = req.user;
        return this.service.enableBulkIncometaxClient(userId, body);
    }
    getDeletedIncomeTaxClients(req, query) {
        const { userId } = req.user;
        return this.service.getDeletedIncomeTaxClients(userId, query);
    }
    async exportIncomeTaxTandeletedClients(req, body) {
        const query = body;
        const { userId } = req.user;
        return this.service.exportIncomeTaxTandeletedClients(userId, query);
    }
    getTanBulkSyncStatus(req) {
        const { userId } = req.user;
        return this.service.getTanBulkSyncStatus(userId);
    }
    enableTanStatus(req) {
        const { userId } = req.user;
        return this.service.updateTanEnableStatus(userId);
    }
    updateTanDisableStatus(req) {
        const { userId } = req.user;
        return this.service.updateTanDisableStatus(userId);
    }
    async organizationTanScheduling(req, body) {
        const { userId } = req.user;
        return this.service.organizationTanScheduling(userId, body);
    }
    getTraceBulkSyncStatus(req) {
        const { userId } = req.user;
        return this.service.getTraceBulkSyncStatus(userId);
    }
    enableTraceStatus(req) {
        const { userId } = req.user;
        return this.service.updateTraceEnableStatus(userId);
    }
    updateTraceDisableStatus(req) {
        const { userId } = req.user;
        return this.service.updateTraceDisableStatus(userId);
    }
    async organizationTraceScheduling(req, body) {
        const { userId } = req.user;
        return this.service.organizationTraceScheduling(userId, body);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('auto-machine/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "createMachine", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('bulkSync'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "bulkAutomationSync", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('checkNoOfSync'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "checkAutomationInOrganization", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('new-updates'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "getIncometexUpdates", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('update/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "findForm", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableIncomeTaxClient'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "disableIncomeTaxClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableIncomeTax/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "disableIncomeTaxSingleClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('enableIncomeTax/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "enableIncometaxClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('enableIncomeTax/bulk-enable'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "enableBulkIncometaxClient", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('deletedClients'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "getDeletedIncomeTaxClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('/deletedIncomeTaxreport'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanSyncController.prototype, "exportIncomeTaxTandeletedClients", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('bulkSyncStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "getTanBulkSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('enableStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "enableTanStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableStatus'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "updateTanDisableStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('scheduling'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanSyncController.prototype, "organizationTanScheduling", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('bulkSyncStatus-trace'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "getTraceBulkSyncStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('enableStatus-trace'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "enableTraceStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('disableStatus-trace'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TanSyncController.prototype, "updateTraceDisableStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('scheduling-trace'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TanSyncController.prototype, "organizationTraceScheduling", null);
TanSyncController = __decorate([
    (0, common_1.Controller)('tan-sync'),
    __metadata("design:paramtypes", [tan_sync_service_1.TanSyncService])
], TanSyncController);
exports.TanSyncController = TanSyncController;
//# sourceMappingURL=tan-sync.controller.js.map