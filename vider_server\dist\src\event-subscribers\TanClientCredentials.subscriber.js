"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanClientCredentialsSubscriber = void 0;
const typeorm_1 = require("typeorm");
const organization_preferences_entity_1 = require("../modules/organization-preferences/entity/organization-preferences.entity");
const tan_client_credentials_entity_1 = require("../modules/tan-automation/entity/tan-client-credentials.entity");
const axios = require('axios');
let clientOldDetails;
let TanClientCredentialsSubscriber = class TanClientCredentialsSubscriber {
    constructor(connection) {
        this.connection = connection;
        connection.subscribers.push(this);
    }
    listenTo() {
        return tan_client_credentials_entity_1.default;
    }
    async beforeUpdate(event) {
        clientOldDetails = event === null || event === void 0 ? void 0 : event.databaseEntity;
    }
    async afterInsert(event) {
        try {
            const { organizationId } = event.entity;
            const organizationPreferences = await organization_preferences_entity_1.default.createQueryBuilder('orgPref')
                .where('orgPref.organization = :organizationId', { organizationId })
                .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.tan') = 'YES'")
                .getOne();
            if (organizationPreferences) {
                let data = '';
                let config = {
                    method: 'get',
                    maxBodyLength: Infinity,
                    url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/tan`,
                    headers: {},
                    data: data,
                };
                axios
                    .request(config)
                    .then((response) => {
                    var _a;
                    if (response === null || response === void 0 ? void 0 : response.data) {
                        const schedule = JSON.parse((_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.schedule);
                        let data1 = JSON.stringify({
                            modules: ['P', 'F', 'EC'],
                            orgId: organizationId,
                            type: 'TAN',
                            schedules: schedule,
                        });
                        let config1 = {
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                            headers: {
                                'X-USER-ID': response.data.userId,
                                'Content-Type': 'application/json',
                            },
                            data: data1,
                        };
                        axios
                            .request(config1)
                            .then((response) => {
                        })
                            .catch((error) => {
                            console.log('error in scheduling call in subscriber camunda', error.message);
                        });
                    }
                })
                    .catch((error) => {
                    console.log('error in scheduling call in subscriber camunda', error.message);
                });
            }
        }
        catch (error) {
            console.log('Error occur in AutClientCredentialsSubscriber', error);
        }
    }
    async afterUpdate(event) {
        try {
            const { status, organizationId } = event.entity;
            if (clientOldDetails.status !== status) {
                const organizationPreferences = await organization_preferences_entity_1.default.createQueryBuilder('orgPref')
                    .where('orgPref.organization = :organizationId', { organizationId })
                    .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.tan') = 'YES'")
                    .getOne();
                if (organizationPreferences) {
                    let data = '';
                    let config = {
                        method: 'get',
                        maxBodyLength: Infinity,
                        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/tan`,
                        headers: {},
                        data: data,
                    };
                    axios
                        .request(config)
                        .then((response) => {
                        var _a;
                        if (response.data) {
                            const schedule = JSON.parse((_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.schedule);
                            let data1 = JSON.stringify({
                                modules: ['P', 'F', 'EC'],
                                orgId: organizationId,
                                type: 'TAN',
                                schedules: schedule,
                            });
                            let config1 = {
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                                headers: {
                                    'X-USER-ID': response.data.userId,
                                    'Content-Type': 'application/json',
                                },
                                data: data1,
                            };
                            axios
                                .request(config1)
                                .then((response) => {
                            })
                                .catch((error) => {
                                console.log('error in scheduling call in subscriber camunda', error.message);
                            });
                        }
                    })
                        .catch((error) => {
                        console.error('error in scheduling call in subscriber camunda Error Message:', error.message);
                    });
                }
            }
        }
        catch (error) {
            console.log('Error occur in AutClientCredentialsSubscriber', error);
        }
    }
};
TanClientCredentialsSubscriber = __decorate([
    (0, typeorm_1.EventSubscriber)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], TanClientCredentialsSubscriber);
exports.TanClientCredentialsSubscriber = TanClientCredentialsSubscriber;
//# sourceMappingURL=TanClientCredentials.subscriber.js.map