import { EventEmitter2 } from '@nestjs/event-emitter';
import DocumentCategory from './entity/document-category.entity';
export declare class DocumentCategoryService {
    private eventEmitter;
    constructor(eventEmitter: EventEmitter2);
    create(userId: number, data: any): Promise<DocumentCategory>;
    get(userId: number, query: any): Promise<DocumentCategory[]>;
    delete(ids: number[]): Promise<{
        success: boolean;
    }>;
}
