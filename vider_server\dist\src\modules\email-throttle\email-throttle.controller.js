"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailThrottleController = void 0;
const common_1 = require("@nestjs/common");
const email_throttle_service_1 = require("./email-throttle.service");
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function sanitizeInput(input) {
    if (!input)
        return '';
    return input.trim().replace(/[<>]/g, '');
}
let EmailThrottleController = class EmailThrottleController {
    constructor(emailThrottleService) {
        this.emailThrottleService = emailThrottleService;
    }
    async queueEmails(body) {
        if (!isValidEmail(body.to)) {
            throw new common_1.BadRequestException('Invalid email address');
        }
        const sanitizedSubject = sanitizeInput(body.subject);
        const sanitizedBody = sanitizeInput(body.body);
        if (!sanitizedSubject || !sanitizedBody) {
            throw new common_1.BadRequestException('Subject and body are required');
        }
        await this.emailThrottleService.enqueueEmail(body.to, sanitizedSubject, sanitizedBody, body.organizationId, body.smtpConfig, body.attachments);
        return { message: `Queued email to ${body.to}` };
    }
    async queueBulkEmails(body) {
        if (!body.emails || !Array.isArray(body.emails) || body.emails.length === 0) {
            throw new common_1.BadRequestException('Emails array is required and cannot be empty');
        }
        const validEmails = [];
        const invalidEmails = [];
        for (const e of body.emails) {
            if (!isValidEmail(e.to)) {
                invalidEmails.push({ email: e.to, reason: 'Invalid email address' });
                continue;
            }
            const sanitizedSubject = sanitizeInput(e.subject);
            const sanitizedBody = sanitizeInput(e.body);
            if (!sanitizedSubject || !sanitizedBody) {
                invalidEmails.push({ email: e.to, reason: 'Subject and body are required' });
                continue;
            }
            try {
                await this.emailThrottleService.enqueueEmail(e.to, sanitizedSubject, sanitizedBody, e.organizationId, e.smtpConfig, e.attachments);
                validEmails.push(e.to);
            }
            catch (error) {
                invalidEmails.push({ email: e.to, reason: error.message });
            }
        }
        return {
            message: `${validEmails.length} emails queued successfully.`,
            queued: validEmails.length,
            failed: invalidEmails.length,
            invalidEmails: invalidEmails
        };
    }
    async getQueueStatus() {
        return await this.emailThrottleService.getQueueStatus();
    }
    async getFailedEmails() {
        return await this.emailThrottleService.getFailedEmails();
    }
    async getConfiguration() {
        return await this.emailThrottleService.getConfiguration();
    }
};
__decorate([
    (0, common_1.Post)('queue'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "queueEmails", null);
__decorate([
    (0, common_1.Post)('bulk-queue'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "queueBulkEmails", null);
__decorate([
    (0, common_1.Get)('status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "getQueueStatus", null);
__decorate([
    (0, common_1.Get)('failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "getFailedEmails", null);
__decorate([
    (0, common_1.Get)('config'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailThrottleController.prototype, "getConfiguration", null);
EmailThrottleController = __decorate([
    (0, common_1.Controller)('email-throttle'),
    __metadata("design:paramtypes", [email_throttle_service_1.EmailThrottleService])
], EmailThrottleController);
exports.EmailThrottleController = EmailThrottleController;
//# sourceMappingURL=email-throttle.controller.js.map