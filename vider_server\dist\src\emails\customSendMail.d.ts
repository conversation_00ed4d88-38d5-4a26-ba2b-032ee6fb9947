import { EmailThrottleService } from '../modules/email-throttle/email-throttle.service';
export declare class CustomMailService {
    private readonly emailThrottleService;
    constructor(emailThrottleService: EmailThrottleService);
    customsendmailthrottled({ to, org, data, useThrottling }: any): Promise<unknown>;
}
export declare function customsendmail({ to, org, data, }: any): Promise<unknown>;
