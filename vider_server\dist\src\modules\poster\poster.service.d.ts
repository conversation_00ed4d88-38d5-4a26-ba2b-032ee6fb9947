import { CreatePosterDto } from './dto/create-poster.dto';
import PosterConfig from './poster-config.entity';
import Storage from '../storage/storage.entity';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from 'src/modules/storage/bharath-upload.service';
import { StorageService } from 'src/modules/storage/storage.service';
export declare class PosterService {
    private awsService;
    private oneDriveService;
    private bharathService;
    private storageService;
    constructor(awsService: AwsService, oneDriveService: OneDriveStorageService, bharathService: BharathCloudService, storageService: StorageService);
    create(userId: number, body: CreatePosterDto): Promise<PosterConfig>;
    updateImage(userId: number, data: any): Promise<void>;
    getPosterConfig(userId: number, query: any): Promise<PosterConfig>;
    getPosterEventTypes(): Promise<any[]>;
    getPosterEventsByType(typeName: string): Promise<{
        id: any;
        name: any;
        date: any;
    }[]>;
    createTemplate1(userId: number, body: any): Promise<{
        imageBase64: string;
    }>;
    createTemplate2(userId: number, body: any): Promise<{
        imageBase64: string;
    }>;
    createTemplate3(userId: number, body: any): Promise<{
        imageBase64: string;
    }>;
    getPosters(query: any): Promise<Storage[]>;
}
