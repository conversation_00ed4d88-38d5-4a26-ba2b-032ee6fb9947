import { TanDashboardService } from '../service/tan-dashboard-service';
export declare class TanDashboardController {
    private service;
    constructor(service: TanDashboardService);
    getFormsUdinAnalytics(req: any, query: any): Promise<{
        totalForms: number;
        originalForms: {
            totalOriginalForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
        revisedFroms: {
            totalRevisedForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
        notApplicableForms: {
            totalNotApplicableForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
    }>;
    incometaxClientCheck(req: any, query: any): Promise<{
        filteredRows: import("../entity/tan-client-credentials.entity").default[];
        totalClients: number;
        count: number;
        uniquePansCount: number;
        totalCount: number;
        totalClientsWithTraces: number;
        traceCount: number;
        totalTracesCount: number;
        totalData: import("../entity/tan-client-credentials.entity").default[];
    }>;
    exportTanInvalid(req: any, body: any): Promise<any>;
    getIncometaxConfigStatus(req: any, query: any): Promise<{
        totalLimit: any;
        difference: number;
        presentClients: number;
    }>;
    getFormsAnalytics(req: any, query: any): Promise<{}>;
    getFormsNavigateAnalytics(req: any, query: any): Promise<{
        totalRecords: number;
        data: any[];
    }>;
    getFormsCorrectionAnalytics(req: any, query: any): Promise<{}>;
    eExcelProccedidingFyiNotice(req: any, query: any): Promise<{
        issueData: {
            last1WeekIssued: number;
            last15DaysIssued: number;
            last1MonthIssued: number;
            todayIssued: number;
        };
        responseDueData: {
            last1WeekDue: number;
            last15DaysDue: number;
            last1MonthDue: number;
            todayDue: number;
        };
    }>;
    tracesNotice(req: any, query: any): Promise<{
        issueData: {
            last1WeekIssued: number;
            last15DaysIssued: number;
            last1MonthIssued: number;
            todayIssued: number;
        };
    }>;
    getExcelFyaEvents(req: any, query: any): Promise<{
        type: string;
        id: number;
        clientId: number;
        client: import("../../clients/entity/client.entity").default;
        organizationId: number;
        tanClientCredentialsId: number;
        proceedingName: string;
        pan: string;
        ay: string;
        proceedingLimitationDate: string;
        proceedingStatus: string;
        proceedingConcludedDate: string;
        noticeDin: string;
        noticeSentDate: string;
        noticeSection: string;
        dateOfCompliance: string;
        dateResponseSubmitted: string;
        createdAt: Date;
        updatedAt: Date;
        uuid: string;
    }[]>;
    getExcelResponseDueEvents(req: any, query: any): Promise<{
        type: string;
        id: number;
        clientId: number;
        client: import("../../clients/entity/client.entity").default;
        organizationId: number;
        tanClientCredentialsId: number;
        proceedingName: string;
        pan: string;
        ay: string;
        proceedingLimitationDate: string;
        proceedingStatus: string;
        proceedingConcludedDate: string;
        noticeDin: string;
        noticeSentDate: string;
        noticeSection: string;
        dateOfCompliance: string;
        dateResponseSubmitted: string;
        createdAt: Date;
        updatedAt: Date;
        uuid: string;
    }[]>;
    getTracesEvents(req: any, query: any): Promise<{
        type: string;
        id: number;
        commRefNo: string;
        commCat: string;
        fy: string;
        hidFy: string;
        qt: string;
        hidQt: string;
        formType: string;
        date: string;
        isRead: string;
        description: string;
        commMstrId: string;
        commInbId: string;
        category: string;
        comCatId: string;
        certNum: string;
        createdAt: string;
        updatedAt: string;
        organizationId: number;
        clientId: number;
        client: import("../../clients/entity/client.entity").default;
        tanClientCredentials: import("../entity/tan-client-credentials.entity").default;
    }[]>;
    incometaxTanClientCheck(req: any, query: any): Promise<{
        filteredRows: import("../../automation/entities/automation_machines.entity").default[];
        totalClients: number;
        count: number;
        uniquePansCount: number;
        totalCount: number;
    }>;
    tracesClientCheck(req: any, query: any): Promise<{
        uniquePansCount: number;
        filteredRows: import("../../automation/entities/automation_machines.entity").default[];
        totalClientsWithTraces: number;
        totalCount: number;
    }>;
}
