import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EmailQueue } from './email-queue.entity';
import * as nodemailer from 'nodemailer';




let transporter: any = nodemailer.createTransport({
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
    },
});

@Injectable()
export class EmailThrottleService {
    private readonly logger = new Logger(EmailThrottleService.name);
    private sending = false;


    constructor(
        @InjectRepository(EmailQueue, 'sqliteConnection')
        private readonly queueRepo: Repository<EmailQueue>,
    ) { }

    async enqueueEmail(to: string, subject: string, body: string) {
        const email = this.queueRepo.create({ to, subject, body });
        await this.queueRepo.save(email);
        this.logger.log(`Email queued: ${to}`);
    }

    @Cron(CronExpression.EVERY_SECOND)
    async handleEmailQueue() {
        if (this.sending) return;
        this.sending = true;

        try {
            const batch = await this.queueRepo.find({
                order: { createdAt: 'ASC' },
                take: 5, // ✅ Send 5 emails per second
            });
            console.log(batch, 'batch');
            for (const email of batch) {
                try {
                    await this.sendEmail(email);
                    await this.queueRepo.remove(email);
                } catch (err) {
                    email.retryCount++;
                    if (email.retryCount < 3) {
                        await this.queueRepo.save(email);
                    } else {
                        await this.queueRepo.remove(email);
                    }
                }
            }
        } finally {
            this.sending = false;
        }
    }

    private async sendEmail(email: EmailQueue) {
        // Replace this with your actual email logic (SendGrid, SES, etc.)
        let mailOptions = {
            from: {
                name: 'Vider',
                address: process.env.FROM_EMAIL,
            },
            to: email.to,
            subject: email.subject,
            html: email.body,
        };
        transporter.sendMail(mailOptions, function (error: any, info: any) {
            if (error) {
                console.log(error);
            } else {
            }
        });
        this.logger.log(`Sending email to ${email.to}`);
        await new Promise((r) => setTimeout(r, 50));
    }
}
