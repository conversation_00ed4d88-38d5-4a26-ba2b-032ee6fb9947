"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const noticeOrders_entity_1 = require("../entity/noticeOrders.entity");
const gstrProfile_entity_1 = require("../entity/gstrProfile.entity");
const client_entity_1 = require("../../clients/entity/client.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const automation_machines_entity_1 = require("../../automation/entities/automation_machines.entity");
const gstrCredentials_entity_1 = require("../entity/gstrCredentials.entity");
const gstrAdditionalOrdersAndNotices_entity_1 = require("../entity/gstrAdditionalOrdersAndNotices.entity");
const lodash_1 = require("lodash");
const whatsapp_controller_1 = require("../../whatsapp/whatsapp.controller");
const moment = require("moment");
const gstr_update_tracker_entity_1 = require("../entity/gstr_update_tracker.entity");
const axios_1 = require("axios");
const permission_1 = require("../../tasks/permission");
const ExcelJS = require("exceljs");
const gstrDemands_entity_1 = require("../entity/gstrDemands.entity");
const gstrLedgersBalance_entity_1 = require("../entity/gstrLedgersBalance.entity");
const categoryLabels = {
    individual: 'Individual',
    huf: 'Hindu Undivided Family',
    partnership_firm: 'Partnership Firm',
    llp: 'Limited Liability Partnership',
    company: 'Company',
    opc: 'OPC',
    public: 'Public Limited',
    government: 'Government',
    sec_8: 'Section-8',
    foreign: 'Foreign',
    aop: 'Association of Persons',
    boi: 'Body of Individuals',
    trust: 'Trust',
    public_trust: 'Public Trust',
    private_discretionary_trust: 'Private Discretionary Trust',
    state: 'State',
    central: 'Central',
    local_authority: 'Local Authority',
    artificial_judicial_person: 'Artificial Juridical Person',
};
const ledgerTypeMap = {
    LIABILITY: 'Electronic Liability Register (Return related)',
    CASH: 'Electronic Cash Ledger',
    ITC: 'Electronic Credit Ledger',
    ITC_REVERSAL: 'Electronic Credit Reversal and Re-claimed Statement',
    RCM: 'RCM Liability/ITC Statement',
    NEGATIVE_LIABILITY: 'Negative liability statement - Regular Taxpayers',
};
let GstrService = class GstrService {
    async getOrderNotices(userId, id, query) {
        const { offset, limit } = query;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const checkGstrCredentials = await gstrCredentials_entity_1.default.findOne({
                where: { id: id, organizationId: user.organization.id },
            });
            if (checkGstrCredentials) {
                const additionalRecords = (0, typeorm_1.createQueryBuilder)(noticeOrders_entity_1.default, 'gstrNoticeOrders').where('gstrNoticeOrders.gstrCredentialsId = :id', { id });
                const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        type: 'gstrNoticeOrders.type',
                        issuedBy: 'gstrNoticeOrders.issuedBy',
                        amountOfDemand: 'gstrNoticeOrders.amountOfDemand',
                        dateOfIssuance: 'gstrNoticeOrders.dateOfIssuance',
                        dueDate: 'gstrNoticeOrders.dueDate',
                    };
                    const column = columnMap[sort.column] || sort.column;
                    additionalRecords.orderBy(column, sort.direction.toUpperCase());
                }
                if (offset >= 0) {
                    additionalRecords.skip(offset);
                }
                if (limit) {
                    additionalRecords.take(limit);
                }
                let result = await additionalRecords.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error accure while getting getGstrProfile', error);
        }
    }
    async gstClientNoticeandordersExport(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const gstrid = query.gstrid;
        let gstNoticeorders = await this.getOrderNotices(userId, gstrid, exportQuery);
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Notice & Orders');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Type', key: 'type' },
            { header: 'Date of Issuance', key: 'issuanceDate' },
            { header: 'Issued by', key: 'issuedBy' },
            { header: 'Notice/Order #', key: 'noticeNum' },
            { header: 'Due Date', key: 'dueDate' },
            { header: 'Amount (₹)', key: 'amount' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        gstNoticeorders.result.forEach((noticeorder) => {
            const amount = (noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.amountOfDemand) === ' ' ? 0 : noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.amountOfDemand;
            const rowData = {
                serialNo: serialCounter++,
                gstIn: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.gstIn,
                issuanceDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dateOfIssuance,
                issuedBy: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.issuedBy,
                type: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.type,
                noticeNum: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.orderNumber,
                issuedDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dateOfIssuance,
                dueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dueDate,
                amount: amount,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getOrderNotice(userId, id) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let noticeOrders = await noticeOrders_entity_1.default.findOne({
                where: { id: id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
                relations: ['client'],
            });
            if ((noticeOrders === null || noticeOrders === void 0 ? void 0 : noticeOrders.createdType) === 'MANUAL') {
                noticeOrders = await noticeOrders_entity_1.default.findOne({
                    where: { id: id, organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id },
                    relations: ['client', 'storage'],
                });
            }
            return noticeOrders;
        }
        catch (error) {
            console.log('error occur while getting getOrderNotice');
        }
    }
    async getGstrAdditionalDeailss(userId, id) {
        var _a, _b;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let additionalData = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                where: { id, organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id },
            });
            if ((additionalData === null || additionalData === void 0 ? void 0 : additionalData.createdType) === 'MANUAL') {
                additionalData = await gstrAdditionalOrdersAndNotices_entity_1.default.findOne({
                    where: { id, organizationId: (_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.id },
                    relations: ['storage'],
                });
            }
            let relatedData = [];
            if (additionalData) {
                relatedData = await gstrAdditionalOrdersAndNotices_entity_1.default.find({
                    where: {
                        caseTypeId: additionalData === null || additionalData === void 0 ? void 0 : additionalData.caseTypeId,
                        caseId: additionalData === null || additionalData === void 0 ? void 0 : additionalData.caseId,
                        gstrCredentialsId: additionalData === null || additionalData === void 0 ? void 0 : additionalData.gstrCredentialsId,
                    },
                });
            }
            const groupedData = relatedData.reduce((acc, record) => {
                const type = record.caseFolderTypeName;
                if (!acc[type]) {
                    acc[type] = [];
                }
                acc[type].push(record);
                return acc;
            }, {});
            const categorizedData = Object.entries(groupedData).map(([type, records]) => ({
                type,
                records,
            }));
            return { additionalData, categorizedData };
        }
        catch (error) {
            console.log('error accure while getting getGstrAdditionalDeailss', error);
        }
    }
    async getGstrProfile(userId, id) {
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const checkGstrCredentials = await gstrCredentials_entity_1.default.findOne({
                where: { id: id, organizationId: user.organization.id },
            });
            if (checkGstrCredentials) {
                const lastCompletedMachine = await automation_machines_entity_1.default.findOne({
                    where: { gstrCredentials: id, status: 'COMPLETED' },
                    order: {
                        id: 'DESC',
                    },
                });
                const gstrProfile = await (0, typeorm_1.createQueryBuilder)(gstrProfile_entity_1.default, 'gstrProfile')
                    .leftJoin('gstrProfile.gstrCredentials', 'gstrCredentials')
                    .where('gstrCredentials.id = :gstrCredId', { gstrCredId: id })
                    .getOne();
                return { gstrProfile, lastCompletedMachine, accessDenied: true };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error accure while getting getGstrProfile', error);
        }
    }
    async getGstrClientCompliance(userId, id) {
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const checkGstrCredentials = await gstrCredentials_entity_1.default.findOne({
                where: { id: id, organizationId: user.organization.id },
            });
            if (checkGstrCredentials) {
                const gstrCredentials = await gstrCredentials_entity_1.default.findOne({ where: { id } });
                const clientId = gstrCredentials === null || gstrCredentials === void 0 ? void 0 : gstrCredentials.clientId;
                const client = await client_entity_1.default.findOne({ id: clientId });
                return client;
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error accure while getting getGstrClientCompliance', error);
        }
    }
    async getAddNoticeAndOrders(userId, query) {
        try {
            const { offset, limit, search, financialYear, folderType, type, interval, caseType, responseType, dueInterval, uniqueType, } = query;
            const user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const applyFiltersToBuilder = (qb) => {
                var _a;
                qb.where('gstrAdditional.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                    .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
                    .andWhere(new typeorm_1.Brackets((subQb) => {
                    subQb
                        .where('gstrCredentials.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE })
                        .orWhere('gstrCredentials.status IS NULL');
                }))
                    .andWhere('gstrAdditional.caseFolderTypeName NOT IN (:...typeName)', {
                    typeName: ['REPLIES', 'APPLICATIONS'],
                });
                if (search) {
                    qb.andWhere(new typeorm_1.Brackets((subQb) => {
                        subQb.where('gstrAdditional.gstIn LIKE :gstsearch', {
                            gstsearch: `%${search}%`,
                        });
                        subQb.orWhere('client.displayName LIKE :namesearch', {
                            namesearch: `%${search}%`,
                        });
                    }));
                }
                if (ViewAssigned && !ViewAll) {
                    qb.andWhere('clientManagers.id = :userId', { userId });
                }
                if (financialYear) {
                    if (financialYear === 'NA') {
                        qb.andWhere('gstrAdditional.fy is null');
                    }
                    else {
                        qb.andWhere('gstrAdditional.fy = :financialYr', {
                            financialYr: financialYear,
                        });
                    }
                }
                if (folderType) {
                    qb.andWhere('gstrAdditional.caseFolderTypeName = :folderType', {
                        folderType: folderType,
                    });
                }
                if (type) {
                    qb.andWhere('gstrAdditional.caseTypeName like :searchType', {
                        searchType: type,
                    });
                }
                if (interval) {
                    const now = new Date();
                    if (interval === 'today') {
                        const today = moment(new Date()).format('YYYY-MM-DD');
                        qb.andWhere('STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") = :today', { today });
                    }
                    else if (interval === 'last15days') {
                        const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000)
                            .toISOString()
                            .split('T')[0];
                        qb.andWhere('STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") BETWEEN :last15days AND CURDATE()', { last15days });
                    }
                    else if (interval === 'last1month') {
                        const last1month = new Date(now.setMonth(now.getMonth() - 1)).toISOString().split('T')[0];
                        qb.andWhere('STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN  :last1month AND CURDATE() ', { last1month });
                    }
                    else if (interval === 'last1week') {
                        const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
                            .toISOString()
                            .split('T')[0];
                        qb.andWhere('STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN :last1week AND CURDATE()', { last1week });
                    }
                }
                if (dueInterval) {
                    const now = new Date();
                    const dueI = dueInterval;
                    if (dueI === 'today') {
                        const today = moment(new Date()).format('YYYY-MM-DD');
                        qb.andWhere('STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") = :today', { today });
                    }
                    else if (dueI === 'next15days') {
                        const next15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
                        qb.andWhere('STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next15days', { next15days });
                    }
                    else if (dueI === 'next1month') {
                        const next1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
                        qb.andWhere('STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1month', { next1month });
                    }
                    else if (dueI === 'next1week') {
                        const next1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
                        qb.andWhere('STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1week', { next1week });
                    }
                }
                if (responseType) {
                    qb.andWhere('gstrAdditional.refStatus = :responseType', {
                        responseType,
                    });
                }
                if (caseType) {
                    qb.andWhere('gstrAdditional.caseStatus = :caseType', {
                        caseType,
                    });
                }
                return qb;
            };
            const additionalRecords = (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditional')
                .leftJoinAndSelect('gstrAdditional.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
                .select([
                'gstrAdditional.id',
                'client.displayName',
                'gstrAdditional.gstIn',
                'gstrAdditional.name',
                'gstrAdditional.caseTypeName',
                'gstrAdditional.refId',
                'gstrAdditional.gstrCredentialsId',
                'gstrAdditional.fy',
                'gstrAdditional.caseFolderTypeName',
                'gstrAdditional.categoryDate',
                'gstrAdditional.dueDate',
                'gstrAdditional.refNum',
                'gstrAdditional.categoryType',
                'gstrAdditional.caseStatus',
                'gstrAdditional.refStatus',
                'gstrAdditional.description',
                'gstrAdditional.createdType',
                'clientManagers.id',
            ]);
            applyFiltersToBuilder(additionalRecords);
            additionalRecords.addSelect(`COALESCE(
          STR_TO_DATE(NULLIF(gstrAdditional.categoryDate, 'NA'), '%d/%m/%Y'),
          '0000-01-01'
        )`, 'issueDateOrder');
            additionalRecords.addOrderBy('issueDateOrder', 'DESC');
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    name: 'client.displayName',
                    fy: 'gstrAdditional.fy',
                    casetype: 'gstrAdditional.caseTypeName',
                    folder: 'gstrAdditional.caseFolderTypeName',
                    type: 'gstrAdditional.categoryType',
                    categoryDate: 'gstrAdditional.categoryDate',
                    dueDate: 'gstrAdditional.dueDate',
                    createdType: 'gstrAdditional.createdType',
                };
                const column = columnMap[sort.column] || sort.column;
                additionalRecords.orderBy(column, sort.direction.toUpperCase());
            }
            if (uniqueType) {
                const subQb = (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditional')
                    .leftJoin('gstrAdditional.client', 'client')
                    .leftJoin('client.clientManagers', 'clientManagers')
                    .leftJoin('client.gstrCredentials', 'gstrCredentials')
                    .select('MAX(gstrAdditional.id)', 'id')
                    .groupBy('gstrAdditional.refNum');
                applyFiltersToBuilder(subQb);
                subQb.andWhere('(gstrAdditional.createdType != :createdType OR gstrAdditional.createdType IS NULL)', { createdType: noticeOrders_entity_1.CreatedType.MANUAL }).andWhere('gstrAdditional.refNum IS NOT NULL');
                const rawIds = await subQb.getRawMany();
                const ids = rawIds.map((r) => r.id).filter(Boolean);
                if (!ids.length) {
                    return { count: 0, result: [] };
                }
                additionalRecords.andWhere('gstrAdditional.id IN (:...uniqueIds)', { uniqueIds: ids });
            }
            if (offset >= 0) {
                additionalRecords.skip(offset);
            }
            if (limit) {
                additionalRecords.take(limit);
            }
            const result = await additionalRecords.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error accure while getting getAddNoticeAndOrders', error);
        }
    }
    async exportAdditionalGstNoticeAndOrders(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let gstNoticeorders = await this.getAddNoticeAndOrders(userId, exportQuery);
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Add. Notice & Orders');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'FY', key: 'fy' },
            { header: 'Folder', key: 'folder' },
            { header: 'Date of Issuance', key: 'issueDate' },
            { header: 'Case Type', key: 'caseType' },
            { header: 'Type', key: 'type' },
            { header: 'Reference ID', key: 'referenceId' },
            { header: 'Due Date', key: 'dueDate' },
            { header: 'Response Status', key: 'responseStatus' },
            { header: 'Case Status', key: 'caseStatus' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        gstNoticeorders.result.forEach((noticeorder) => {
            const { client: { displayName: clientName }, } = noticeorder;
            const rowData = {
                serialNo: serialCounter++,
                fy: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.fy,
                clientName: clientName,
                gstIn: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.gstIn,
                caseType: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseTypeName,
                type: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.categoryType,
                folder: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseFolderTypeName,
                noticeName: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.name,
                referenceId: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.refNum,
                issueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.categoryDate,
                dueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dueDate,
                responseStatus: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.refStatus,
                caseStatus: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseStatus,
            };
            const row = worksheet.addRow(rowData);
            const responseStatusCell = row.getCell('responseStatus');
            if (rowData.responseStatus === 'REPLIED') {
                responseStatusCell.font = {
                    color: { argb: 'FF800080' },
                    bold: true,
                };
                responseStatusCell.value = 'Replied';
            }
            else if (rowData.responseStatus === 'NOT_REPLIED') {
                responseStatusCell.font = {
                    color: { argb: 'FF008080' },
                    bold: true,
                };
                responseStatusCell.value = 'Not Replied';
            }
            else if (rowData.responseStatus === 'NA') {
                responseStatusCell.font = {
                    color: { argb: 'FF000000' },
                    bold: true,
                };
                responseStatusCell.value = 'NA';
            }
            const caseStatusCell = row.getCell('caseStatus');
            if (rowData.caseStatus === 'OPEN') {
                caseStatusCell.font = {
                    color: { argb: 'FF800000' },
                    bold: true,
                };
                caseStatusCell.value = 'Open';
            }
            else if (rowData.caseStatus === 'CLOSED') {
                caseStatusCell.font = {
                    color: { argb: 'FF008000' },
                    bold: true,
                };
                caseStatusCell.value = 'Closed';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getNoticeAndOrders(userId, query) {
        var _a;
        try {
            const { offset, limit, search, issuedBy, interval, dueInterval, type } = query;
            const user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const noticeAndOrders = (0, typeorm_1.createQueryBuilder)(noticeOrders_entity_1.default, 'gstrNoticeOrders')
                .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
                .where('gstrNoticeOrders.organizationId = :id', { id: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
                .andWhere('gstrCredentials.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE });
            noticeAndOrders.addSelect(`STR_TO_DATE(NULLIF(gstrNoticeOrders.dateOfIssuance, 'NA'), '%d/%m/%Y')`, 'issueDateOrder');
            noticeAndOrders.addOrderBy('issueDateOrder', 'DESC');
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    name: 'client.displayName',
                    type: 'gstrNoticeOrders.type',
                    issuedBy: 'gstrNoticeOrders.issuedBy',
                    amountOfDemand: 'gstrNoticeOrders.amountOfDemand',
                    dateOfIssuance: 'gstrNoticeOrders.dateOfIssuance',
                    dueDate: 'gstrNoticeOrders.dueDate',
                };
                const column = columnMap[sort.column] || sort.column;
                noticeAndOrders.orderBy(column, sort.direction.toUpperCase());
            }
            if (search) {
                noticeAndOrders.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.orWhere('client.displayName LIKE :namesearch', {
                        namesearch: `%${search}%`,
                    });
                    qb.orWhere('gstrNoticeOrders.gstIn LIKE :gstsearch', {
                        gstsearch: `%${search}%`,
                    });
                }));
            }
            if (ViewAssigned && !ViewAll) {
                noticeAndOrders.andWhere('clientManagers.id = :userId', { userId });
            }
            if (type) {
                noticeAndOrders.andWhere('gstrNoticeOrders.type like :search', {
                    search: type,
                });
            }
            if (issuedBy) {
                noticeAndOrders.andWhere('gstrNoticeOrders.issuedBy like :search', {
                    search: `%${issuedBy}%`,
                });
            }
            if (interval) {
                const now = new Date();
                if (interval === 'today') {
                    const today = moment(new Date()).format('YYYY-MM-DD');
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") = :today', { today });
                }
                else if (interval === 'last15days') {
                    const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000);
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last15days', { last15days });
                }
                else if (interval === 'last1month') {
                    const last1month = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1month', { last1month });
                }
                else if (interval === 'last1week') {
                    const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1week', { last1week });
                }
            }
            if (dueInterval) {
                const now = new Date();
                const interval = dueInterval;
                if (interval === 'today') {
                    const today = moment(new Date()).format("YYYY-MM-DD");
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") = :today', { today });
                }
                else if (interval === 'next15days') {
                    const next15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next15days', { next15days });
                }
                else if (interval === 'next1month') {
                    const next1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1month', { next1month });
                }
                else if (interval === 'next1week') {
                    const next1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
                    noticeAndOrders.andWhere('STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1week', { next1week });
                }
            }
            if (offset >= 0) {
                noticeAndOrders.skip(offset);
            }
            if (limit) {
                noticeAndOrders.take(limit);
            }
            let result = await noticeAndOrders.getManyAndCount();
            return {
                count: result[1],
                result: result[0],
            };
        }
        catch (error) {
            console.log('error accure while getting getNoticeAndOrders', error);
        }
    }
    async exportGstNoticeAndOrders(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let gstNoticeorders = await this.getNoticeAndOrders(userId, exportQuery);
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Notice & Orders');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Type', key: 'type' },
            { header: 'Date of Issuance', key: 'issuanceDate' },
            { header: 'Issued by', key: 'issuedBy' },
            { header: 'Notice/Order #', key: 'noticeNum' },
            { header: 'Due Date', key: 'dueDate' },
            { header: 'Amount (₹)', key: 'amount' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        gstNoticeorders.result.forEach((noticeorder) => {
            var _a;
            const amount = (noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.amountOfDemand) === ' ' ? 0 : noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.amountOfDemand;
            const rowData = {
                serialNo: serialCounter++,
                clientName: (_a = noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.client) === null || _a === void 0 ? void 0 : _a.displayName,
                gstIn: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.gstIn,
                issuanceDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dateOfIssuance,
                issuedBy: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.issuedBy,
                type: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.type,
                noticeNum: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.orderNumber,
                issuedDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dateOfIssuance,
                dueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dueDate,
                amount: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.amountOfDemand,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getGstrReport(userId, query) {
        try {
            const { limit, offset, status, remarks } = query;
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            const entityManager = (0, typeorm_1.getRepository)(automation_machines_entity_1.default);
            let sql = await entityManager
                .createQueryBuilder('automationMachines')
                .leftJoinAndSelect('automationMachines.gstrCredentials', 'gstrCredentials')
                .leftJoinAndSelect('gstrCredentials.client', 'client')
                .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
                .andWhere('automationMachines.type = :type', { type: 'GSTR' })
                .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
                .andWhere('gstrCredentials.status != :disStatus', { disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE });
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    clientname: 'client.displayName',
                    userName: 'gstrCredentials.userName',
                    remarks: 'automationMachines.remarks',
                    createdAt: 'automationMachines.createdAt',
                };
                const column = columnMap[sort.column] || sort.column;
                sql.orderBy(column, sort.direction.toUpperCase());
            }
            if (status) {
                sql = sql.andWhere('automationMachines.status = :status', { status });
            }
            if (remarks) {
                sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
            }
            sql = sql
                .andWhere((qb) => {
                const subQuery = qb
                    .subQuery()
                    .select('MAX(innerAutomationMachines.id)', 'maxId')
                    .from(automation_machines_entity_1.default, 'innerAutomationMachines')
                    .leftJoin('innerAutomationMachines.gstrCredentials', 'gstrCredentials')
                    .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
                    .groupBy('gstrCredentials.id')
                    .getQuery();
                return 'automationMachines.id IN ' + subQuery;
            })
                .limit(limit)
                .offset(offset);
            const result = await sql.getManyAndCount();
            return {
                data: result[0],
                count: result[1],
            };
        }
        catch (error) {
            console.log('error accure while getting getGstrReport', error);
        }
    }
    async exportGstClientReport(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        let reports = await this.getGstrReport(userId, newQuery);
        if (!reports.data.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GST Sync Status');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'GSTIN', key: 'gst' },
            { header: 'User Name', key: 'userName' },
            { header: 'Password', key: 'password' },
            { header: 'Last Sync', key: 'lastSync' },
            { header: 'Status', key: 'status' },
            { header: 'Remarks', key: 'remarks' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        reports.data.forEach((report) => {
            var _a, _b, _c, _d, _e, _f;
            const formatDateTime1 = (dateString) => {
                if (!dateString)
                    return '-';
                const date = new Date(dateString);
                return date.toLocaleString();
            };
            const rowData = {
                serialNo: serialCounter++,
                clientName: (_b = (_a = report === null || report === void 0 ? void 0 : report.gstrCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName,
                gst: (_d = (_c = report === null || report === void 0 ? void 0 : report.gstrCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.gstNumber,
                userName: (_e = report === null || report === void 0 ? void 0 : report.gstrCredentials) === null || _e === void 0 ? void 0 : _e.userName,
                password: (_f = report === null || report === void 0 ? void 0 : report.gstrCredentials) === null || _f === void 0 ? void 0 : _f.password,
                lastSync: formatDateTime1(report === null || report === void 0 ? void 0 : report.createdAt),
                status: (0, lodash_1.capitalize)(report === null || report === void 0 ? void 0 : report.status),
                remarks: report === null || report === void 0 ? void 0 : report.remarks,
            };
            const row = worksheet.addRow(rowData);
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getGstrAdditionalNoticeOrders(userId, id, query) {
        var _a;
        const { offset, limit } = query;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const checkGstrCredentials = await gstrCredentials_entity_1.default.findOne({
                where: { id: id, organizationId: user.organization.id },
            });
            if (checkGstrCredentials) {
                const additionalRecords = (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAdditionalNoticeOrders')
                    .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
                    .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
                    orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
                })
                    .andWhere('gstrAdditionalNoticeOrders.caseFolderTypeName NOT IN (:...typeName)', {
                    typeName: ['REPLIES', 'APPLICATIONS'],
                });
                additionalRecords.orderBy(`STR_TO_DATE(gstrAdditionalNoticeOrders.categoryDate, '%d/%m/%Y')`, 'DESC');
                const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
                if (sort === null || sort === void 0 ? void 0 : sort.column) {
                    const columnMap = {
                        fy: 'gstrAdditionalNoticeOrders.fy',
                        categoryDate: 'gstrAdditionalNoticeOrders.categoryDate',
                        dueDate: 'gstrAdditionalNoticeOrders.dueDate',
                        casetype: 'gstrAdditionalNoticeOrders.caseTypeName',
                        folder: 'gstrAdditionalNoticeOrders.caseFolderTypeName',
                        type: 'gstrAdditionalNoticeOrders.categoryType',
                    };
                    const column = columnMap[sort.column] || sort.column;
                    additionalRecords.orderBy(column, sort.direction.toUpperCase());
                }
                if (offset >= 0) {
                    additionalRecords.skip(offset);
                }
                if (limit) {
                    additionalRecords.take(limit);
                }
                let result = await additionalRecords.getManyAndCount();
                return {
                    count: result[1],
                    result: result[0],
                    accessDenied: true,
                };
            }
            else {
                return { accessDenied: false };
            }
        }
        catch (error) {
            console.log('error accure while getting getGstrAdditionalNoticeOrders', error);
        }
    }
    async exportreferenceBasedNotices(userId, query) {
        const exportQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const gstrid = query === null || query === void 0 ? void 0 : query.gstrid;
        let gstNoticeorders = await this.getGstrAdditionalNoticeOrders(userId, gstrid, exportQuery);
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Add.Notice & Orders (Ref ID)');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'FY', key: 'fy' },
            { header: 'Folder', key: 'folder' },
            { header: 'Date of Issuance', key: 'issueDate' },
            { header: 'Case Type', key: 'caseType' },
            { header: 'Type', key: 'type' },
            { header: 'Reference ID', key: 'referenceId' },
            { header: 'Due Date', key: 'dueDate' },
            { header: 'Response Status', key: 'responseStatus' },
            { header: 'Case Status', key: 'caseStatus' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        if (!gstNoticeorders.result.length)
            throw new common_1.BadRequestException('No Data for Export');
        gstNoticeorders.result.forEach((noticeorder) => {
            const rowData = {
                serialNo: serialCounter++,
                fy: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.fy,
                caseType: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseTypeName,
                folder: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseFolderTypeName,
                noticeName: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.description,
                type: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.categoryType,
                referenceId: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.refId,
                issueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.categoryDate,
                dueDate: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.dueDate,
                responseStatus: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.refStatus,
                caseStatus: noticeorder === null || noticeorder === void 0 ? void 0 : noticeorder.caseStatus,
            };
            const row = worksheet.addRow(rowData);
            const responseStatusCell = row.getCell('responseStatus');
            if (rowData.responseStatus === 'REPLIED') {
                responseStatusCell.font = {
                    color: { argb: 'FF800080' },
                    bold: true,
                };
                responseStatusCell.value = 'Replied';
            }
            else if (rowData.responseStatus === 'NOT_REPLIED') {
                responseStatusCell.font = {
                    color: { argb: 'FF008080' },
                    bold: true,
                };
                responseStatusCell.value = 'Not Replied';
            }
            else if (rowData.responseStatus === 'NA') {
                responseStatusCell.font = {
                    color: { argb: 'FF000000' },
                    bold: true,
                };
                responseStatusCell.value = 'NA';
            }
            const caseStatusCell = row.getCell('caseStatus');
            if (rowData.caseStatus === 'OPEN') {
                caseStatusCell.font = {
                    color: { argb: 'FF800000' },
                    bold: true,
                };
                caseStatusCell.value = 'Open';
            }
            else if (rowData.caseStatus === 'CLOSED') {
                caseStatusCell.font = {
                    color: { argb: 'FF008000' },
                    bold: true,
                };
                caseStatusCell.value = 'Closed';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
            });
        });
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 2;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getNoticeAndOrderDueDateEvents(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        let gstrDueDate = (0, typeorm_1.createQueryBuilder)(noticeOrders_entity_1.default, 'gstrNoticeOrders')
            .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('client.gstrCredentials', 'gstrCredentials')
            .where('gstrNoticeOrders.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere('gstrCredentials.status != :disStatus', {
            disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
            const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
            gstrDueDate.andWhere(`(gstrNoticeOrders.dueDate IS NOT NULL AND gstrNoticeOrders.dueDate != 'NA' AND STR_TO_DATE(gstrNoticeOrders.dueDate, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`, { startOfMonth, endOfMonth });
        }
        if (ViewAssigned && !ViewAll) {
            gstrDueDate.andWhere('clientManagers.id = :userId', { userId });
        }
        const result2 = await gstrDueDate.getMany();
        return result2;
    }
    async getNoticeAndOrderIssueDateEvents(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        let gstrIssueDate = (0, typeorm_1.createQueryBuilder)(noticeOrders_entity_1.default, 'gstrNoticeOrders')
            .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('client.gstrCredentials', 'gstrCredentials')
            .where('gstrNoticeOrders.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere('gstrCredentials.status != :disStatus', {
            disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
        });
        if (query) {
            const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
            const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
            gstrIssueDate.andWhere(`(gstrNoticeOrders.dateOfIssuance IS NOT NULL AND gstrNoticeOrders.dateOfIssuance != 'NA' AND STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`, { startOfMonth, endOfMonth });
        }
        if (ViewAssigned && !ViewAll) {
            gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
        }
        const result2 = await gstrIssueDate.getMany();
        return result2;
    }
    async getAdditionalNoticeOrderIssueDateEvents(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        let gstrIssueDate = (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAddNoticeOrders')
            .leftJoinAndSelect('gstrAddNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('client.gstrCredentials', 'gstrCredentials')
            .where('gstrAddNoticeOrders.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
        }));
        if (query) {
            const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
            const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
            gstrIssueDate.andWhere(`(gstrAddNoticeOrders.categoryDate IS NOT NULL 
          AND gstrAddNoticeOrders.categoryDate != 'NA' 
          AND STR_TO_DATE(gstrAddNoticeOrders.categoryDate, '%d/%m/%Y') 
            BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') 
            AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`, { startOfMonth, endOfMonth });
        }
        if (ViewAssigned && !ViewAll) {
            gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
        }
        const result2 = await gstrIssueDate.getMany();
        return result2;
    }
    async getAdditionalNoticeOrderDueDateEvents(userId, query) {
        var _a;
        let user = await user_entity_1.User.findOne(userId, { relations: ['organization', 'role'] });
        let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        let gstrIssueDate = (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'gstrAddNoticeOrders')
            .leftJoinAndSelect('gstrAddNoticeOrders.client', 'client')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('client.gstrCredentials', 'gstrCredentials')
            .where('gstrAddNoticeOrders.organizationId = :organizationId', {
            organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
        })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
        }));
        if (query) {
            const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
            const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
            gstrIssueDate.andWhere(`(gstrAddNoticeOrders.dueDate IS NOT NULL AND gstrAddNoticeOrders.dueDate != 'NA' AND STR_TO_DATE(gstrAddNoticeOrders.dueDate, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`, { startOfMonth, endOfMonth });
        }
        if (ViewAssigned && !ViewAll) {
            gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
        }
        const result2 = await gstrIssueDate.getMany();
        return result2;
    }
    async getGstrUpdates(userId, query) {
        var _a;
        try {
            let user = await user_entity_1.User.findOne({
                where: { id: userId },
                relations: ['organization', 'role'],
            });
            let ViewAll = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
            let ViewAssigned = user.role.permissions.find((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
            let gstrUpdateTracker = (0, typeorm_1.createQueryBuilder)(gstr_update_tracker_entity_1.default, 'gstrUpdateTracker')
                .leftJoinAndSelect('gstrUpdateTracker.client', 'client')
                .leftJoin('client.clientManagers', 'clientManagers')
                .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
                .where('gstrUpdateTracker.organizationId = :organizationId', {
                organizationId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id,
            })
                .andWhere('gstrUpdateTracker.isChange = :isChange', { isChange: true })
                .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
                .andWhere('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            });
            if (ViewAssigned && !ViewAll) {
                gstrUpdateTracker.andWhere('clientManagers.id = :userId', { userId });
            }
            const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
            if (sort === null || sort === void 0 ? void 0 : sort.column) {
                const columnMap = {
                    displayName: 'client.displayName',
                };
                const column = columnMap[sort.column] || sort.column;
                gstrUpdateTracker.orderBy(column, sort.direction.toUpperCase());
            }
            const result = await gstrUpdateTracker.getMany();
            return result;
        }
        catch (error) {
            console.log('error occur while getting  getIncometexUpdates', error);
        }
    }
    async findGstrUpdateItem(userId, id) {
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            let updateTracker = (0, typeorm_1.getConnection)()
                .createQueryBuilder(gstr_update_tracker_entity_1.default, 'gstrUpdateTracker')
                .leftJoinAndSelect('gstrUpdateTracker.client', 'client')
                .leftJoin('client.organization', 'organization')
                .where('gstrUpdateTracker.id = :id', { id: id })
                .andWhere('organization.id = :organization', { organization: user.organization.id })
                .getOne();
            return updateTracker;
        }
        catch (error) {
            console.log('error occur while getting  getUpdatedItem', error);
        }
    }
    async organizationGstrScheduling(userId) {
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
            const organizarionId = user.organization.id;
            let data = JSON.stringify({
                modules: ['P', 'NAO', 'ANO', 'LB', 'OD'],
                orgId: organizarionId,
                type: 'GSTR',
            });
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                    'X-USER-ID': userId,
                    'Content-Type': 'application/json',
                },
                data: data,
            };
            axios_1.default
                .request(config)
                .then((response) => { })
                .catch((error) => {
                console.log(error);
            });
        }
        catch (error) {
            console.log('error occur while organizationScheduling', error);
        }
    }
    async getAllGstrDemands(userId, query) {
        var _a;
        const { limit, offset, fromDate, toDate, search } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        const demandRecords = (0, typeorm_1.createQueryBuilder)(gstrDemands_entity_1.GstrOutstandingDemand, "gstrOutstandingDemand")
            .leftJoinAndSelect("gstrOutstandingDemand.gstrCredentials", 'gstrCredentials')
            .leftJoinAndSelect('gstrCredentials.client', 'client')
            .where('gstrOutstandingDemand.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrOutstandingDemand.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere('gstrOutstandingDemand.isInPortal IS NULL');
        }))
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
        }));
        demandRecords.orderBy('gstrOutstandingDemand.demandDt', 'DESC');
        const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                gstIn: 'gstrOutstandingDemand.gstIn',
                demandDt: 'gstrOutstandingDemand.demandDt',
                igstTot: 'gstrOutstandingDemand.igstTot',
                cgstTot: 'gstrOutstandingDemand.cgstTot',
                sgstTot: 'gstrOutstandingDemand.sgstTot',
                cessTot: 'gstrOutstandingDemand.cessTot',
                totalTot: 'gstrOutstandingDemand.totalTot',
                orderNo: 'gstrOutstandingDemand.orderNo'
            };
            const column = columnMap[sort === null || sort === void 0 ? void 0 : sort.column] || (sort === null || sort === void 0 ? void 0 : sort.column);
            demandRecords.orderBy(column, sort === null || sort === void 0 ? void 0 : sort.direction.toUpperCase());
        }
        if (fromDate || toDate) {
            const fromDateMs = new Date(fromDate).getTime();
            const toDateMs = new Date(toDate).getTime();
            demandRecords.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('gstrOutstandingDemand.demandDt BETWEEN :fromDate AND :toDate', { fromDate: fromDateMs, toDate: toDateMs });
            }));
        }
        if (search) {
            console.log(search);
            demandRecords.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('client.displayName LIKE :clientSearch', { clientSearch: `%${search}%` });
                qb.orWhere('gstrOutstandingDemand.gstIn LIKE :gstInSearch', { gstInSearch: `%${search}%` });
            }));
        }
        if (limit) {
            demandRecords.take(limit);
        }
        if (offset >= 0) {
            demandRecords.skip(offset);
        }
        const result = await demandRecords.getManyAndCount();
        return {
            count: result[1],
            result: result[0]
        };
    }
    async getClientDemands(userId, id) {
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization']
        });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        const clientDemands = (0, typeorm_1.createQueryBuilder)(gstrDemands_entity_1.GstrOutstandingDemand, "gstrOutstandingDemand")
            .leftJoinAndSelect('gstrOutstandingDemand.gstrCredentials', 'gstrCredentials')
            .where('gstrCredentials.id = :gstrCredId', { gstrCredId: id })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrOutstandingDemand.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere('gstrOutstandingDemand.isInPortal IS NULL');
        }));
        const result = await clientDemands.getManyAndCount();
        return {
            result: result[0],
            count: result[1]
        };
    }
    async getAllGstrLedgers(userId, query) {
        var _a;
        const { limit, offset, search, type } = query;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        const ledgerRecords = (0, typeorm_1.createQueryBuilder)(gstrLedgersBalance_entity_1.GstrLedgerBalance, "gstrLedgersBalance")
            .leftJoinAndSelect("gstrLedgersBalance.gstrCredentials", 'gstrCredentials')
            .leftJoinAndSelect('gstrCredentials.client', 'client')
            .where('gstrLedgersBalance.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
            .andWhere('client.status != :status', { status: whatsapp_controller_1.UserStatus.DELETED })
            .andWhere(new typeorm_1.Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
                disStatus: gstrCredentials_entity_1.GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
        }));
        const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                gstIn: 'gstrLedgersBalance.gstIn',
                igst: 'gstrLedgersBalance.igst',
                cgst: 'gstrLedgersBalance.cgst',
                sgst: 'gstrLedgersBalance.sgst',
                cess: 'gstrLedgersBalance.cess',
                total: 'gstrLedgersBalance.total',
            };
            const column = columnMap[sort === null || sort === void 0 ? void 0 : sort.column] || (sort === null || sort === void 0 ? void 0 : sort.column);
            ledgerRecords.orderBy(column, sort === null || sort === void 0 ? void 0 : sort.direction.toUpperCase());
        }
        if (type) {
            ledgerRecords.andWhere('gstrLedgersBalance.ledgerType LIKE :type', { type });
        }
        if (search) {
            ledgerRecords.andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('client.displayName LIKE :clientSearch', { clientSearch: `%${search}%` });
                qb.orWhere('gstrLedgersBalance.gstIn LIKE :gstInSearch', { gstInSearch: `%${search}%` });
            }));
        }
        if (limit) {
            ledgerRecords.take(limit);
        }
        if (offset >= 0) {
            ledgerRecords.skip(offset);
        }
        const result = await ledgerRecords.getManyAndCount();
        return {
            count: result[1],
            result: result[0]
        };
    }
    async getClientLedgers(userId, query, id) {
        const { limit, offset } = query;
        const user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization']
        });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        const clientLedgers = (0, typeorm_1.createQueryBuilder)(gstrLedgersBalance_entity_1.GstrLedgerBalance, "gstrLedgersBalance")
            .leftJoinAndSelect("gstrLedgersBalance.gstrCredentials", 'gstrCredentials')
            .where('gstrCredentials.id = :gstrCredId', { gstrCredId: id });
        const sort = typeof (query === null || query === void 0 ? void 0 : query.sort) === 'string' ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                gstIn: 'gstrLedgersBalance.gstIn',
                igst: 'gstrLedgersBalance.igst',
                cgst: 'gstrLedgersBalance.cgst',
                sgst: 'gstrLedgersBalance.sgst',
                cess: 'gstrLedgersBalance.cess',
                total: 'gstrLedgersBalance.total',
            };
            const column = columnMap[sort === null || sort === void 0 ? void 0 : sort.column] || (sort === null || sort === void 0 ? void 0 : sort.column);
            clientLedgers.orderBy(column, sort === null || sort === void 0 ? void 0 : sort.direction.toUpperCase());
        }
        if (limit) {
            clientLedgers.take(limit);
        }
        if (offset >= 0) {
            clientLedgers.skip(offset);
        }
        const result = await clientLedgers.getManyAndCount();
        return {
            result: result[0],
            count: result[1]
        };
    }
    async exportDemands(userId, query) {
        var _a, _b, _c, _d;
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const { result: demands } = await this.getAllGstrDemands(userId, newQuery);
        if (!demands.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Category', key: 'category' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Demand Date', key: 'demandDt' },
            { header: 'Order Number', key: 'orderNo' },
            { header: 'IGST Total', key: 'igstTot' },
            { header: 'IGST Tax', key: 'igstTx' },
            { header: 'IGST Fee', key: 'igstFee' },
            { header: 'IGST Penalty', key: 'igstPen' },
            { header: 'IGST Interest', key: 'igstIntr' },
            { header: 'IGST Others', key: 'igstOth' },
            { header: 'CGST Total', key: 'cgstTot' },
            { header: 'CGST Tax', key: 'cgstTx' },
            { header: 'CGST Fee', key: 'cgstFee' },
            { header: 'CGST Penalty', key: 'cgstPen' },
            { header: 'CGST Interest', key: 'cgstIntr' },
            { header: 'CGST Others', key: 'cgstOth' },
            { header: 'SGST Total', key: 'sgstTot' },
            { header: 'SGST Tax', key: 'sgstTx' },
            { header: 'SGST Fee', key: 'sgstFee' },
            { header: 'SGST Penalty', key: 'sgstPen' },
            { header: 'SGST Interest', key: 'sgstIntr' },
            { header: 'SGST Others', key: 'sgstOth' },
            { header: 'CESS Total', key: 'cessTot' },
            { header: 'CESS Tax', key: 'cessTx' },
            { header: 'CESS Fee', key: 'cessFee' },
            { header: 'CESS Penalty', key: 'cessPen' },
            { header: 'CESS Interest', key: 'cessIntr' },
            { header: 'CESS Others', key: 'cessOth' },
            { header: 'Grand Total', key: 'totalTot' },
            { header: 'Total Tax', key: 'totalTx' },
            { header: 'Total Fee', key: 'totalFee' },
            { header: 'Total Penalty', key: 'totalPen' },
            { header: 'Total Interest', key: 'totalIntr' },
            { header: 'Total Others', key: 'totalOth' }
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = headers
            .map(h => h.key)
            .filter(key => !['serialNo', 'clientName', 'category', 'gstIn', 'orderNo', 'demandDt'].includes(key));
        for (const demand of demands) {
            const rowData = {
                serialNo: serialCounter++,
                clientName: ((_b = (_a = demand === null || demand === void 0 ? void 0 : demand.gstrCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName) || '-',
                category: categoryLabels[(_d = (_c = demand === null || demand === void 0 ? void 0 : demand.gstrCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.category] || '-',
                gstIn: (demand === null || demand === void 0 ? void 0 : demand.gstIn) || '-',
                demandDt: (demand === null || demand === void 0 ? void 0 : demand.demandDt) ? new Date(Number(demand.demandDt)) : null,
                orderNo: (demand === null || demand === void 0 ? void 0 : demand.orderNo) || '-'
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            const dateColIndex = headers.findIndex(h => h.key === 'demandDt') + 1;
            const dateCell = row.getCell(dateColIndex);
            if (rowData.demandDt instanceof Date && !isNaN(rowData.demandDt)) {
                dateCell.numFmt = 'dd-mmm-yyyy';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        const rightAlignedColumns = [
            'IGST Tax', 'IGST Fee', 'IGST Penalty', 'IGST Interest', 'IGST Others', 'IGST Total',
            'CGST Tax', 'CGST Fee', 'CGST Penalty', 'CGST Interest', 'CGST Others', 'CGST Total',
            'SGST Tax', 'SGST Fee', 'SGST Penalty', 'SGST Interest', 'SGST Others', 'SGST Total',
            'CESS Tax', 'CESS Fee', 'CESS Penalty', 'CESS Interest', 'CESS Others', 'CESS Total',
            'Total Tax', 'Total Fee', 'Total Penalty', 'Total Interest', 'Total Others', 'Grand Total'
        ];
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell, colNumber) => {
                var _a;
                const colHeader = (_a = headers[colNumber - 1]) === null || _a === void 0 ? void 0 : _a.header;
                if (rightAlignedColumns.includes(colHeader)) {
                    cell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true };
                }
                else {
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                }
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportLedgers(userId, query) {
        var _a, _b, _c, _d;
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const { result: ledgers } = await this.getAllGstrLedgers(userId, newQuery);
        if (!ledgers.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client Name', key: 'clientName' },
            { header: 'Category', key: 'category' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Ledger Type', key: 'ledgerType' },
            { header: 'IGST', key: 'igst' },
            { header: 'CGST', key: 'cgst' },
            { header: 'SGST', key: 'sgst' },
            { header: 'CESS', key: 'cess' },
            { header: 'Total', key: 'total' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = headers
            .map(h => h.key)
            .filter(key => !['serialNo', 'clientName', 'category', 'gstIn', 'ledgerType',].includes(key));
        for (const demand of ledgers) {
            const rowData = {
                serialNo: serialCounter++,
                clientName: ((_b = (_a = demand === null || demand === void 0 ? void 0 : demand.gstrCredentials) === null || _a === void 0 ? void 0 : _a.client) === null || _b === void 0 ? void 0 : _b.displayName) || '-',
                category: categoryLabels[(_d = (_c = demand === null || demand === void 0 ? void 0 : demand.gstrCredentials) === null || _c === void 0 ? void 0 : _c.client) === null || _d === void 0 ? void 0 : _d.category] || '-',
                gstIn: (demand === null || demand === void 0 ? void 0 : demand.gstIn) || '-',
                ledgerType: ledgerTypeMap[demand === null || demand === void 0 ? void 0 : demand.ledgerType] || '-'
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber !== 1) {
                row.eachCell((cell) => {
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                });
            }
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientDemand(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const id = query.gstrid;
        const { result: demands } = await this.getClientDemands(userId, id);
        if (!demands.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Demand Date', key: 'demandDt' },
            { header: 'Order Number', key: 'orderNo' },
            { header: 'IGST Tax', key: 'igstTx' },
            { header: 'IGST Fee', key: 'igstFee' },
            { header: 'IGST Penalty', key: 'igstPen' },
            { header: 'IGST Interest', key: 'igstIntr' },
            { header: 'IGST Others', key: 'igstOth' },
            { header: 'IGST Total', key: 'igstTot' },
            { header: 'CGST Tax', key: 'cgstTx' },
            { header: 'CGST Fee', key: 'cgstFee' },
            { header: 'CGST Penalty', key: 'cgstPen' },
            { header: 'CGST Interest', key: 'cgstIntr' },
            { header: 'CGST Others', key: 'cgstOth' },
            { header: 'CGST Total', key: 'cgstTot' },
            { header: 'SGST Tax', key: 'sgstTx' },
            { header: 'SGST Fee', key: 'sgstFee' },
            { header: 'SGST Penalty', key: 'sgstPen' },
            { header: 'SGST Interest', key: 'sgstIntr' },
            { header: 'SGST Others', key: 'sgstOth' },
            { header: 'SGST Total', key: 'sgstTot' },
            { header: 'CESS Tax', key: 'cessTx' },
            { header: 'CESS Fee', key: 'cessFee' },
            { header: 'CESS Penalty', key: 'cessPen' },
            { header: 'CESS Interest', key: 'cessIntr' },
            { header: 'CESS Others', key: 'cessOth' },
            { header: 'CESS Total', key: 'cessTot' },
            { header: 'Total Tax', key: 'totalTx' },
            { header: 'Total Fee', key: 'totalFee' },
            { header: 'Total Penalty', key: 'totalPen' },
            { header: 'Total Interest', key: 'totalIntr' },
            { header: 'Total Others', key: 'totalOth' },
            { header: 'Grand Total', key: 'totalTot' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = headers
            .map(h => h.key)
            .filter(key => !['serialNo', 'gstIn', 'orderNo', 'demandDt'].includes(key));
        for (const demand of demands) {
            const rowData = {
                serialNo: serialCounter++,
                gstIn: (demand === null || demand === void 0 ? void 0 : demand.gstIn) || '-',
                demandDt: (demand === null || demand === void 0 ? void 0 : demand.demandDt) ? new Date(Number(demand.demandDt)) : null,
                orderNo: (demand === null || demand === void 0 ? void 0 : demand.orderNo) || '-'
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            const dateColIndex = headers.findIndex(h => h.key === 'demandDt') + 1;
            const dateCell = row.getCell(dateColIndex);
            if (rowData.demandDt instanceof Date && !isNaN(rowData.demandDt)) {
                dateCell.numFmt = 'dd-mmm-yyyy';
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        const rightAlignedColumns = [
            'IGST Tax', 'IGST Fee', 'IGST Penalty', 'IGST Interest', 'IGST Others', 'IGST Total',
            'CGST Tax', 'CGST Fee', 'CGST Penalty', 'CGST Interest', 'CGST Others', 'CGST Total',
            'SGST Tax', 'SGST Fee', 'SGST Penalty', 'SGST Interest', 'SGST Others', 'SGST Total',
            'CESS Tax', 'CESS Fee', 'CESS Penalty', 'CESS Interest', 'CESS Others', 'CESS Total',
            'Total Tax', 'Total Fee', 'Total Penalty', 'Total Interest', 'Total Others', 'Grand Total'
        ];
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell, colNumber) => {
                var _a;
                const colHeader = (_a = headers[colNumber - 1]) === null || _a === void 0 ? void 0 : _a.header;
                if (rightAlignedColumns.includes(colHeader)) {
                    console.log("hiii");
                    cell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true };
                }
                else {
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                }
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async exportClientLedger(userId, query) {
        const newQuery = Object.assign(Object.assign({}, query), { offset: 0, limit: 100000000 });
        const id = query.gstrid;
        const { result: ledgers } = await this.getClientLedgers(userId, newQuery, id);
        if (!ledgers.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'GSTIN', key: 'gstIn' },
            { header: 'Ledger Type', key: 'ledgerType' },
            { header: 'IGST', key: 'igst' },
            { header: 'CGST', key: 'cgst' },
            { header: 'SGST', key: 'sgst' },
            { header: 'CESS', key: 'cess' },
            { header: 'Total', key: 'total' },
        ];
        worksheet.columns = headers;
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
        const numberFields = headers
            .map(h => h.key)
            .filter(key => !['serialNo', 'gstIn', 'ledgerType',].includes(key));
        for (const demand of ledgers) {
            const rowData = {
                serialNo: serialCounter++,
                gstIn: (demand === null || demand === void 0 ? void 0 : demand.gstIn) || '-',
                ledgerType: ledgerTypeMap[demand === null || demand === void 0 ? void 0 : demand.ledgerType] || '-'
            };
            for (const key of numberFields) {
                const val = demand === null || demand === void 0 ? void 0 : demand[key];
                rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
            }
            const row = worksheet.addRow(rowData);
            for (const key of numberFields) {
                const colIndex = headers.findIndex(h => h.key === key) + 1;
                const cell = row.getCell(colIndex);
                if (rowData[key] !== null && rowData[key] !== undefined) {
                    cell.numFmt = '₹#,##,##0.00';
                }
            }
            worksheet.columns.forEach((column, colIndex) => {
                var _a, _b;
                const headerLength = ((_a = column.header) === null || _a === void 0 ? void 0 : _a.toString().length) || 0;
                const cellLength = ((_b = rowData[column.key]) === null || _b === void 0 ? void 0 : _b.toString().length) || 0;
                columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex], headerLength, cellLength);
            });
        }
        worksheet.columns.forEach((column, colIndex) => {
            column.width = columnMaxLengths[colIndex] + 3;
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' }
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
        });
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber !== 1) {
                row.eachCell((cell) => {
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                });
            }
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getCaseTypeNames(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId } });
            const uniqueCaseTypeName = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'n')
                .select('DISTINCT n.caseTypeName', 'caseTypeName')
                .leftJoin('n.client', 'c')
                .where('n.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('c.status != :clientStatus', { clientStatus: whatsapp_controller_1.UserStatus.DELETED })
                .getRawMany();
            const filteredCaseTypes = uniqueCaseTypeName
                .map((section) => section.caseTypeName)
                .filter((section) => section !== '' && section !== null);
            return filteredCaseTypes;
        }
        catch (error) {
            console.log('Error in fetching Case Type Names', error);
        }
    }
    async getCaseFolderTypeNames(userId) {
        var _a;
        try {
            const user = await user_entity_1.User.findOne({ where: { id: userId } });
            const uniqueCaseFolderTypeName = await (0, typeorm_1.createQueryBuilder)(gstrAdditionalOrdersAndNotices_entity_1.default, 'n')
                .select('DISTINCT n.caseFolderTypeName', 'caseFolderTypeName')
                .leftJoin('n.client', 'c')
                .leftJoin('c.gstrCredentials', 'gc')
                .where('n.organizationId = :orgId', { orgId: (_a = user === null || user === void 0 ? void 0 : user.organization) === null || _a === void 0 ? void 0 : _a.id })
                .andWhere('gc.status != :clientStatus', { clientStatus: gstrCredentials_entity_1.GstrStatus.DISABLE })
                .andWhere('n.caseFolderTypeName NOT IN (:...folderTypes)', { folderTypes: ['REPLIES', 'APPLICATIONS'] })
                .getRawMany();
            const filteredCaseFolderTypes = uniqueCaseFolderTypeName
                .map((section) => section.caseFolderTypeName)
                .filter((section) => section !== '' && section !== null);
            return filteredCaseFolderTypes;
        }
        catch (e) {
            console.log('Error in fethinf case folder type names', e);
        }
    }
};
GstrService = __decorate([
    (0, common_1.Injectable)()
], GstrService);
exports.GstrService = GstrService;
//# sourceMappingURL=notices.service.js.map