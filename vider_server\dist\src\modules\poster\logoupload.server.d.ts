/// <reference types="node" />
/// <reference types="node" />
import { S3 } from 'aws-sdk';
export declare class AwsService {
    upload(buffer: Buffer, key: string, contentType?: string): Promise<unknown>;
    get(key: string): Promise<unknown>;
    getFileFromS3(bucket: any, key: any): Promise<unknown>;
    uploadS3(file: Buffer, bucket: any, key: any, contentType: any): Promise<unknown>;
    deleteFile(key: string): Promise<unknown>;
    getS3(): S3;
}
