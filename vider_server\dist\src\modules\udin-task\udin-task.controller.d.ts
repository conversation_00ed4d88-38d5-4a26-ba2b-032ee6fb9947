import { UdinTaskService } from './udin-task.service';
export declare class UdinTaskController {
    private readonly service;
    constructor(service: UdinTaskService);
    getUdinTasks(req: any, query: any): Promise<[import("./udin-task.entity").default[], number]>;
    exportUdinTasksPageReport(req: any, body: any): Promise<import("exceljs").Buffer>;
    getUdinTask(req: any, query: any, taskId: number): Promise<import("./udin-task.entity").default>;
    updateUdinTaskDetails(body: any, req: any): Promise<import("./udin-task.entity").default>;
    createUdinTask(body: any, req: any): Promise<import("./udin-task.entity").default>;
    updateUdinTask(body: any, req: any, id: number): Promise<import("./udin-task.entity").default>;
}
