"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEspoMail = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
const moment = require("moment");
function sendEspoMail(data) {
    try {
        (0, axios_1.default)({
            url: 'http://vidersupport.com/espo/crm/api/v1/AtomEmails',
            method: 'POST',
            headers: {
                'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
            },
            data: {
                assignedUserId: '1',
                assignedUserName: 'Admin',
                atomID: 'atom11',
                attachments: data.attachments || '',
                bccid: data.bccid || '',
                body: data.body || '',
                cCid: data.cCid || '',
                description: data.description || '',
                emailType: data.emailType || '',
                fromaddress: data.fromAddress || '',
                name: data.name || 'default',
                preferredsource: data.preferredsource || '',
                signature: data.signature || '',
                subject: data.subject || '',
                templateid: data.templateid || '',
                toAddress: data.toAddress || '',
                clientName: data.clientName || '',
                orgName: data.orgName || '',
                payload: data.payload || '',
                status: data.status || 'Active',
                link: data.link,
            },
        }).then((res) => res);
    }
    catch (error) {
        console.log(error);
        throw new common_1.InternalServerErrorException(error);
    }
}
exports.sendEspoMail = sendEspoMail;
async function organizationSignup(data) {
    const newRecordPayload = [];
    try {
        (0, axios_1.default)({
            url: 'http://vidersupport.com/espo/crm/api/v1/EmailTemplate',
            method: 'GET',
            headers: {
                'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
            }
        }).then((templates) => {
            var _a, _b, _c;
            if (((_a = templates === null || templates === void 0 ? void 0 : templates.data) === null || _a === void 0 ? void 0 : _a.list) && ((_b = templates === null || templates === void 0 ? void 0 : templates.data) === null || _b === void 0 ? void 0 : _b.list.length) > 0) {
                (_c = templates === null || templates === void 0 ? void 0 : templates.data) === null || _c === void 0 ? void 0 : _c.list.forEach((template) => {
                    if ((template === null || template === void 0 ? void 0 : template.name) && (template === null || template === void 0 ? void 0 : template.name) == 'organizationSignUp') {
                        newRecordPayload.push({ template: template === null || template === void 0 ? void 0 : template.name, client: true, user: true });
                    }
                    else {
                        newRecordPayload.push({ template: template === null || template === void 0 ? void 0 : template.name, client: false, user: false });
                    }
                });
                (0, axios_1.default)({
                    url: 'http://vidersupport.com/espo/crm/api/v1/EmailPrefernces',
                    method: 'POST',
                    headers: {
                        'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
                    },
                    data: {
                        assignedUserId: "1",
                        assignedUserName: "Admin",
                        name: data.orgName,
                        orgName: data.orgName,
                        orgnizationId: data.orgId,
                        orgEmail: data.email,
                        payload: JSON.stringify(newRecordPayload),
                        teamsIds: [],
                        teamsNames: {},
                        template: ""
                    },
                }).then((res) => {
                    sendEspoMail({
                        name: 'organizationSignUp-TEST',
                        templateid: 'organizationSignUp',
                        fromAddress: '<EMAIL>',
                        toAddress: data === null || data === void 0 ? void 0 : data.email,
                        subject: 'Organization Signup',
                        body: 'Organization Signup',
                        preferredsource: 'Atom',
                        payload: JSON.stringify({
                            viderLeadName: `${data === null || data === void 0 ? void 0 : data.orgName}`,
                            viderOrganizationId: `${data === null || data === void 0 ? void 0 : data.orgId}`,
                        }),
                    });
                });
            }
        });
    }
    catch (error) {
        console.log(error);
        throw new common_1.InternalServerErrorException(error);
    }
}
async function leadCreated(lead) {
    var _a, _b;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: lead === null || lead === void 0 ? void 0 : lead.email,
        subject: 'Lead Created',
        body: 'Lead Created',
        preferredsource: 'Atom',
        templateid: 'leadCreated',
        name: 'Lead-Created-TEST1',
        clientName: 'leadClientName',
        payload: JSON.stringify({
            viderLeadName: `${lead === null || lead === void 0 ? void 0 : lead.name}`,
            viderOrganizationName: `${(_a = lead === null || lead === void 0 ? void 0 : lead.organization) === null || _a === void 0 ? void 0 : _a.legalName}`,
            viderOrganizationId: `${(_b = lead === null || lead === void 0 ? void 0 : lead.organization) === null || _b === void 0 ? void 0 : _b.id}`
        }),
    });
}
async function taskCreated(taskCreationData) {
    var _a, _b;
    const flatten = (nums) => {
        return nums.reduce((acc, ele) => {
            if (Array.isArray(ele)) {
                acc = Object.assign(Object.assign({}, acc), { num: [...flatten(ele)] });
            }
            else {
                acc = Object.assign(Object.assign({}, acc), { num: [...acc.num, ele] });
            }
            return acc;
        }, { num: [] });
    };
    let { num } = flatten((_a = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _a === void 0 ? void 0 : _a.members);
    let memberNames = num.map((member) => member.fullName).toString();
    (_b = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _b === void 0 ? void 0 : _b.clients.forEach((client) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        sendEspoMail({
            fromAddress: '<EMAIL>',
            toAddress: client === null || client === void 0 ? void 0 : client.email,
            subject: 'Task Created',
            body: 'Task-Created',
            preferredsource: 'Atom',
            templateid: 'taskCreatedForAClient',
            name: 'Task_Created_TEST',
            payload: JSON.stringify({
                viderClientName: client.displayName,
                viderTaskName: ((_b = (_a = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.name)
                    ? `${(_d = (_c = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _c === void 0 ? void 0 : _c.data) === null || _d === void 0 ? void 0 : _d.name}`
                    : `${(_f = (_e = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _e === void 0 ? void 0 : _e.service) === null || _f === void 0 ? void 0 : _f.name}`,
                viderOrgnizationName: `${(_h = (_g = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.user) === null || _g === void 0 ? void 0 : _g.organization) === null || _h === void 0 ? void 0 : _h.legalName}`,
                viderOrganizationId: `${(_k = (_j = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.user) === null || _j === void 0 ? void 0 : _j.organization) === null || _k === void 0 ? void 0 : _k.id}`,
                viderTaskId: ((_l = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.user) === null || _l === void 0 ? void 0 : _l.fullName.slice(0, 2)) + '000' + Math.floor(Math.random() * 10) + 1,
                viderStartDate: moment((_o = (_m = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _m === void 0 ? void 0 : _m.data) === null || _o === void 0 ? void 0 : _o.startDate).format('DD-MM-YYYY'),
                viderDueDate: moment((_q = (_p = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _p === void 0 ? void 0 : _p.data) === null || _q === void 0 ? void 0 : _q.expectedCompletionDate).format('DD-MM-YYYY'),
                viderAssigneNames: memberNames,
            }),
        });
        num.forEach((member) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
            sendEspoMail({
                fromAddress: '<EMAIL>',
                toAddress: member === null || member === void 0 ? void 0 : member.email,
                subject: 'Task Created',
                body: 'task created to you',
                preferredsource: 'Atom',
                templateid: 'taskCreatedForMember',
                name: 'taskCreatedForMembers_TEST',
                payload: JSON.stringify({
                    viderMemberName: `${member === null || member === void 0 ? void 0 : member.fullName}`,
                    viderOrgnizationName: `${(_b = (_a = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.user) === null || _a === void 0 ? void 0 : _a.organization) === null || _b === void 0 ? void 0 : _b.legalName}`,
                    viderOrganizationId: '',
                    viderTaskName: ((_d = (_c = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _c === void 0 ? void 0 : _c.data) === null || _d === void 0 ? void 0 : _d.name)
                        ? `${(_f = (_e = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _e === void 0 ? void 0 : _e.data) === null || _f === void 0 ? void 0 : _f.name}`
                        : `${(_h = (_g = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _g === void 0 ? void 0 : _g.service) === null || _h === void 0 ? void 0 : _h.name}`,
                    viderClientName: client.displayName,
                    viderTaskId: ((_j = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.user) === null || _j === void 0 ? void 0 : _j.fullName.slice(0, 2)) +
                        '000' +
                        Math.floor(Math.random() * 10) +
                        1,
                    viderStartDate: moment((_l = (_k = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _k === void 0 ? void 0 : _k.data) === null || _l === void 0 ? void 0 : _l.startDate).format('DD-MM-YYYY'),
                    viderDueDate: moment((_o = (_m = taskCreationData === null || taskCreationData === void 0 ? void 0 : taskCreationData.event) === null || _m === void 0 ? void 0 : _m.data) === null || _o === void 0 ? void 0 : _o.expectedCompletionDate).format('DD-MM-YYYY'),
                    viderAssineesNames: memberNames,
                }),
            });
        });
    });
}
async function subTaskCreated(subTaskCreationData) {
    var _a, _b, _c;
    let { task } = subTaskCreationData;
    task.members.map((member) => {
        var _a, _b, _c, _d, _e;
        sendEspoMail({
            fromAddress: '<EMAIL>',
            toAddress: member === null || member === void 0 ? void 0 : member.email,
            subject: 'Sub-Task Created',
            body: 'Sub-Task-Created',
            preferredsource: 'Atom',
            templateid: 'subTaskCreatedForMember',
            name: 'Sub_Task_Created_TEST',
            payload: JSON.stringify({
                viderMemberName: `${member === null || member === void 0 ? void 0 : member.fullName}`,
                viderOrganizationName: `${(_a = task === null || task === void 0 ? void 0 : task.organization) === null || _a === void 0 ? void 0 : _a.legalName}`,
                viderOrganizationId: `${(_b = task === null || task === void 0 ? void 0 : task.organization) === null || _b === void 0 ? void 0 : _b.id}`,
                viderTaskName: `${(_c = task === null || task === void 0 ? void 0 : task.parentTask) === null || _c === void 0 ? void 0 : _c.name}`,
                viderClientName: `${(_d = task === null || task === void 0 ? void 0 : task.client) === null || _d === void 0 ? void 0 : _d.displayName}`,
                viderTaskID: `${task === null || task === void 0 ? void 0 : task.taskNumber}`,
                viderSubTaskName: `${task === null || task === void 0 ? void 0 : task.name}`,
                viderDueDate: moment(task === null || task === void 0 ? void 0 : task.dueDate).format('DD-MM-YYYY'),
                viderAsignees: (_e = task === null || task === void 0 ? void 0 : task.members) === null || _e === void 0 ? void 0 : _e.map((member) => member.fullName).toString(),
            }),
        });
    });
    let membersNames = (_a = task === null || task === void 0 ? void 0 : task.members) === null || _a === void 0 ? void 0 : _a.map((member) => member.fullName).toString();
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: task.client.email,
        subject: 'Sub-Task Created',
        body: 'Sub-Task-Created',
        preferredsource: 'Atom',
        templateid: 'subTaskCreatedForAClient',
        name: 'Sub_Task_Created_TEST',
        payload: JSON.stringify({
            viderClientName: `${(_b = task === null || task === void 0 ? void 0 : task.client) === null || _b === void 0 ? void 0 : _b.displayName}`,
            viderOrganizationId: '',
            viderTaskName: `${(_c = task === null || task === void 0 ? void 0 : task.parentTask) === null || _c === void 0 ? void 0 : _c.name}`,
            viderTaskID: `${task === null || task === void 0 ? void 0 : task.taskNumber}`,
            viderSubTaskName: `${task === null || task === void 0 ? void 0 : task.name}`,
            viderDueDate: moment(task === null || task === void 0 ? void 0 : task.dueDate).format('DD-MM-YYYY'),
            viderAsignees: membersNames,
        }),
    });
}
async function taskDeleted(taskDeletedData) {
    var _a, _b, _c, _d, _e, _f;
    let { task, user } = taskDeletedData;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: user === null || user === void 0 ? void 0 : user.email,
        subject: 'Task Deleted',
        body: 'Task-Deleted',
        preferredsource: 'Atom',
        templateid: 'taskDeleted',
        name: 'taskDeleted_TEST',
        payload: JSON.stringify({
            viderMemberName: `${(_a = task === null || task === void 0 ? void 0 : task.client) === null || _a === void 0 ? void 0 : _a.displayName}`,
            viderTaskName: `${task === null || task === void 0 ? void 0 : task.name}`,
            viderOrganizationName: `${(_b = user === null || user === void 0 ? void 0 : user.organization) === null || _b === void 0 ? void 0 : _b.legalName}`,
            viderOrganizationId: `${(_c = user === null || user === void 0 ? void 0 : user.organization) === null || _c === void 0 ? void 0 : _c.id}`,
            viderDateOfTaskCreation: moment(task === null || task === void 0 ? void 0 : task.createdAt).format('DD-MM-YYYY'),
            viderDeletionDate: moment(task === null || task === void 0 ? void 0 : task.dueDate).format('DD-MM-YYYY'),
            viderTaskId: `${task === null || task === void 0 ? void 0 : task.taskNumber}`,
            viderCompName: `${(_d = user === null || user === void 0 ? void 0 : user.organization) === null || _d === void 0 ? void 0 : _d.legalName}`,
            viderCompRegStartDate: moment((_e = task === null || task === void 0 ? void 0 : task.receivedDate) === null || _e === void 0 ? void 0 : _e.dateOfFormation).format('DD-MM-YYYY'),
            viderCompRegEndDate: moment((_f = task === null || task === void 0 ? void 0 : task.receivedDate) === null || _f === void 0 ? void 0 : _f.dateOfFormation).format('DD-MM-YYYY'),
            viderAssignedTo: user === null || user === void 0 ? void 0 : user.fullName,
        }),
    });
}
async function taskUpdateStatusDone(taskStatusUpdatedData) {
    var _a, _b, _c, _d, _e, _f, _g;
    let { task, user } = taskStatusUpdatedData;
    const flatten = (nums) => {
        return nums.reduce((acc, ele) => {
            if (Array.isArray(ele)) {
                acc = Object.assign(Object.assign({}, acc), { num: [...flatten(ele)] });
            }
            else {
                acc = Object.assign(Object.assign({}, acc), { num: [...acc.num, ele] });
            }
            return acc;
        }, { num: [] });
    };
    let { num } = flatten((_a = taskStatusUpdatedData === null || taskStatusUpdatedData === void 0 ? void 0 : taskStatusUpdatedData.task) === null || _a === void 0 ? void 0 : _a.members);
    let memberNames = num.map((member) => member.fullName).toString();
    num.forEach((member) => {
        var _a, _b, _c, _d, _e;
        sendEspoMail({
            fromAddress: '<EMAIL>',
            toAddress: member === null || member === void 0 ? void 0 : member.email,
            subject: 'Task Status Moved To Done',
            body: 'Task-Status-Moved-To-Done',
            preferredsource: 'Atom',
            templateid: 'taskStatusUpdatedForUser',
            name: 'taskStatusUpdatedDone-TEST',
            payload: JSON.stringify({
                viderMemberName: `${member === null || member === void 0 ? void 0 : member.fullName}`,
                viderOrganizationName: `${(_b = (_a = task === null || task === void 0 ? void 0 : task.user) === null || _a === void 0 ? void 0 : _a.organization) === null || _b === void 0 ? void 0 : _b.legalName}`,
                viderOrganizationId: `${(_d = (_c = task === null || task === void 0 ? void 0 : task.user) === null || _c === void 0 ? void 0 : _c.organization) === null || _d === void 0 ? void 0 : _d.id}`,
                viderMemberDone: (user === null || user === void 0 ? void 0 : user.fullName) === (member === null || member === void 0 ? void 0 : member.fullName) ? 'You' : `${user === null || user === void 0 ? void 0 : user.fullName}`,
                viderTaskId: `${task === null || task === void 0 ? void 0 : task.taskNumber}`,
                viderTaskName: `${task === null || task === void 0 ? void 0 : task.name}`,
                viderToStatus: `${task === null || task === void 0 ? void 0 : task.status}`,
                viderClientName: `${(_e = task === null || task === void 0 ? void 0 : task.client) === null || _e === void 0 ? void 0 : _e.displayName}`,
                viderAssignees: memberNames,
            }),
        });
    });
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: (_b = task === null || task === void 0 ? void 0 : task.client) === null || _b === void 0 ? void 0 : _b.email,
        subject: 'Task Status Moved To Done',
        body: 'Task-Status-Moved-To-Done',
        preferredsource: 'Atom',
        templateid: 'taskStatusUpdatedForClient',
        name: 'taskStatusUpdatedDone-TEST',
        payload: JSON.stringify({
            viderClientName: `${(_c = task === null || task === void 0 ? void 0 : task.client) === null || _c === void 0 ? void 0 : _c.displayName}`,
            viderOrganizationName: `${(_e = (_d = task === null || task === void 0 ? void 0 : task.user) === null || _d === void 0 ? void 0 : _d.organization) === null || _e === void 0 ? void 0 : _e.legalName}`,
            viderOrganizationId: `${(_g = (_f = task === null || task === void 0 ? void 0 : task.user) === null || _f === void 0 ? void 0 : _f.organization) === null || _g === void 0 ? void 0 : _g.id}`,
            viderMemberName: `${user === null || user === void 0 ? void 0 : user.fullName}`,
            viderTaskId: `${task === null || task === void 0 ? void 0 : task.taskNumber}`,
            viderTaskName: `${task === null || task === void 0 ? void 0 : task.name}`,
            viderToStatus: `${task === null || task === void 0 ? void 0 : task.status}`,
            viderStartDate: moment(task === null || task === void 0 ? void 0 : task.taskStartDate).format('DD-MM-YYYY'),
            viderEndDate: moment(task === null || task === void 0 ? void 0 : task.dueDate).format('DD-MM-YYYY'),
            viderAssignees: memberNames,
        }),
    });
}
async function dscRegisterIssued(dscIssueData) {
    var _a, _b;
    let { body, dscRegister } = dscIssueData;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.email,
        subject: 'DSC-Register-Issued',
        body: 'DSC-Register-Issued',
        preferredsource: 'Atom',
        templateid: 'dscRegisterIssued',
        name: 'dscRegisterIssued-TEST',
        payload: JSON.stringify({
            viderClientName: `${(_a = dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.client) === null || _a === void 0 ? void 0 : _a.displayName}`,
            viderDSCHolderName: `${dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.holderName}`,
            viderIssueDate: moment(dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.issuedDate).format('DD-MM-YYYY'),
            viderExpiryDate: moment(dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.expiryDate).format('DD-MM-YYYY'),
            viderOrganizationName: `${(_b = dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.client) === null || _b === void 0 ? void 0 : _b.legalName}`,
            viderOrganizationId: '',
            viderReceiverName: `${body === null || body === void 0 ? void 0 : body.personName}`,
        }),
    });
}
async function dscRegisterReceived(dscReceivedData) {
    var _a, _b, _c, _d;
    let { body, dscRegister } = dscReceivedData;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.email,
        subject: 'DSC-Register-Received',
        body: 'DSC-Register-Received',
        preferredsource: 'Atom',
        templateid: 'dscRegisterReceived',
        name: 'dscRegisterReceived-TEST',
        payload: JSON.stringify({
            viderClientName: `${(_a = dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.client) === null || _a === void 0 ? void 0 : _a.displayName}`,
            viderDSCHolderName: `${dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.holderName}`,
            viderDateOfReceipt: moment(dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.receivedDate).format('DD-MM-YYYY'),
            viderExpiryDate: moment(dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.expiryDate).format('DD-MM-YYYY'),
            viderOrganizationName: `${(_b = dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.client) === null || _b === void 0 ? void 0 : _b.legalName}`,
            viderOrganizationId: `${(_d = (_c = dscReceivedData === null || dscReceivedData === void 0 ? void 0 : dscReceivedData.user) === null || _c === void 0 ? void 0 : _c.organization) === null || _d === void 0 ? void 0 : _d.id}`,
            viderReceiverName: `${body === null || body === void 0 ? void 0 : body.personName}`,
        }),
    });
}
async function dscRegisterExpire(dscExpireData) {
    let { dscRegister, numberOfDaysLeft } = dscExpireData;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.email,
        subject: 'DSC-Register-Expire',
        body: 'DSC-Register-Expire',
        preferredsource: 'Atom',
        templateid: 'dscRegisterIsExpiring',
        name: 'dscRegisterIsExpiring-TEST',
        payload: JSON.stringify({
            viderClientName: `${dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.holderName}`,
            viderOrganizationId: '',
            ViderDSCHolderName: `${dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.holderName}`,
            viderExpiryDate: moment(dscRegister === null || dscRegister === void 0 ? void 0 : dscRegister.receivedDate).format('DD-MM-YYYY'),
            viderDaysLeft: numberOfDaysLeft,
        }),
    });
}
async function clientCreated(clientCreatedData) {
    var _a, _b, _c, _d, _e, _f;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: ((_a = clientCreatedData === null || clientCreatedData === void 0 ? void 0 : clientCreatedData.data) === null || _a === void 0 ? void 0 : _a.email) + ',' + ((_c = (_b = clientCreatedData === null || clientCreatedData === void 0 ? void 0 : clientCreatedData.data) === null || _b === void 0 ? void 0 : _b.organization) === null || _c === void 0 ? void 0 : _c.email),
        subject: 'Clien Creation',
        body: 'Client-Creation',
        preferredsource: 'Atom',
        templateid: 'clientCreation',
        name: 'clientCreation-TEST',
        payload: JSON.stringify({
            viderClientName: `${(_d = clientCreatedData === null || clientCreatedData === void 0 ? void 0 : clientCreatedData.data) === null || _d === void 0 ? void 0 : _d.displayName}`,
            viderOrganizationName: `${clientCreatedData === null || clientCreatedData === void 0 ? void 0 : clientCreatedData.orgName}`,
            viderOrganizationId: `${(_f = (_e = clientCreatedData === null || clientCreatedData === void 0 ? void 0 : clientCreatedData.data) === null || _e === void 0 ? void 0 : _e.organization) === null || _f === void 0 ? void 0 : _f.id}`,
        }),
    });
}
async function eventCreated(eventCreatedData) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: (_b = (_a = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.email,
        subject: 'Event Created',
        body: 'Event-Created',
        preferredsource: 'Atom',
        templateid: 'eventCreatedAtom',
        name: 'eventCreated-TEST',
        payload: JSON.stringify({
            ViderClientName: `${(_d = (_c = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _c === void 0 ? void 0 : _c.user) === null || _d === void 0 ? void 0 : _d.fullName}`,
            ViderEventName: `${(_f = (_e = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _e === void 0 ? void 0 : _e.eventData) === null || _f === void 0 ? void 0 : _f.title}`,
            viderTaskName: `${(_h = (_g = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _g === void 0 ? void 0 : _g.eventData) === null || _h === void 0 ? void 0 : _h.type}`,
            ViderOrganizationName: `${(_j = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _j === void 0 ? void 0 : _j.orgName}`,
            ViderDate: moment((_l = (_k = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _k === void 0 ? void 0 : _k.eventData) === null || _l === void 0 ? void 0 : _l.date).format('DD-MM-YYYY'),
            ViderStartDate: moment((_o = (_m = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _m === void 0 ? void 0 : _m.eventData) === null || _o === void 0 ? void 0 : _o.startTime).format('LLLL'),
            ViderEndDate: moment((_q = (_p = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _p === void 0 ? void 0 : _p.eventData) === null || _q === void 0 ? void 0 : _q.endTime).format('LLLL'),
            ViderLocation: `${(_s = (_r = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _r === void 0 ? void 0 : _r.eventData) === null || _s === void 0 ? void 0 : _s.location}`,
            viderOrganizationId: ''
        }),
    });
}
async function eventCreatedWithTaskData(eventCreatedData) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: (_b = (_a = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.data) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.email,
        subject: 'Event Created',
        body: 'Event-Created',
        preferredsource: 'Atom',
        templateid: 'eventCreatedWithTaskAtom',
        name: 'eventCreatedWithTask-TEST',
        payload: JSON.stringify({
            ViderClientName: `${(_d = (_c = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.data) === null || _c === void 0 ? void 0 : _c.user) === null || _d === void 0 ? void 0 : _d.fullName}`,
            ViderEventName: `${(_f = (_e = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _e === void 0 ? void 0 : _e.eventData) === null || _f === void 0 ? void 0 : _f.title}`,
            ViderTaskName: `${(_h = (_g = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.data) === null || _g === void 0 ? void 0 : _g.task) === null || _h === void 0 ? void 0 : _h.name}`,
            ViderOrganizationName: `${(_j = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _j === void 0 ? void 0 : _j.orgName}`,
            ViderDate: moment((_l = (_k = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _k === void 0 ? void 0 : _k.eventData) === null || _l === void 0 ? void 0 : _l.date).format('DD-MM-YYYY'),
            ViderStartDate: moment((_o = (_m = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _m === void 0 ? void 0 : _m.eventData) === null || _o === void 0 ? void 0 : _o.startTime).format('LLLL'),
            ViderEndDate: moment((_q = (_p = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _p === void 0 ? void 0 : _p.eventData) === null || _q === void 0 ? void 0 : _q.endTime).format('LLLL'),
            viderOrganizationId: '',
            ViderLocation: `${(_s = (_r = eventCreatedData === null || eventCreatedData === void 0 ? void 0 : eventCreatedData.event) === null || _r === void 0 ? void 0 : _r.eventData) === null || _s === void 0 ? void 0 : _s.location}`,
        }),
    });
}
async function userInvite(userInviteData) {
    var _a, _b, _c, _d, _e, _f, _g;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: (_b = (_a = userInviteData === null || userInviteData === void 0 ? void 0 : userInviteData.tempevent) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.email,
        subject: 'User Invite',
        body: 'User-invite',
        preferredsource: 'Atom',
        templateid: 'userInvited',
        name: 'User_Invite_TEST',
        payload: JSON.stringify({
            viderJoinNowUrl: `${(_c = userInviteData === null || userInviteData === void 0 ? void 0 : userInviteData.tempevent) === null || _c === void 0 ? void 0 : _c.link}`,
            viderUserName: `${(_e = (_d = userInviteData === null || userInviteData === void 0 ? void 0 : userInviteData.tempevent) === null || _d === void 0 ? void 0 : _d.data) === null || _e === void 0 ? void 0 : _e.fullName}`,
            viderOrganizationName: `${(_f = userInviteData === null || userInviteData === void 0 ? void 0 : userInviteData.tempevent) === null || _f === void 0 ? void 0 : _f.orgName}`,
            viderOrganizationId: `${(_g = userInviteData === null || userInviteData === void 0 ? void 0 : userInviteData.tempevent) === null || _g === void 0 ? void 0 : _g.orgId}`,
        }),
    });
}
async function dscRegisterAdd(dscRegisterAddData) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: (_a = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister) === null || _a === void 0 ? void 0 : _a.email,
        subject: 'dscRegistered',
        body: 'dscRegistered',
        preferredsource: 'Atom',
        templateid: 'dscRegisterAdd',
        name: 'dscRegisterAdd_TEST',
        payload: JSON.stringify({
            viderClientName: `${(_b = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.client) === null || _b === void 0 ? void 0 : _b.displayName}`,
            viderOrganizationName: `${(_d = (_c = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister) === null || _c === void 0 ? void 0 : _c.organization) === null || _d === void 0 ? void 0 : _d.legalName}`,
            viderOrganizationId: `${(_f = (_e = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister) === null || _e === void 0 ? void 0 : _e.organization) === null || _f === void 0 ? void 0 : _f.id}`,
            viderDSCHolderName: `${(_g = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister) === null || _g === void 0 ? void 0 : _g.holderName}`,
            viderDateOfReceipt: moment(dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister).format('DD-MM-YYYY'),
            viderExpiryDate: moment((_h = dscRegisterAddData === null || dscRegisterAddData === void 0 ? void 0 : dscRegisterAddData.dscRegister) === null || _h === void 0 ? void 0 : _h.expiryDate).format('DD-MM-YYYY'),
        }),
    });
}
exports.default = {
    leadCreated,
    organizationSignup,
    taskCreated,
    subTaskCreated,
    taskDeleted,
    taskUpdateStatusDone,
    dscRegisterIssued,
    dscRegisterReceived,
    dscRegisterExpire,
    clientCreated,
    eventCreated,
    eventCreatedWithTaskData,
    userInvite,
    dscRegisterAdd,
};
//# sourceMappingURL=sendMail.js.map