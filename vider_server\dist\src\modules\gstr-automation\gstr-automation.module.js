"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GstrAutomationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const gstrCredentials_entity_1 = require("./entity/gstrCredentials.entity");
const gstr_client_service_1 = require("./service/gstr-client.service");
const gstr_client_controller_1 = require("./controllers/gstr-client.controller");
const notices_service_1 = require("./service/notices.service");
const notices_controllers_1 = require("./controllers/notices.controllers");
const noticeOrders_entity_1 = require("./entity/noticeOrders.entity");
const gstrMachines_entity_1 = require("./entity/gstrMachines.entity");
const gstrProfile_entity_1 = require("./entity/gstrProfile.entity");
const dashboard_services_1 = require("./service/dashboard.services");
const dashboard_controller_1 = require("./controllers/dashboard.controller");
const gstrAdditionalOrdersAndNotices_entity_1 = require("./entity/gstrAdditionalOrdersAndNotices.entity");
const gstr_cron_job_service_1 = require("./service/gstr-cron-job-service");
const gstr_update_tracker_entity_1 = require("./entity/gstr_update_tracker.entity");
const config_controller_1 = require("./controllers/config.controller");
const config_services_1 = require("./service/config.services");
const GstrCredentials_subscriber_1 = require("../../event-subscribers/GstrCredentials.subscriber");
const gst_cron_controller_1 = require("./controllers/gst-cron.controller");
const attachments_gst_service_1 = require("./service/attachments-gst.service");
const attachments_gst_controller_1 = require("./controllers/attachments-gst.controller");
const upload_service_1 = require("../storage/upload.service");
const onedrive_storage_service_1 = require("../ondrive-storage/onedrive-storage.service");
const googledrive_storage_service_1 = require("../ondrive-storage/googledrive-storage.service");
const storage_service_1 = require("../storage/storage.service");
const attachments_service_1 = require("../tasks/services/attachments.service");
const bharath_storage_service_1 = require("../storage/bharath-storage.service");
const bharath_upload_service_1 = require("../storage/bharath-upload.service");
const gstrDemands_entity_1 = require("./entity/gstrDemands.entity");
const gstrLedgersBalance_entity_1 = require("./entity/gstrLedgersBalance.entity");
let GstrAutomationModule = class GstrAutomationModule {
};
GstrAutomationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                gstrCredentials_entity_1.default,
                noticeOrders_entity_1.default,
                gstrMachines_entity_1.default,
                gstrProfile_entity_1.default,
                gstrAdditionalOrdersAndNotices_entity_1.default,
                gstr_update_tracker_entity_1.default,
                gstrDemands_entity_1.GstrOutstandingDemand,
                gstrLedgersBalance_entity_1.GstrLedgerBalance
            ]),
        ],
        controllers: [
            gstr_client_controller_1.GstrClientController,
            notices_controllers_1.GstrController,
            dashboard_controller_1.GstrDashboardController,
            config_controller_1.GstrConfigController,
            gst_cron_controller_1.GstCronController,
            attachments_gst_controller_1.AttachmentGstController,
        ],
        providers: [
            gstr_client_service_1.GstrClientService,
            notices_service_1.GstrService,
            dashboard_services_1.GstrDashboardService,
            gstr_cron_job_service_1.GstrCronJobService,
            config_services_1.GstrConfigService,
            GstrCredentials_subscriber_1.GstrCredentialsSubscriber,
            attachments_gst_service_1.AttachmentGstService,
            upload_service_1.AwsService,
            onedrive_storage_service_1.OneDriveStorageService,
            googledrive_storage_service_1.GoogleDriveStorageService,
            storage_service_1.StorageService,
            attachments_service_1.AttachmentsService,
            bharath_storage_service_1.BharathStorageService,
            bharath_upload_service_1.BharathCloudService,
        ],
    })
], GstrAutomationModule);
exports.GstrAutomationModule = GstrAutomationModule;
//# sourceMappingURL=gstr-automation.module.js.map