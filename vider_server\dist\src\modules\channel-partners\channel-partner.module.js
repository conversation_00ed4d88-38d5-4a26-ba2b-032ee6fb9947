"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelPartnerModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const channel_partner_entity_1 = require("./entity/channel-partner.entity");
const coupon_code_entity_1 = require("./entity/coupon-code.entity");
const channel_partner_signup_entity_1 = require("./entity/channel-partner-signup.entity");
const channel_partner_controller_1 = require("./controllers/channel-partner.controller");
const channel_partner_service_1 = require("./services/channel-partner.service");
let ChannelPartnerModule = class ChannelPartnerModule {
};
ChannelPartnerModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([channel_partner_entity_1.ChannelPartner, coupon_code_entity_1.CouponCode, channel_partner_signup_entity_1.ChannelPartnerSignup])],
        controllers: [channel_partner_controller_1.ChannelPartnerController],
        providers: [channel_partner_service_1.ChannelPartnerService],
    })
], ChannelPartnerModule);
exports.ChannelPartnerModule = ChannelPartnerModule;
//# sourceMappingURL=channel-partner.module.js.map