"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TanCronService = void 0;
const common_1 = require("@nestjs/common");
const cron_activity_entity_1 = require("../../cron-activity/cron-activity.entity");
const moment = require("moment");
const organization_entity_1 = require("../../organization/entities/organization.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const tan_communication_inbox_entity_1 = require("../entity/tan-communication-inbox.entity");
const newemails_1 = require("../../../emails/newemails");
const tan_temp_epro_fyi_entity_1 = require("../entity/tan_temp_epro_fyi.entity");
const tan_temp_epro_fya_entity_1 = require("../entity/tan_temp_epro_fya.entity");
const permission_1 = require("../../events/permission");
let TanCronService = class TanCronService {
    async handleTracesComminicationMail() {
        if (process.env.Cron_Running === 'true') {
            console.log("-------- TRACES NOTICE CRON EXECUTION STARTED ---------");
            const cronData = new cron_activity_entity_1.default();
            cronData.cronType = 'Traces NOTICES';
            cronData.cronDate = moment().toDate().toString();
            cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const cornActivityID = await cronData.save();
            const errorList = [];
            try {
                const today = moment().format('DD-MMM-YYYY');
                const previousSeventhDay = moment().subtract(7, 'days').format('DD-MMM-YYYY');
                const totalOrganization = await organization_entity_1.Organization.createQueryBuilder('organization')
                    .leftJoinAndSelect('organization.users', 'user')
                    .where("DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate", { expirydate: moment().format('YYYY-MM-DD') })
                    .andWhere('user.status = :status', { status: 'active' })
                    .andWhere('user.type = :type', { type: user_entity_1.UserType.ORGANIZATION })
                    .getMany();
                try {
                    for (let organization of totalOrganization) {
                        const users = organization === null || organization === void 0 ? void 0 : organization.users;
                        if ((users === null || users === void 0 ? void 0 : users.length) > 0) {
                            for (let user of users) {
                                if (user.status === user_entity_1.UserStatus.DELETED)
                                    return;
                                const userData = await user_entity_1.User.findOne({
                                    where: { id: user.id },
                                    relations: ['organization', 'role'],
                                });
                                const { role } = userData;
                                const ViewAll = role.permissions.some((p) => p.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
                                const ViewAssigned = role.permissions.some((p) => p.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
                                let tracesRecordsArray = [];
                                let fyiRecordsArray = [];
                                let fyaRecordsArray = [];
                                if (ViewAll || ViewAssigned) {
                                    const tracesRecordsQuery = await (0, typeorm_1.createQueryBuilder)(tan_communication_inbox_entity_1.default, 'tanCommunicationInbox')
                                        .leftJoinAndSelect('tanCommunicationInbox.client', 'client')
                                        .where('tanCommunicationInbox.organizationId = :orgId', { orgId: organization === null || organization === void 0 ? void 0 : organization.id })
                                        .andWhere('STR_TO_DATE(tanCommunicationInbox.date, "%d-%b-%Y") BETWEEN STR_TO_DATE(:previousSeventhDay, "%d-%b-%Y") AND STR_TO_DATE(:today, "%d-%b-%Y")', {
                                        previousSeventhDay,
                                        today,
                                    })
                                        .andWhere('client.status != :status', { status: user_entity_1.UserStatus.DELETED });
                                    if (!ViewAll) {
                                        tracesRecordsQuery.andWhere((qb) => {
                                            const subQuery = qb
                                                .subQuery()
                                                .select('1')
                                                .from('client_client_managers_user', 'cm')
                                                .where('cm.client_id = client.id')
                                                .andWhere('cm.user_id = :userId')
                                                .getQuery();
                                            return `EXISTS (${subQuery})`;
                                        }, { userId: userData.id });
                                    }
                                    const tracesRecords = await tracesRecordsQuery.getMany();
                                    if ((tracesRecords === null || tracesRecords === void 0 ? void 0 : tracesRecords.length) > 0) {
                                        tracesRecordsArray = tracesRecords.map((item) => {
                                            var _a;
                                            return ({
                                                orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                                id: item.id,
                                                clientName: (_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                                category: (item === null || item === void 0 ? void 0 : item.commCat) ? item === null || item === void 0 ? void 0 : item.commCat : "--",
                                                referenceId: (item === null || item === void 0 ? void 0 : item.commRefNo) ? item === null || item === void 0 ? void 0 : item.commRefNo : "--",
                                                formType: (item === null || item === void 0 ? void 0 : item.formType) || '--',
                                                quarter: (item === null || item === void 0 ? void 0 : item.qt) || '--',
                                                fy: (item === null || item === void 0 ? void 0 : item.fy) === '0-01' ? '--' : item === null || item === void 0 ? void 0 : item.fy,
                                                issuedOnDate: (item === null || item === void 0 ? void 0 : item.date) ? item === null || item === void 0 ? void 0 : item.date : '--',
                                                type: (item === null || item === void 0 ? void 0 : item.type) ? item === null || item === void 0 ? void 0 : item.type : '--',
                                            });
                                        });
                                    }
                                    const tanToday = moment().format('YYYY-MM-DD');
                                    const tanPreviousSeventhDay = moment().subtract(7, 'days').format('YYYY-MM-DD');
                                    const tanNextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
                                    const fyiRecordsQuery = await (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fyi_entity_1.default, 'tanTempEproFyi')
                                        .where('tanTempEproFyi.organizationId = :orgId', { orgId: organization === null || organization === void 0 ? void 0 : organization.id })
                                        .andWhere(new typeorm_1.Brackets((qb) => {
                                        qb.where('STR_TO_DATE(tanTempEproFyi.noticeSentDate, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday', {
                                            tanPreviousSeventhDay,
                                            tanToday,
                                        }).orWhere('STR_TO_DATE(tanTempEproFyi.dateOfCompliance, "%d-%m-%Y") BETWEEN :tanToday AND :tanNextSeventhDay', {
                                            tanToday,
                                            tanNextSeventhDay,
                                        }).orWhere('STR_TO_DATE(tanTempEproFyi.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday', {
                                            tanPreviousSeventhDay,
                                            tanToday,
                                        });
                                    }))
                                        .leftJoinAndSelect('tanTempEproFyi.client', 'client');
                                    const fyiRecords = await fyiRecordsQuery.getMany();
                                    if ((fyiRecords === null || fyiRecords === void 0 ? void 0 : fyiRecords.length) > 0) {
                                        fyiRecordsArray = fyiRecords.map((item) => {
                                            var _a;
                                            return ({
                                                orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                                id: item.id,
                                                clientName: (_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                                panNumber: item === null || item === void 0 ? void 0 : item.pan,
                                                proceedingName: item === null || item === void 0 ? void 0 : item.proceedingName,
                                                din: (item === null || item === void 0 ? void 0 : item.noticeDin) || '-',
                                                section: (item === null || item === void 0 ? void 0 : item.noticeSection) || '-',
                                                ay: (item === null || item === void 0 ? void 0 : item.ay) === '0-01' ? 'NA' : item === null || item === void 0 ? void 0 : item.ay,
                                                issuedOnDate: (item === null || item === void 0 ? void 0 : item.noticeSentDate) ? item === null || item === void 0 ? void 0 : item.noticeSentDate : '-',
                                                responseDueDate: (item === null || item === void 0 ? void 0 : item.dateOfCompliance) ? item === null || item === void 0 ? void 0 : item.dateOfCompliance : '-',
                                                responseSubmitted: (item === null || item === void 0 ? void 0 : item.dateResponseSubmitted) ? item === null || item === void 0 ? void 0 : item.dateResponseSubmitted : "-"
                                            });
                                        });
                                    }
                                    const fyaRecordsQuery = await (0, typeorm_1.createQueryBuilder)(tan_temp_epro_fya_entity_1.default, 'tanTempEproFya')
                                        .where('tanTempEproFya.organizationId = :orgId', { orgId: organization === null || organization === void 0 ? void 0 : organization.id })
                                        .andWhere(new typeorm_1.Brackets((qb) => {
                                        qb.where('STR_TO_DATE(tanTempEproFya.noticeSentDate, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday', {
                                            tanPreviousSeventhDay,
                                            tanToday,
                                        }).orWhere('STR_TO_DATE(tanTempEproFya.dateOfCompliance, "%d-%m-%Y") BETWEEN :tanToday AND :tanNextSeventhDay', {
                                            tanToday,
                                            tanNextSeventhDay,
                                        }).orWhere('STR_TO_DATE(tanTempEproFya.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday', {
                                            tanPreviousSeventhDay,
                                            tanToday,
                                        });
                                    }))
                                        .leftJoinAndSelect('tanTempEproFya.client', 'client');
                                    const orgId = organization === null || organization === void 0 ? void 0 : organization.id;
                                    const organizations = await organization_entity_1.Organization.findOne({ id: orgId });
                                    const addressParts = [
                                        organizations.buildingNo || '',
                                        organizations.floorNumber || '',
                                        organizations.buildingName || '',
                                        organizations.street || '',
                                        organizations.location || '',
                                        organizations.city || '',
                                        organizations.district || '',
                                        organizations.state || '',
                                    ].filter((part) => part && part.trim() !== '');
                                    const pincode = organizations.pincode && organizations.pincode.trim() !== ''
                                        ? ` - ${organizations.pincode}`
                                        : '';
                                    const address = addressParts.join(', ') + pincode;
                                    const fyaRecords = await fyaRecordsQuery.getMany();
                                    if ((fyaRecords === null || fyaRecords === void 0 ? void 0 : fyaRecords.length) > 0) {
                                        fyaRecordsArray = fyaRecords.map((item) => {
                                            var _a;
                                            return ({
                                                orgId: organization === null || organization === void 0 ? void 0 : organization.id,
                                                id: item.id,
                                                clientName: (_a = item === null || item === void 0 ? void 0 : item.client) === null || _a === void 0 ? void 0 : _a.displayName,
                                                panNumber: item === null || item === void 0 ? void 0 : item.pan,
                                                proceedingName: item === null || item === void 0 ? void 0 : item.proceedingName,
                                                section: (item === null || item === void 0 ? void 0 : item.noticeSection) || '-',
                                                din: (item === null || item === void 0 ? void 0 : item.noticeDin) || '-',
                                                ay: (item === null || item === void 0 ? void 0 : item.ay) === '0-01' ? 'NA' : item === null || item === void 0 ? void 0 : item.ay,
                                                issuedOnDate: (item === null || item === void 0 ? void 0 : item.noticeSentDate) ? item === null || item === void 0 ? void 0 : item.noticeSentDate : '-',
                                                responseDueDate: (item === null || item === void 0 ? void 0 : item.dateOfCompliance) ? item === null || item === void 0 ? void 0 : item.dateOfCompliance : '-',
                                                responseSubmitted: (item === null || item === void 0 ? void 0 : item.dateResponseSubmitted) ? item === null || item === void 0 ? void 0 : item.dateResponseSubmitted : "-"
                                            });
                                        });
                                    }
                                    if ((tracesRecordsArray === null || tracesRecordsArray === void 0 ? void 0 : tracesRecordsArray.length) > 0 || (fyaRecordsArray === null || fyaRecordsArray === void 0 ? void 0 : fyaRecordsArray.length) > 0 || (fyiRecordsArray === null || fyiRecordsArray === void 0 ? void 0 : fyiRecordsArray.length) > 0) {
                                        const mailOptions = {
                                            data: {
                                                fyaRecordsArray,
                                                fyiRecordsArray,
                                                tracesRecordsArray,
                                                userName: user === null || user === void 0 ? void 0 : user.fullName,
                                                userId: user === null || user === void 0 ? void 0 : user.id,
                                                websiteUrl: process.env.WEBSITE_URL,
                                            },
                                            email: user === null || user === void 0 ? void 0 : user.email,
                                            filePath: 'traces-notice',
                                            subject: 'TAN Traces Notices',
                                            key: 'TRACES_NOTICE_MAIL',
                                            id: user === null || user === void 0 ? void 0 : user.id,
                                        };
                                        await (0, newemails_1.sendnewMail)(mailOptions);
                                    }
                                }
                            }
                        }
                    }
                }
                catch (error) {
                    console.log(`Error in getting Income tax Notice records in cron:`, error);
                }
            }
            catch (error) {
                errorList.push(error.message);
                const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                    .where('id = :id', { id: cornActivityID.id })
                    .getOne();
                getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
                getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                await getcornActivityID.save();
                return console.log(error.message);
            }
            const getcornActivityID = await (0, typeorm_1.createQueryBuilder)(cron_activity_entity_1.default, 'cronActivity')
                .where('id = :id', { id: cornActivityID.id })
                .getOne();
            getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
            getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await getcornActivityID.save();
            console.log('TRACES NOTICE CRON EXECUTION COMPLETED!!!!');
            return 'TRACES NOTICE CRON EXECUTION COMPLETED!!!!';
        }
    }
};
TanCronService = __decorate([
    (0, common_1.Injectable)()
], TanCronService);
exports.TanCronService = TanCronService;
//# sourceMappingURL=tan-cron.service.js.map