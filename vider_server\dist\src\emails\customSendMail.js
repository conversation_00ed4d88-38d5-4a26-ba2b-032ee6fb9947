"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.customsendmail = exports.CustomMailService = void 0;
const nodemailer = require("nodemailer");
const common_1 = require("@nestjs/common");
const email_throttle_service_1 = require("../modules/email-throttle/email-throttle.service");
let defaultsmtp = {
    host: 'email-smtp.ap-south-1.amazonaws.com',
    port: 587,
    auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/**************************',
    },
};
let CustomMailService = class CustomMailService {
    constructor(emailThrottleService) {
        this.emailThrottleService = emailThrottleService;
    }
    async customsendmailthrottled({ to, org, data, useThrottling = true }) {
        return new Promise(async (resolve, reject) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
            try {
                const mailOptions = {
                    from: (org === null || org === void 0 ? void 0 : org.smtp) ? ((_a = org === null || org === void 0 ? void 0 : org.smtp) === null || _a === void 0 ? void 0 : _a.name) ? `"${(_b = org === null || org === void 0 ? void 0 : org.smtp) === null || _b === void 0 ? void 0 : _b.name}" <${(_d = (_c = org === null || org === void 0 ? void 0 : org.smtp) === null || _c === void 0 ? void 0 : _c.auth) === null || _d === void 0 ? void 0 : _d.user}>` : (_f = (_e = org === null || org === void 0 ? void 0 : org.smtp) === null || _e === void 0 ? void 0 : _e.auth) === null || _f === void 0 ? void 0 : _f.user : "<EMAIL>",
                    to: to,
                    subject: data.subject,
                    html: data.html,
                    attachments: data.attachments
                };
                if (org === null || org === void 0 ? void 0 : org.smtp) {
                    (_g = org === null || org === void 0 ? void 0 : org.smtp) === null || _g === void 0 ? true : delete _g.name;
                }
                if (((_h = org === null || org === void 0 ? void 0 : org.smtp) === null || _h === void 0 ? void 0 : _h.service) === "outlook" || ((_j = org === null || org === void 0 ? void 0 : org.smtp) === null || _j === void 0 ? void 0 : _j.service) === "yahoo") {
                    (_k = org === null || org === void 0 ? void 0 : org.smtp) === null || _k === void 0 ? true : delete _k.service;
                }
                if (useThrottling) {
                    await this.emailThrottleService.enqueueEmail(mailOptions.to, mailOptions.subject, mailOptions.html, org === null || org === void 0 ? void 0 : org.id, org === null || org === void 0 ? void 0 : org.smtp, mailOptions.attachments);
                    resolve('Email queued successfully');
                }
                else {
                    const smtp = (org === null || org === void 0 ? void 0 : org.smtp) ? org === null || org === void 0 ? void 0 : org.smtp : defaultsmtp;
                    const customtransporter = nodemailer.createTransport(smtp);
                    customtransporter.sendMail(mailOptions, function (error, info) {
                        if (error) {
                            console.log(error.message);
                            reject(error.message);
                        }
                        else {
                            resolve(info.response);
                        }
                    });
                }
            }
            catch (error) {
                reject(error);
            }
        });
    }
};
CustomMailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [email_throttle_service_1.EmailThrottleService])
], CustomMailService);
exports.CustomMailService = CustomMailService;
async function customsendmail({ to, org, data, }) {
    return new Promise(async (resolve, reject) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        try {
            const mailOptions = {
                from: (org === null || org === void 0 ? void 0 : org.smtp) ? ((_a = org === null || org === void 0 ? void 0 : org.smtp) === null || _a === void 0 ? void 0 : _a.name) ? `"${(_b = org === null || org === void 0 ? void 0 : org.smtp) === null || _b === void 0 ? void 0 : _b.name}" <${(_d = (_c = org === null || org === void 0 ? void 0 : org.smtp) === null || _c === void 0 ? void 0 : _c.auth) === null || _d === void 0 ? void 0 : _d.user}>` : (_f = (_e = org === null || org === void 0 ? void 0 : org.smtp) === null || _e === void 0 ? void 0 : _e.auth) === null || _f === void 0 ? void 0 : _f.user : "<EMAIL>",
                to: to,
                subject: data.subject,
                html: data.html,
                attachments: data.attachments
            };
            if (org === null || org === void 0 ? void 0 : org.smtp) {
                (_g = org === null || org === void 0 ? void 0 : org.smtp) === null || _g === void 0 ? true : delete _g.name;
            }
            if (((_h = org === null || org === void 0 ? void 0 : org.smtp) === null || _h === void 0 ? void 0 : _h.service) === "outlook" || ((_j = org === null || org === void 0 ? void 0 : org.smtp) === null || _j === void 0 ? void 0 : _j.service) === "yahoo") {
                (_k = org === null || org === void 0 ? void 0 : org.smtp) === null || _k === void 0 ? true : delete _k.service;
            }
            const smtp = (org === null || org === void 0 ? void 0 : org.smtp) ? org === null || org === void 0 ? void 0 : org.smtp : defaultsmtp;
            const customtransporter = nodemailer.createTransport(smtp);
            customtransporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(error.message);
                    reject(error.message);
                }
                else {
                    resolve(info.response);
                }
            });
        }
        catch (error) {
            reject(error);
        }
    });
}
exports.customsendmail = customsendmail;
//# sourceMappingURL=customSendMail.js.map