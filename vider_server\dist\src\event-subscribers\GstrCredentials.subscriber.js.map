{"version": 3, "file": "GstrCredentials.subscriber.js", "sourceRoot": "", "sources": ["../../../src/event-subscribers/GstrCredentials.subscriber.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAMiB;AAEjB,gIAAkH;AAClH,qGAAwF;AACxF,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B,IAAI,gBAAiC,CAAC;AAE/B,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QACjD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,QAAQ;QACN,OAAO,gCAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAmC;QACpD,gBAAgB,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,cAAc,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAmC;QACnD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;YACxC,MAAM,uBAAuB,GAAG,MAAM,yCAAuB,CAAC,kBAAkB,CAAC,SAAS,CAAC;iBACxF,KAAK,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,CAAC;iBACnE,QAAQ,CAAC,2DAA2D,CAAC;iBACrE,MAAM,EAAE,CAAC;YACZ,IAAI,uBAAuB,EAAE;gBAC3B,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,MAAM,GAAG;oBACX,MAAM,EAAE,KAAK;oBACb,aAAa,EAAE,QAAQ;oBACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,2CAA2C,cAAc,OAAO;oBAC/F,OAAO,EAAE,EAAE;oBACX,IAAI,EAAE,IAAI;iBACX,CAAC;gBAEF,KAAK;qBACF,OAAO,CAAC,MAAM,CAAC;qBACf,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;;oBACjB,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,EAAE;wBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,QAAQ,CAAC,CAAC;wBACtD,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;4BACzB,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC;4BACtC,KAAK,EAAE,cAAc;4BACrB,IAAI,EAAE,MAAM;4BACZ,SAAS,EAAE,QAAQ;yBACpB,CAAC,CAAC;wBAEH,IAAI,OAAO,GAAG;4BACZ,MAAM,EAAE,MAAM;4BACd,aAAa,EAAE,QAAQ;4BACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,yCAAyC;4BACxE,OAAO,EAAE;gCACP,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;gCACjC,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,KAAK;yBACZ,CAAC;wBAEF,KAAK;6BACF,OAAO,CAAC,OAAO,CAAC;6BAChB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;wBACnB,CAAC,CAAC;6BACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;4BACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;wBAC/E,CAAC,CAAC,CAAC;qBACN;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC/E,CAAC,CAAC,CAAC;aACN;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;SAChE;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAmC;QACnD,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;YAChD,IAAI,gBAAgB,CAAC,MAAM,KAAK,MAAM,EAAE;gBACtC,MAAM,uBAAuB,GAAG,MAAM,yCAAuB,CAAC,kBAAkB,CAAC,SAAS,CAAC;qBACxF,KAAK,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,CAAC;qBACnE,QAAQ,CAAC,2DAA2D,CAAC;qBACrE,MAAM,EAAE,CAAC;gBACZ,IAAI,uBAAuB,EAAE;oBAC3B,IAAI,IAAI,GAAG,EAAE,CAAC;oBACd,IAAI,MAAM,GAAG;wBACX,MAAM,EAAE,KAAK;wBACb,aAAa,EAAE,QAAQ;wBACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,2CAA2C,cAAc,OAAO;wBAC/F,OAAO,EAAE,EAAE;wBACX,IAAI,EAAE,IAAI;qBACX,CAAC;oBAEF,KAAK;yBACF,OAAO,CAAC,MAAM,CAAC;yBACf,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;;wBACjB,IAAI,QAAQ,CAAC,IAAI,EAAE;4BACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,QAAQ,CAAC,CAAC;4BACtD,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;gCACzB,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;gCACxC,KAAK,EAAE,cAAc;gCACrB,IAAI,EAAE,MAAM;gCACZ,SAAS,EAAE,QAAQ;6BACpB,CAAC,CAAC;4BAEH,IAAI,OAAO,GAAG;gCACZ,MAAM,EAAE,MAAM;gCACd,aAAa,EAAE,QAAQ;gCACvB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,yCAAyC;gCACxE,OAAO,EAAE;oCACP,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;oCACjC,cAAc,EAAE,kBAAkB;iCACnC;gCACD,IAAI,EAAE,KAAK;6BACZ,CAAC;4BAEF,KAAK;iCACF,OAAO,CAAC,OAAO,CAAC;iCAChB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;4BACnB,CAAC,CAAC;iCACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gCACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;4BAC/E,CAAC,CAAC,CAAC;yBACN;oBACH,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBACf,OAAO,CAAC,KAAK,CACX,+DAA+D,EAC/D,KAAK,CAAC,OAAO,CACd,CAAC;oBACJ,CAAC,CAAC,CAAC;iBACN;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;SAChE;IACH,CAAC;CACF,CAAA;AArIY,yBAAyB;IADrC,IAAA,yBAAe,GAAE;qCAEyB,oBAAU;GADxC,yBAAyB,CAqIrC;AArIY,8DAAyB"}