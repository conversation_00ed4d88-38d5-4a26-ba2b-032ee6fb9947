import UdinTask from './udin-task.entity';
import * as ExcelJS from 'exceljs';
export declare class UdinTaskService {
    getUdinTasks(query: any, userId: any): Promise<[UdinTask[], number]>;
    exportUdinTasksPageReport(userId: number, query: any): Promise<ExcelJS.Buffer>;
    getUdinTask(query: any, userId: any): Promise<UdinTask>;
    update(body: any, userId: number): Promise<UdinTask>;
    createUdinTask(userId: number, body: any): Promise<UdinTask>;
    updateUdinTask(userId: number, id: number, body: any): Promise<UdinTask>;
}
