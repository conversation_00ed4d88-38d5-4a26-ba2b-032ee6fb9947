import { Connection, EntitySubscriberInterface, InsertEvent } from 'typeorm';
import { Organization } from 'src/modules/organization/entities/organization.entity';
export declare class OrganizationSubscriber implements EntitySubscriberInterface<Organization> {
    private readonly connection;
    constructor(connection: Connection);
    listenTo(): typeof Organization;
    beforeInsert(event: InsertEvent<Organization>): Promise<void>;
    afterInsert(event: InsertEvent<Organization>): Promise<void>;
}
