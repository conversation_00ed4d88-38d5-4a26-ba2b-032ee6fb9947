import { GstrDashboardService } from '../service/dashboard.services';
export declare class GstrDashboardController {
    private service;
    constructor(service: GstrDashboardService);
    getNoticeOrdersDateCount(req: any, query: any): Promise<{
        issuanceDate: {
            last1Week: number;
            last15Days: number;
            last1Month: number;
            today: number;
        };
        dueDate: {
            nxt1Week: number;
            nxt15Days: number;
            nxt1Month: number;
            today: number;
        };
    }>;
    getAdditionalNoticeOrdersDateCount(req: any, query: any): Promise<{
        issuanceDate: {
            last1Week: number;
            last15Days: number;
            last1Month: number;
            today: number;
        };
        dueDate: {
            nxt1WeekDue: number;
            nxt15DaysDue: number;
            nxt1MonthDue: number;
            today: number;
        };
    }>;
    getIncometaxConfigStatus(req: any, query: any): Promise<{
        totalLimit: any;
        difference: number;
        presentClients: number;
    }>;
    clientCheck(req: any, query: any): Promise<{
        queryResult: import("../entity/gstrCredentials.entity").default[];
        totalClients: number;
        count: number;
        totalCount: number;
    }>;
    exportGstrInvalid(req: any, body: any): Promise<import("exceljs").Buffer>;
    getAdditionalNoticesStatCount(req: any, query: any): Promise<{
        stats: {
            open: number;
            closed: number;
            replied: number;
            notReplied: number;
        };
    }>;
    outstandingDemandStats(req: any, query: any): Promise<{
        result: import("../entity/gstrDemands.entity").GstrOutstandingDemand[];
    }>;
    getAdditionalNoticeStats(req: any, query: any): Promise<{
        noticesByCaseType: Record<string, {
            openReplied: number;
            openNotReplied: number;
            closedReplied: number;
            closedNotReplied: number;
            totalNotices: number;
            totalOrders: number;
        }>;
        totals: {
            totalNotices: number;
            totalOrders: number;
        };
    }>;
}
