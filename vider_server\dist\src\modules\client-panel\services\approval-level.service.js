"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApprovalLevelService = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../../users/entities/user.entity");
const approval_procedures_entity_1 = require("../../atm-qtm-approval/entities/approval-procedures.entity");
const typeorm_1 = require("typeorm");
const task_entity_1 = require("../../tasks/entity/task.entity");
const axios_1 = require("axios");
const types_1 = require("../../tasks/dto/types");
let ApprovalLevelService = class ApprovalLevelService {
    async find(userId) {
        let user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        let result = await (0, typeorm_1.createQueryBuilder)(approval_procedures_entity_1.default, 'approvalProcedures')
            .leftJoin('approvalProcedures.organization', 'organization')
            .leftJoinAndSelect('approvalProcedures.approval', 'approval')
            .leftJoinAndSelect('approval.approvalLevels', 'approvalLevel')
            .where('organization.id = :orgId', { orgId: user.organization.id })
            .andWhere('approvalProcedures.module=:module', { module: 'task' })
            .getMany();
        return result;
    }
    async findTasks(data) {
        let task = await task_entity_1.default.findOne({ where: { id: data.taskId } });
        if (task.processInstanceId) {
            let result = [];
            result = await this.getApprovals(task.processInstanceId, task.status);
            return { result, taskStatus: task.status };
        }
        return [];
    }
    async getApprovals(id, status) {
        var _a, _b, _c, _d, _e;
        try {
            const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
            let data = {
                method: 'get',
                maxBodyLength: Infinity,
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            const response = await axios_1.default.get(url, data);
            const result = (_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.tasks.filter((task) => task.name !== 'holdTillDocumentIsSubmitted');
            const taskSnapshots = (_c = (_b = response === null || response === void 0 ? void 0 : response.data) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.taskSnapshots;
            const taskSnapshotsMainTasks = taskSnapshots === null || taskSnapshots === void 0 ? void 0 : taskSnapshots.map((snapshot) => snapshot.tasks);
            let resultTasks = [];
            const firstTask = result.filter((task) => {
                return task.name[task.name.length - 1] == 1;
            });
            for (let taskitems of result) {
                const name = taskitems.name;
                let rejectionArray = [];
                if ((_e = (_d = response === null || response === void 0 ? void 0 : response.data) === null || _d === void 0 ? void 0 : _d.data) === null || _e === void 0 ? void 0 : _e.taskSnapshots) {
                    rejectionArray = taskSnapshotsMainTasks === null || taskSnapshotsMainTasks === void 0 ? void 0 : taskSnapshotsMainTasks.map((subArray) => {
                        const task = subArray === null || subArray === void 0 ? void 0 : subArray.find((task) => (task === null || task === void 0 ? void 0 : task.name) === name &&
                            ((task === null || task === void 0 ? void 0 : task.status) === 'DECLINED' ||
                                (task === null || task === void 0 ? void 0 : task.status) === 'AUTO_DECLINED' ||
                                (task === null || task === void 0 ? void 0 : task.status) === 'APPROVED'));
                        if (task) {
                            return {
                                lastUpdatedOn: task.lastUpdatedOn,
                                comments: task.comments,
                                name: task.name,
                                status: task.status,
                            };
                        }
                        return null;
                    }).filter((task) => task !== null);
                }
                if (status == types_1.TaskStatusEnum.UNDER_REVIEW) {
                    rejectionArray.push({
                        lastUpdatedOn: taskitems.lastUpdatedOn,
                        comments: taskitems.comments,
                        name: taskitems.name,
                        status: taskitems.status,
                    });
                }
                if (status !== types_1.TaskStatusEnum.UNDER_REVIEW) {
                    if (firstTask[0].status) {
                        rejectionArray.push({
                            lastUpdatedOn: taskitems.lastUpdatedOn,
                            comments: taskitems.comments,
                            name: taskitems.name,
                            status: taskitems.status,
                        });
                    }
                }
                taskitems.rejectionArray = rejectionArray;
                resultTasks.push(taskitems);
            }
            resultTasks.sort((a, b) => {
                const lastCharA = a.name.charAt(a.name.length - 1);
                const lastCharB = b.name.charAt(b.name.length - 1);
                if (lastCharA < lastCharB) {
                    return -1;
                }
                else if (lastCharA > lastCharB) {
                    return 1;
                }
                else {
                    return 0;
                }
            });
            return resultTasks;
        }
        catch (err) {
            console.error(err);
        }
    }
    async getNextApprovalDetails(id, level) {
        var _a, _b, _c, _d;
        try {
            const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
            let data = {
                method: 'get',
                maxBodyLength: Infinity,
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            const response = await axios_1.default.get(url, data);
            const result = (_b = (_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.tasks) === null || _b === void 0 ? void 0 : _b.filter((task) => task.name === 'holdTillDocumentIsSubmitted');
            if ((_c = result[0]) === null || _c === void 0 ? void 0 : _c.status) {
                const arr = [];
                return arr;
            }
            else {
                const lastDigit = parseInt(level.slice(-1));
                const newLastDigit = (lastDigit + 1) % 10;
                const updatedString = level.slice(0, -1) + newLastDigit;
                const nextLevel = (_d = response === null || response === void 0 ? void 0 : response.data) === null || _d === void 0 ? void 0 : _d.tasks.filter((task) => task.name === updatedString);
                return nextLevel;
            }
        }
        catch (err) {
            console.error(err);
        }
    }
    async updateTaskApprovals(body, userId) {
        var _a;
        const { taskId, approvalHierarchyId, removeApproval } = body;
        let task = await task_entity_1.default.findOne({ where: { id: taskId } });
        let user = await user_entity_1.User.findOne({ where: { id: userId } });
        if (removeApproval) {
            task.approvalProcedures = null;
            task.processInstanceId = null;
            task.approvalStatus = null;
        }
        if (approvalHierarchyId) {
            const approval = await approval_procedures_entity_1.default.findOne({
                where: { id: approvalHierarchyId, organization: user.organization.id },
                relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
                select: ['id'],
            });
            task.approvalProcedures = approval;
            try {
                const data = JSON.stringify({
                    processKey: 'genericApprovalProcess',
                    metaData: {
                        typeOfApproval: 'ATOM_TASK',
                        approvalProcessId: `Level${(_a = approval === null || approval === void 0 ? void 0 : approval.approval) === null || _a === void 0 ? void 0 : _a.approvalLevels.length}ApprovalProcess`,
                    },
                });
                let config = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    data: data,
                };
                await axios_1.default.request(config).then(async (response) => {
                    var _a, _b;
                    const processInstanceId = (_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.processInstanceId;
                    if (processInstanceId)
                        task.approvalStatus = [
                            {
                                status: `Approval Levels (${(_b = approval === null || approval === void 0 ? void 0 : approval.approval) === null || _b === void 0 ? void 0 : _b.approvalLevels.length})`,
                                completed: false,
                            },
                        ];
                    task.processInstanceId = processInstanceId;
                    this.approvalProcess(processInstanceId, approval);
                });
            }
            catch (err) {
                console.log(`Error on creating camunda approval process:${err}`);
            }
        }
        await task.save();
        return 'success';
    }
    async approvalProcess(id, approvalData) {
        var _a;
        try {
            const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
            let data = {
                method: 'get',
                maxBodyLength: Infinity,
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            const response = await axios_1.default.get(url, data);
            const { approval } = approvalData;
            const { approvalLevels } = approval;
            let assignApprovalTasks = [];
            assignApprovalTasks = (_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.tasks.filter((item) => item.name !== 'holdTillDocumentIsSubmitted').map((item) => {
                let levelNumber = parseInt(item.name.slice(-1));
                const foundUser = approvalLevels.find((level) => level.level === levelNumber);
                const { user } = foundUser;
                const { id } = user;
                return `${process.env.CAMUNDA_URL}/vider/quantum/api/task/${item.id}/assign/${id}`;
            });
            const makeApiCall = async (url) => {
                try {
                    let config = {
                        method: 'put',
                        maxBodyLength: Infinity,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    };
                    const response = await axios_1.default.put(url, config);
                    return response.data;
                }
                catch (error) {
                    console.error('Error for', url, ':', error);
                    throw error;
                }
            };
            const apiPromises = assignApprovalTasks.map((endpoint) => makeApiCall(endpoint));
            Promise.all(apiPromises)
                .then((apiResponses) => { })
                .catch((error) => {
                console.error('One or more API calls failed:', error);
            });
        }
        catch (err) {
            console.log(err);
        }
    }
};
ApprovalLevelService = __decorate([
    (0, common_1.Injectable)()
], ApprovalLevelService);
exports.ApprovalLevelService = ApprovalLevelService;
//# sourceMappingURL=approval-level.service.js.map