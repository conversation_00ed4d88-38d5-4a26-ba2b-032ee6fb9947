{"version": 3, "file": "documentInOut.subscriber.js", "sourceRoot": "", "sources": ["../../../src/event-subscribers/documentInOut.subscriber.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQiB;AACjB,qGAAsF;AACtF,iCAAiC;AAG1B,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAQnD,mBAAc,GAAG,EAAE,CAAC;QACpB,iBAAY,GAAG,EAAE,CAAC;QARhB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,QAAQ;QACN,OAAO,gCAAa,CAAC;IACvB,CAAC;IAID,KAAK,CAAC,YAAY,CAAC,KAAiC;QAClD,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7D,MAAM,MAAM,GAAG,iBAAiB,GAAG,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACrI,MAAM,KAAK,GAAG,MAAM,IAAA,4BAAkB,EAAC,gCAAa,EAAE,eAAe,CAAC;aACnE,QAAQ,CAAC,4BAA4B,EAAE,cAAc,CAAC;aACtD,KAAK,CAAC,mCAAmC,EAAE,EAAE,cAAc,EAAE,YAAY,CAAC,EAAE,EAAE,CAAC;aAC/E,QAAQ,CAAC,2CAA2C,EAAE,EAAE,UAAU,EAAE,GAAG,MAAM,GAAG,EAAE,CAAC;aACnF,QAAQ,EAAE,CAAC;QACd,SAAS,aAAa,CAAC,EAAU;YAC/B,IAAI,EAAE,GAAG,KAAK,EAAE;gBACd,OAAO,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aAChD;YACD,OAAO,MAAM,GAAG,EAAE,CAAC;QACrB,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAiC;IACnD,CAAC;CACF,CAAA;AA/BY,uBAAuB;IADnC,IAAA,yBAAe,GAAE;qCAEyB,oBAAU;GADxC,uBAAuB,CA+BnC;AA/BY,0DAAuB"}