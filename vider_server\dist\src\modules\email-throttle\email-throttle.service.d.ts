import { Repository } from 'typeorm';
import { EmailQueue } from './email-queue.entity';
export declare class EmailThrottleService {
    private readonly queueRepo;
    private readonly logger;
    private sending;
    private readonly batchSize;
    private readonly maxRetries;
    private readonly processingDelay;
    constructor(queueRepo: Repository<EmailQueue>);
    enqueueEmail(to: string, subject: string, body: any, organizationId?: number, smtpConfig?: any, attachments?: any[]): Promise<void>;
    handleEmailQueue(): Promise<void>;
    private sendEmail;
    getQueueStatus(): Promise<{
        totalEmails: number;
        pendingEmails: number;
        retryingEmails: number;
        failingEmails: number;
        isProcessing: boolean;
    }>;
    getFailedEmails(): Promise<EmailQueue[]>;
    getConfiguration(): Promise<{
        batchSize: number;
        maxRetries: number;
        processingDelay: number;
        cronExpression: string;
        description: string;
    }>;
}
