"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UdinTaskService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const task_entity_1 = require("../tasks/entity/task.entity");
const user_entity_1 = require("../users/entities/user.entity");
const udin_task_entity_1 = require("./udin-task.entity");
const types_1 = require("../tasks/dto/types");
const moment = require("moment");
const activity_entity_1 = require("../activity/activity.entity");
const actions_1 = require("../../event-listeners/actions");
const ExcelJS = require("exceljs");
const permission_1 = require("../events/permission");
let UdinTaskService = class UdinTaskService {
    async getUdinTasks(query, userId) {
        var _a, _b, _c;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
        const ViewAll = user.role.permissions.some((permission) => permission.slug === permission_1.Permissions.VIEW_ALL_CLIENT_MANAGERS);
        const ViewAssigned = user.role.permissions.some((permission) => permission.slug === permission_1.Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);
        const udinTasks = await (0, typeorm_1.createQueryBuilder)(udin_task_entity_1.default, 'udinTask')
            .leftJoinAndSelect('udinTask.client', 'udinClient')
            .leftJoin('udinClient.clientManagers', 'clientManagers')
            .leftJoinAndSelect('udinTask.clientGroup', 'udinClientGroup')
            .leftJoin('udinClientGroup.clientGroupManagers', 'clientGroupManagers')
            .leftJoinAndSelect('udinTask.task', 'task')
            .leftJoinAndSelect('task.parentTask', 'parentTask')
            .leftJoinAndSelect('task.client', 'client')
            .leftJoin('client.clientManagers', 'taskClientManagers')
            .leftJoinAndSelect('task.clientGroup', 'clientGroup')
            .leftJoin('clientGroup.clientGroupManagers', 'taskClientGroupManagers')
            .leftJoinAndSelect('task.members', 'members')
            .leftJoinAndSelect('udinTask.user', 'udinUser')
            .leftJoinAndSelect('udinTask.organization', 'organization')
            .where(new typeorm_1.Brackets(qb => {
            qb.where('udinTask.task IS NULL')
                .orWhere('udinTask.task IS NOT NULL')
                .orWhere('udinTask.userType = :userType', { userType: user_entity_1.UserType.NON_ORGANIZATION });
        }))
            .andWhere(new typeorm_1.Brackets(qb => {
            qb.where('udinTask.task IS NULL')
                .orWhere(new typeorm_1.Brackets(innerQb => {
                innerQb.where('task.recurringStatus = :recurringStatus', { recurringStatus: types_1.TaskRecurringStatus.CREATED });
            }));
        }))
            .andWhere(new typeorm_1.Brackets(qb => {
            qb.where('task.id IS NULL')
                .orWhere(`task.status NOT IN ('${types_1.TaskStatusEnum.DELETED}', '${types_1.TaskStatusEnum.TERMINATED}')`);
        }))
            .andWhere('udinTask.udinTaskStatus = :udinStatus', { udinStatus: udin_task_entity_1.UdinTaskStatus.ACTIVE })
            .andWhere('organization.id = :orgId', { orgId: user.organization.id })
            .orderBy('task.id', 'DESC')
            .addOrderBy('udinTask.id', 'DESC');
        udinTasks
            .andWhere(new typeorm_1.Brackets((qb) => qb
            .where('task.status IN (:...statuses)', {
            statuses: [
                types_1.TaskStatusEnum.TODO,
                types_1.TaskStatusEnum.IN_PROGRESS,
                types_1.TaskStatusEnum.ON_HOLD,
                types_1.TaskStatusEnum.UNDER_REVIEW,
                types_1.TaskStatusEnum.COMPLETED
            ],
        })
            .andWhere('task.recurringStatus NOT IN (:recStatus)', { recStatus: 'pending' })
            .orWhere('task.id IS NULL')));
        if (!ViewAll && ViewAssigned) {
            udinTasks.andWhere(new typeorm_1.Brackets(qb => {
                qb.where(new typeorm_1.Brackets(innerQb => {
                    innerQb.where('udinClient.id IS NOT NULL')
                        .andWhere('clientManagers.id = :userId', { userId });
                }))
                    .orWhere(new typeorm_1.Brackets(innerQb => {
                    innerQb.where('udinClientGroup.id IS NOT NULL')
                        .andWhere('clientGroupManagers.id = :userId', { userId });
                }))
                    .orWhere(new typeorm_1.Brackets(innerQb => {
                    innerQb.where('client.id IS NOT NULL')
                        .andWhere('taskClientManagers.id = :userId', { userId });
                }))
                    .orWhere(new typeorm_1.Brackets(innerQb => {
                    innerQb.where('clientGroup.id IS NOT NULL')
                        .andWhere('taskClientGroupManagers.id = :userId', { userId });
                }))
                    .orWhere(new typeorm_1.Brackets(innerQb => {
                    innerQb.where('udinClient.id IS NULL')
                        .andWhere('udinClientGroup.id IS NULL')
                        .andWhere('client.id IS NULL')
                        .andWhere('clientGroup.id IS NULL');
                }));
            }));
        }
        else if (!ViewAll && !ViewAssigned) {
            udinTasks.andWhere(new typeorm_1.Brackets(qb => {
                qb.where('udinClient.id IS NULL')
                    .andWhere('udinClientGroup.id IS NULL')
                    .andWhere('client.id IS NULL')
                    .andWhere('clientGroup.id IS NULL');
            }));
        }
        if (query.selectedTaskType === 'task') {
            udinTasks.andWhere('task.parentTask IS NULL');
        }
        if (query.selectedTaskType === 'sub-task') {
            udinTasks.andWhere('task.parentTask IS NOT NULL');
        }
        if ((_a = query === null || query === void 0 ? void 0 : query.type) === null || _a === void 0 ? void 0 : _a.includes('withoutAtom')) {
            udinTasks.andWhere('udinTask.userType = :userType', { userType: user_entity_1.UserType.NON_ORGANIZATION });
        }
        if ((_b = query === null || query === void 0 ? void 0 : query.type) === null || _b === void 0 ? void 0 : _b.includes('withAtom')) {
            udinTasks.andWhere('udinTask.userType = :userType', { userType: user_entity_1.UserType.ORGANIZATION })
                .andWhere('udinTask.task IS NOT NULL')
                .andWhere(new typeorm_1.Brackets(qb => {
                qb.where('task.recurringStatus IS NULL')
                    .orWhere('task.recurringStatus = :recurringStatus', { recurringStatus: types_1.TaskRecurringStatus.CREATED });
            }));
        }
        if ((_c = query === null || query === void 0 ? void 0 : query.assignee) === null || _c === void 0 ? void 0 : _c.length) {
            udinTasks.andWhere('udinUser.id IN (:...assignee)', {
                assignee: query.assignee,
            });
        }
        if (query.search) {
            udinTasks.andWhere(`(
          task.name LIKE :search OR
          task.taskNumber LIKE :search OR
          client.displayName LIKE :search OR
          udinTask.udinNumber LIKE :search OR
          udinTask.name LIKE :search OR
          udinClient.displayName LIKE :search
        )`, { search: `%${query.search}%` });
        }
        if (query.offset) {
            udinTasks.skip(query.offset);
        }
        if (query.limit) {
            udinTasks.take(query.limit);
        }
        const sort = (typeof (query === null || query === void 0 ? void 0 : query.sort) === "string") ? JSON.parse(query.sort) : query === null || query === void 0 ? void 0 : query.sort;
        if (sort === null || sort === void 0 ? void 0 : sort.column) {
            const columnMap = {
                taskNumber: 'task.taskNumber',
                date: 'udinTask.date',
                name: 'task.name',
                client: 'client.displayName',
                fullName: 'user.fullName',
                udinNumber: 'udinTask.udinNumber',
            };
            const column = columnMap[sort.column] || sort.column;
            udinTasks.orderBy(column, sort.direction.toUpperCase());
        }
        else {
            udinTasks.orderBy('udinTask.date', 'DESC');
        }
        let result = await udinTasks.getManyAndCount();
        return result;
    }
    async exportUdinTasksPageReport(userId, query) {
        let udintasks = await this.getUdinTasks(query, userId);
        if (!udintasks || !udintasks.length) {
            throw new common_1.BadRequestException('No Data for Export');
        }
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('UDIN Register');
        const headers = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Client / Client Group', key: 'clientGroup' },
            { header: 'Task ID', key: 'taskId' },
            { header: 'Task Name/Title', key: 'taskName' },
            { header: 'UDIN', key: 'udin' },
            { header: 'Date of Signing', key: 'dateOfSigning' },
            { header: 'Professional Name', key: 'professionalName' },
        ];
        worksheet.columns = headers;
        let serialCounter = 1;
        const formatDate = (dateString) => {
            if (!dateString || isNaN(new Date(dateString).getTime())) {
                return "";
            }
            const date = new Date(dateString);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        };
        const rows = udintasks[0].map((udintask) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
            return {
                serialNo: serialCounter++,
                clientGroup: (udintask === null || udintask === void 0 ? void 0 : udintask.client)
                    ? (_a = udintask === null || udintask === void 0 ? void 0 : udintask.client) === null || _a === void 0 ? void 0 : _a.displayName
                    : (((_b = udintask === null || udintask === void 0 ? void 0 : udintask.clientGroup) === null || _b === void 0 ? void 0 : _b.displayName) ||
                        (((_c = udintask === null || udintask === void 0 ? void 0 : udintask.task) === null || _c === void 0 ? void 0 : _c.client)
                            ? (_e = (_d = udintask === null || udintask === void 0 ? void 0 : udintask.task) === null || _d === void 0 ? void 0 : _d.client) === null || _e === void 0 ? void 0 : _e.displayName
                            : (_g = (_f = udintask === null || udintask === void 0 ? void 0 : udintask.task) === null || _f === void 0 ? void 0 : _f.clientGroup) === null || _g === void 0 ? void 0 : _g.displayName)) || " ",
                taskId: ((_h = udintask === null || udintask === void 0 ? void 0 : udintask.task) === null || _h === void 0 ? void 0 : _h.taskNumber) || " ",
                taskName: ((_j = udintask === null || udintask === void 0 ? void 0 : udintask.task) === null || _j === void 0 ? void 0 : _j.name) || (udintask === null || udintask === void 0 ? void 0 : udintask.name) || " ",
                udin: (udintask === null || udintask === void 0 ? void 0 : udintask.udinNumber) || " ",
                dateOfSigning: formatDate(udintask === null || udintask === void 0 ? void 0 : udintask.date) || " ",
                professionalName: ((_k = udintask === null || udintask === void 0 ? void 0 : udintask.user) === null || _k === void 0 ? void 0 : _k.fullName) || " ",
            };
        });
        rows.forEach(row => worksheet.addRow(row));
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        headers.forEach((_, index) => {
            const cell = headerRow.getCell(index + 1);
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'clientGroup' || column.key === 'taskName') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            }
            else {
                let maxLength = column.header.length;
                column.eachCell({ includeEmpty: true }, (cell) => {
                    const cellValue = cell.value || '';
                    const cellLength = cellValue.toString().length;
                    if (cellLength > maxLength) {
                        maxLength = cellLength;
                    }
                });
                column.width = maxLength + 2;
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });
        worksheet.eachRow((row) => {
            row.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }
    async getUdinTask(query, userId) {
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'] });
        const udinTask = await udin_task_entity_1.default.findOne({
            where: { task: { id: query.taskId }, organization: { id: user.organization.id } },
            relations: ['user']
        });
        return udinTask;
    }
    async update(body, userId) {
        var _a, _b, _c, _d;
        let user = await user_entity_1.User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        const udinTask = await (0, typeorm_1.createQueryBuilder)(udin_task_entity_1.default, 'udinTask')
            .leftJoinAndSelect('udinTask.task', 'task')
            .leftJoinAndSelect('udinTask.user', 'user')
            .leftJoinAndSelect('udinTask.organization', 'organization')
            .where('task.id = :taskId', { taskId: body === null || body === void 0 ? void 0 : body.taskId })
            .andWhere('organization.id = :org', { org: user.organization.id })
            .getOne();
        const oldDate = udinTask === null || udinTask === void 0 ? void 0 : udinTask.date;
        const oldNumber = udinTask === null || udinTask === void 0 ? void 0 : udinTask.udinNumber;
        const oldUser = udinTask === null || udinTask === void 0 ? void 0 : udinTask.user;
        const task = await task_entity_1.default.findOne({ where: { id: body === null || body === void 0 ? void 0 : body.taskId } });
        if ((body === null || body === void 0 ? void 0 : body.isUdin) && (udinTask === null || udinTask === void 0 ? void 0 : udinTask.task)) {
            udinTask.task = task;
            udinTask.date = body === null || body === void 0 ? void 0 : body.udinDate;
            udinTask.udinNumber = body === null || body === void 0 ? void 0 : body.udinNumber;
            udinTask.user = body === null || body === void 0 ? void 0 : body.udinUser;
            await udinTask.save();
            let activity = new activity_entity_1.default();
            activity.action = actions_1.Event_Actions.UDIN_UPDATED;
            activity.actorId = user.id;
            activity.type = activity_entity_1.ActivityType.TASK;
            activity.typeId = udinTask.task ? (_a = udinTask === null || udinTask === void 0 ? void 0 : udinTask.task) === null || _a === void 0 ? void 0 : _a.id : task.id;
            activity.remarks = `UDIN Certificate Details updated from "${(oldUser === null || oldUser === void 0 ? void 0 : oldUser.fullName) || 'NA'} | ${oldNumber || "NA"} | ${moment(oldDate).format("DD-MM-YYYY") || "NA"} " to "${(_b = udinTask === null || udinTask === void 0 ? void 0 : udinTask.user) === null || _b === void 0 ? void 0 : _b.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" by ${user === null || user === void 0 ? void 0 : user.fullName}`;
            await activity.save();
        }
        if ((body === null || body === void 0 ? void 0 : body.isUdin) && !(udinTask === null || udinTask === void 0 ? void 0 : udinTask.task)) {
            let udinTask = new udin_task_entity_1.default();
            udinTask.task = task;
            udinTask.user = body === null || body === void 0 ? void 0 : body.udinUser;
            udinTask.date = body === null || body === void 0 ? void 0 : body.udinDate;
            udinTask.organization = user.organization;
            udinTask.udinNumber = body === null || body === void 0 ? void 0 : body.udinNumber;
            await udinTask.save();
            task.isUdin = true;
            await task.save();
            let activity = new activity_entity_1.default();
            activity.action = actions_1.Event_Actions.UDIN_ADDED;
            activity.actorId = user.id;
            activity.type = activity_entity_1.ActivityType.TASK;
            activity.typeId = udinTask.task ? (_c = udinTask === null || udinTask === void 0 ? void 0 : udinTask.task) === null || _c === void 0 ? void 0 : _c.id : task.id;
            activity.remarks = `UDIN Certificate Details "${udinTask.user.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" added by ${user.fullName}`;
            await activity.save();
        }
        if (!(body === null || body === void 0 ? void 0 : body.isUdin) && (udinTask === null || udinTask === void 0 ? void 0 : udinTask.task)) {
            await udinTask.remove();
            task.isUdin = false;
            await task.save();
            if (udinTask === null || udinTask === void 0 ? void 0 : udinTask.udinNumber) {
                let activity = new activity_entity_1.default();
                activity.action = actions_1.Event_Actions.UDIN_REMOVED;
                activity.actorId = user.id;
                activity.type = activity_entity_1.ActivityType.TASK;
                activity.typeId = udinTask.task ? (_d = udinTask === null || udinTask === void 0 ? void 0 : udinTask.task) === null || _d === void 0 ? void 0 : _d.id : task.id;
                activity.remarks = `UDIN Certificate Details "${udinTask.user.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" removed by ${user === null || user === void 0 ? void 0 : user.fullName}`;
                await activity.save();
            }
            return null;
        }
        if (!(body === null || body === void 0 ? void 0 : body.isUdin) && !(udinTask === null || udinTask === void 0 ? void 0 : udinTask.task)) {
            task.isUdin = false;
            await task.save();
        }
        return udinTask;
    }
    async createUdinTask(userId, body) {
        var _a, _b;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'], });
        let newUdinTask = new udin_task_entity_1.default();
        newUdinTask.date = body === null || body === void 0 ? void 0 : body.udinDate;
        newUdinTask.udinNumber = body === null || body === void 0 ? void 0 : body.udinNumber;
        newUdinTask.organization = user.organization;
        newUdinTask.user = body === null || body === void 0 ? void 0 : body.user;
        newUdinTask.client = ((_a = body === null || body === void 0 ? void 0 : body.client) === null || _a === void 0 ? void 0 : _a.type) === "CLIENT_GROUP" ? null : body === null || body === void 0 ? void 0 : body.client;
        newUdinTask.clientGroup = ((_b = body === null || body === void 0 ? void 0 : body.client) === null || _b === void 0 ? void 0 : _b.type) === "CLIENT_GROUP" ? body === null || body === void 0 ? void 0 : body.client : null;
        newUdinTask.name = body === null || body === void 0 ? void 0 : body.name;
        newUdinTask.userType = udin_task_entity_1.userType.NON_ORGANIZATION;
        newUdinTask.udinTaskStatus = udin_task_entity_1.UdinTaskStatus.ACTIVE;
        await newUdinTask.save();
        return newUdinTask;
    }
    async updateUdinTask(userId, id, body) {
        var _a, _b;
        const user = await user_entity_1.User.findOne({ where: { id: userId }, relations: ['organization'], });
        const udinTask = await udin_task_entity_1.default.findOne({ where: { id } });
        let newUdinTask = udinTask;
        newUdinTask.date = body === null || body === void 0 ? void 0 : body.udinDate;
        newUdinTask.udinNumber = body === null || body === void 0 ? void 0 : body.udinNumber;
        newUdinTask.organization = user.organization;
        newUdinTask.user = body === null || body === void 0 ? void 0 : body.user;
        newUdinTask.client = ((_a = body === null || body === void 0 ? void 0 : body.client) === null || _a === void 0 ? void 0 : _a.type) === "CLIENT_GROUP" ? null : body === null || body === void 0 ? void 0 : body.client;
        newUdinTask.clientGroup = ((_b = body === null || body === void 0 ? void 0 : body.client) === null || _b === void 0 ? void 0 : _b.type) === "CLIENT_GROUP" ? body === null || body === void 0 ? void 0 : body.client : null;
        newUdinTask.name = body === null || body === void 0 ? void 0 : body.name;
        newUdinTask.userType = udin_task_entity_1.userType.NON_ORGANIZATION;
        newUdinTask.udinTaskStatus = udin_task_entity_1.UdinTaskStatus.ACTIVE;
        await newUdinTask.save();
        return newUdinTask;
    }
};
UdinTaskService = __decorate([
    (0, common_1.Injectable)()
], UdinTaskService);
exports.UdinTaskService = UdinTaskService;
//# sourceMappingURL=udin-task.service.js.map