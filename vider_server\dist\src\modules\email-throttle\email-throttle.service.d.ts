import { Repository } from 'typeorm';
import { EmailQueue } from './email-queue.entity';
export declare class EmailThrottleService {
    private readonly queueRepo;
    private readonly logger;
    private sending;
    constructor(queueRepo: Repository<EmailQueue>);
    enqueueEmail(to: string, subject: string, body: string): Promise<void>;
    handleEmailQueue(): Promise<void>;
    private sendEmail;
}
