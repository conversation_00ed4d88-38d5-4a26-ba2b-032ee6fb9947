import TanUpdateTracker from '../entity/tan-update-tracker.entity';
import TanClientCredentials from '../entity/tan-client-credentials.entity';
import * as ExcelJS from 'exceljs';
export declare class TanSyncService {
    createMachine(userId: number, id: any, data: any): Promise<any>;
    sendSingleIncometaxTanAutomationRequestToCamunda(userId: any, data: any): Promise<any>;
    sendIncometaxAutomationRequestToCamunda(userId: number, data: any): Promise<void>;
    bulkAutomationSync(userId: number, data: any): Promise<void>;
    checkAutomationInOrganization(userId: any): Promise<number>;
    getIncometexUpdates(userId: number): Promise<TanUpdateTracker[]>;
    getUpdatedItem(userId: any, id: number): Promise<TanUpdateTracker>;
    disableIncomeTaxClient(userId: number, body: any): Promise<void>;
    disableIncomeTaxSingleClient(id: number, userId: number): Promise<void>;
    enableIncometaxClient(id: number, userId: number): Promise<void>;
    enableBulkIncometaxClient(userId: number, body: any): Promise<void>;
    getDeletedIncomeTaxClients(userId: number, query: any): Promise<{
        count: number;
        result: TanClientCredentials[];
    }>;
    exportIncomeTaxTandeletedClients(userId: number, body: any): Promise<ExcelJS.Buffer>;
    getTanBulkSyncStatus(userId: number): Promise<string>;
    updateTanEnableStatus(userId: number): Promise<void>;
    updateTanDisableStatus(userId: number): Promise<void>;
    organizationTanScheduling(userId: any, body: any): Promise<string>;
    getTraceBulkSyncStatus(userId: number): Promise<string>;
    updateTraceEnableStatus(userId: number): Promise<void>;
    updateTraceDisableStatus(userId: number): Promise<void>;
    organizationTraceScheduling(userId: any, body: any): Promise<string>;
}
