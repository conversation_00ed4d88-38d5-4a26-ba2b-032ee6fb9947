import TanClientCredentials from '../entity/tan-client-credentials.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
export declare class TanDashboardService {
    getFormsUdinAnalytics(userId: number, assesmentYear: any): Promise<{
        totalForms: number;
        originalForms: {
            totalOriginalForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
        revisedFroms: {
            totalRevisedForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
        notApplicableForms: {
            totalNotApplicableForms: number;
            udinCompleted: number;
            udinPending: number;
            udinNotApplicable: number;
        };
    }>;
    incometaxClientCheck(userId: any, queryy: any): Promise<{
        filteredRows: TanClientCredentials[];
        totalClients: number;
        count: number;
        uniquePansCount: number;
        totalCount: number;
        totalClientsWithTraces: number;
        traceCount: number;
        totalTracesCount: number;
        totalData: TanClientCredentials[];
    }>;
    exportTanInvalid(userId: number, query: any): Promise<any>;
    getIncometaxConfigStatus(userId: any, query: Date): Promise<{
        totalLimit: any;
        difference: number;
        presentClients: number;
    }>;
    getFormsAnalytics(userId: number, financialYear: any): Promise<{}>;
    getFormsNavigateAnalytics(userId: number, query: any): Promise<{
        totalRecords: number;
        data: any[];
    }>;
    getFormsCorrectionAnalytics(userId: number, financialYear: any): Promise<{}>;
    getExcelNoticeDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'notice_sent_date', query: any, ViewAll: any, ViewAssigned: any, userId: any): Promise<number>;
    getExcelNoticeFyaResponseDueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'date_of_compliance', query: any, ViewAll: any, ViewAssigned: any, userId: any): Promise<number>;
    getExcelNoticeFyiDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'notice_sent_date', query: any, ViewAll: any, ViewAssigned: any, userId: any): Promise<number>;
    getExcelNoticeFyiResponseDueDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'date_of_compliance', query: any, ViewAll: any, ViewAssigned: any, userId: any): Promise<number>;
    getExcelCombinedNoticesCount(userId: number, query: any): Promise<{
        issueData: {
            last1WeekIssued: number;
            last15DaysIssued: number;
            last1MonthIssued: number;
            todayIssued: number;
        };
        responseDueData: {
            last1WeekDue: number;
            last15DaysDue: number;
            last1MonthDue: number;
            todayDue: number;
        };
    }>;
    getTraceNoticeDates(organizationId: number, interval: '1week' | '15days' | '1month' | '1year' | 'today', dateColumn: 'date', query: any, ViewAll: any, ViewAssigned: any, userId: any): Promise<number>;
    tracesNotice(userId: number, query: any): Promise<{
        issueData: {
            last1WeekIssued: number;
            last15DaysIssued: number;
            last1MonthIssued: number;
            todayIssued: number;
        };
    }>;
    getExcelFyaEvents(userId: any, query: Date): Promise<{
        type: string;
        id: number;
        clientId: number;
        client: Client;
        organizationId: number;
        tanClientCredentialsId: number;
        proceedingName: string;
        pan: string;
        ay: string;
        proceedingLimitationDate: string;
        proceedingStatus: string;
        proceedingConcludedDate: string;
        noticeDin: string;
        noticeSentDate: string;
        noticeSection: string;
        dateOfCompliance: string;
        dateResponseSubmitted: string;
        createdAt: Date;
        updatedAt: Date;
        uuid: string;
    }[]>;
    getExcelResponseDueEvents(userId: any, query: Date): Promise<{
        type: string;
        id: number;
        clientId: number;
        client: Client;
        organizationId: number;
        tanClientCredentialsId: number;
        proceedingName: string;
        pan: string;
        ay: string;
        proceedingLimitationDate: string;
        proceedingStatus: string;
        proceedingConcludedDate: string;
        noticeDin: string;
        noticeSentDate: string;
        noticeSection: string;
        dateOfCompliance: string;
        dateResponseSubmitted: string;
        createdAt: Date;
        updatedAt: Date;
        uuid: string;
    }[]>;
    getTracesEvents(userId: any, query: Date): Promise<{
        type: string;
        id: number;
        commRefNo: string;
        commCat: string;
        fy: string;
        hidFy: string;
        qt: string;
        hidQt: string;
        formType: string;
        date: string;
        isRead: string;
        description: string;
        commMstrId: string;
        commInbId: string;
        category: string;
        comCatId: string;
        certNum: string;
        createdAt: string;
        updatedAt: string;
        organizationId: number;
        clientId: number;
        client: Client;
        tanClientCredentials: TanClientCredentials;
    }[]>;
    incometaxTanClientCheck(userId: any, queryy: any): Promise<{
        filteredRows: AutomationMachines[];
        totalClients: number;
        count: number;
        uniquePansCount: number;
        totalCount: number;
    }>;
    tracesClientCheck(userId: any, queryy: any): Promise<{
        uniquePansCount: number;
        filteredRows: AutomationMachines[];
        totalClientsWithTraces: number;
        totalCount: number;
    }>;
}
